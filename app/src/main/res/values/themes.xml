<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="GroupPreference" parent="AppTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    </style>

    <style name="AppThemeNoneTranslucent" parent="@style/Theme.COUI">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    </style>
    <!-- Application overlay theme. -->
    <style name="AppOverlayTheme" parent="AppTheme">
        <item name="couiIsSplitHideWithActionBar">true</item>
    </style>
</resources>