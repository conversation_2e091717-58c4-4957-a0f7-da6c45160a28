/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : MultiFileObserverTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/18
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/18, LI Kun, create
 ************************************************************/

package oplus.multimedia.soundrecorder.fileobserve;

import android.os.Build;
import android.os.FileObserver;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.fileobserve.MultiFileObserver;
import com.soundrecorder.common.fileobserve.OnFileEventListener;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.io.File;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;

import oplus.multimedia.soundrecorder.shadows.ShadowOS12FeatureUtil;
import oplus.multimedia.soundrecorder.shadows.ShadowFeatureOption;
import oplus.multimedia.soundrecorder.shadows.ShadowRecorderUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowRecorderUtil.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class MultiFileObserverTest {
    private static final String TEST_PATH = "test/path";
    private static final String M_FILE_OBSERVERS = "mFileObservers";
    private static final String M_LISTENERS = "mListeners";

    @Test
    public void should_mapIsNotEmpty_when_addPath_withPathNotNull() {
        MultiFileObserver multiFileObserver = MultiFileObserver.getInstance();
        ConcurrentHashMap<String, FileObserver> fileObservers = Whitebox.getInternalState(multiFileObserver, M_FILE_OBSERVERS);
        Assert.assertTrue(fileObservers.isEmpty());
        File testFile = new File(TEST_PATH);
        if(!testFile.exists()){
            testFile.mkdirs();
        }
        multiFileObserver.addPath(TEST_PATH);
        Assert.assertFalse(fileObservers.isEmpty());
        Assert.assertTrue(fileObservers.containsKey(TEST_PATH));
        testFile.deleteOnExit();
    }

    @Test
    public void should_mapIsEmpty_when_addPath_withPathNull() {
        MultiFileObserver multiFileObserver = MultiFileObserver.getInstance();
        ConcurrentHashMap<String, FileObserver> fileObservers = Whitebox.getInternalState(multiFileObserver, M_FILE_OBSERVERS);
        Assert.assertTrue(fileObservers.isEmpty());
        multiFileObserver.addPath(null);
        Assert.assertTrue(fileObservers.isEmpty());
    }

    @Test
    public void should_removeFileEventListener_not_remove_when_null() {
        MultiFileObserver multiFileObserver = MultiFileObserver.getInstance();
        ArrayList<OnFileEventListener> eventListeners = Whitebox.getInternalState(multiFileObserver, M_LISTENERS);
        OnFileEventListener addListener = (event, path, allPath) -> {
            //do nothing;
        };
        multiFileObserver.addFileEventListener(addListener);
        Assert.assertEquals(1, eventListeners.size());
        multiFileObserver.removeFileEventListener(null);
        Assert.assertEquals(1, eventListeners.size());

        multiFileObserver.removeFileEventListener(addListener);
    }


    @Test
    public void should_addFileEventListener_not_add_when_listener_null() {
        MultiFileObserver multiFileObserver = MultiFileObserver.getInstance();
        ArrayList<OnFileEventListener> eventListeners = Whitebox.getInternalState(multiFileObserver, M_LISTENERS);
        multiFileObserver.addFileEventListener((event, path, allPath) -> {
            //do nothing;
        });
        Assert.assertEquals(1, eventListeners.size());
        multiFileObserver.addFileEventListener(null);
        Assert.assertEquals(1, eventListeners.size());
    }

    @Test
    public void should_mapIsEmpty_when_clear() {
        MultiFileObserver multiFileObserver = MultiFileObserver.getInstance();
        ConcurrentHashMap<String, FileObserver> fileObservers = Whitebox.getInternalState(multiFileObserver, M_FILE_OBSERVERS);
        Assert.assertTrue(fileObservers.isEmpty());
        File testFile = new File(TEST_PATH);
        if(!testFile.exists()){
            testFile.mkdirs();
        }
        multiFileObserver.addPath(TEST_PATH);
        Assert.assertTrue(fileObservers.size() > 0);
        multiFileObserver.clear();
        Assert.assertTrue(fileObservers.isEmpty());
        testFile.deleteOnExit();
    }


}
