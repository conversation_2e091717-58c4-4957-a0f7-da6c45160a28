package oplus.multimedia.soundrecorder.shadows;

import com.soundrecorder.base.utils.OplusCompactUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/6/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
@Implements(OplusCompactUtil.class)
public class ShadowOplusCompactUtil {

    @Implementation
    public static boolean isOver11dot3() {
        return true;
    }
}
