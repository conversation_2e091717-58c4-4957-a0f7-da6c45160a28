/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderApplicationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder

import android.content.res.Configuration
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.cloudkit.oldcompat.CloudPermissionActivity
import com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import io.mockk.every
import io.mockk.mockk
import oplus.multimedia.soundrecorder.shadows.ShadowFeatureOption
import oplus.multimedia.soundrecorder.shadows.ShadowOS12FeatureUtil
import oplus.multimedia.soundrecorder.slidebar.TransparentActivity
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class RecorderApplicationTest {

    private val cloudTipManagerAction = mockk<CloudTipManagerAction>()

    private val koinApp = koinApplication {
        modules(module {
            single { cloudTipManagerAction }
        })
    }

    @Before
    fun setup(){
        startKoin(koinApp)
    }

    @After
    fun release() {
        stopKoin()
    }

    @Test
    fun should_success_when_onConfigurationChanged() {
        val application = RecorderApplication()
        Whitebox.setInternalState(application, "densityDpi", 120)
        val newConfig = Configuration().also {
            it.densityDpi = 240
            it.locale = Locale.getDefault()
        }
        application.onConfigurationChanged(newConfig)
        Assert.assertEquals(240, Whitebox.getInternalState(application, "densityDpi"))
    }

    @Test
    fun should_success_when_onDestroyedRelease() {
        val application = RecorderApplication()
        val mockedPermissionUtils = Mockito.mockStatic(PermissionUtils::class.java)
        Mockito.`when`(PermissionUtils.hasReadAudioPermission(any())).thenReturn(true)
        Mockito.`when`(PermissionUtils.getNextAction())
            .thenReturn(PermissionUtils.SHOULD_REQUEST_PERMISSIONS)
        every { cloudTipManagerAction.getCloudPermissionActivityName() } returns CloudPermissionActivity::class.java.name
        application.onDestroyedRelease(CloudPermissionActivity())
        every { cloudTipManagerAction.getRecordCloudSettingActivityName() } returns SettingRecordSyncActivity::class.java.name
        application.onDestroyedRelease(SettingRecordSyncActivity())
        application.onDestroyedRelease(TransparentActivity())
        application.onDestroyedRelease(BrowseFile())
        val fileObserver = MultiFileObserver.getInstance()
        Assert.assertEquals(
            0,
            Whitebox.getInternalState<ConcurrentHashMap<String, MultiFileObserver.FileObserverWrapper>>(
                fileObserver,
                "mFileObservers"
            ).size
        )
        mockedPermissionUtils.close()
    }
}