/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IntentExtTest
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.soundrecorder.base.ext

import android.content.Intent
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.ext.IntentExt.getBooleanValue
import com.soundrecorder.base.ext.IntentExt.getIntValue
import com.soundrecorder.base.ext.IntentExt.getLongValue
import com.soundrecorder.base.ext.IntentExt.getStringValue
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class IntentExtTest {

    @Test
    fun should_return_correct_when_getStringValue() {
        val intent = Intent()
        Assert.assertNull(intent.getStringValue("key"))
        Assert.assertEquals("test", intent.getStringValue("key", "test"))
        intent.putExtra("key", "value")
        Assert.assertEquals("value", intent.getStringValue("key", "test"))
    }

    @Test
    fun should_return_correct_when_getBooleanValue() {
        val intent = Intent()
        Assert.assertFalse(intent.getBooleanValue("key"))
        Assert.assertTrue(intent.getBooleanValue("key", true))
        intent.putExtra("key", false)
        Assert.assertFalse(intent.getBooleanValue("key", true))
    }

    @Test
    fun should_return_correct_when_getIntValue() {
        val intent = Intent()
        Assert.assertEquals(0, intent.getIntValue("key", 0))
        intent.putExtra("key", 1)
        Assert.assertEquals(1, intent.getIntValue("key", 0))
    }

    @Test
    fun should_return_correct_when_getLongValue() {
        val intent = Intent()
        Assert.assertEquals(0L, intent.getLongValue("key", 0L))
        intent.putExtra("key", 1L)
        Assert.assertEquals(1L, intent.getLongValue("key", 0L))
    }
}