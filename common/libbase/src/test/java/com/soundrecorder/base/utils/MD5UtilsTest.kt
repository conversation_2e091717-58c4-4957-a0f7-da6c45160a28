/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MD5UtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.net.Uri
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.io.File
import java.util.Locale

@RunWith(AndroidJUnit4::class)
@PrepareForTest(Locale::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MD5UtilsTest {

    @Test
    fun should_equals_when_calMd5() {
        val md5 = MD5Utils.calcMd5(null)
        Assert.assertNull(md5)
    }

    @Test
    fun should_equals_when_calMd5_other() {
        var md5 = Whitebox.invokeMethod<String>(MD5Utils::class.java, "calcMd5", "", null)
        Assert.assertEquals("d41d8cd98f00b204e9800998ecf8427e", md5)

        md5 = Whitebox.invokeMethod<String>(MD5Utils::class.java, "calcMd5", "123", null)
        Assert.assertEquals("202cb962ac59075b964b07152d234b70", md5)
    }

    @Test
    fun should_equals_when_calMd5_getMD5() {
        val mockedFile = File("sdCard/123.txt")
        var md5 = MD5Utils.getMD5(mockedFile)
        Assert.assertEquals("", md5)

        val mockedUri = Uri.EMPTY
        md5 = MD5Utils.getMD5(mockedUri)
        Assert.assertEquals("d41d8cd98f00b204e9800998ecf8427e", md5)
    }
}