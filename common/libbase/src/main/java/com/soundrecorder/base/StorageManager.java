/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.base;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.StatFs;
import android.preference.PreferenceManager;

import androidx.annotation.Nullable;

import com.soundrecorder.base.utils.AddonAdapterCompatUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.ToastManager;

import java.util.HashMap;

public class StorageManager {
    public static final String TAG = "StorageManager";
    public static final boolean DEBUG = true;
    public static final int MIN_SYSTEM_VALID_SPACE = 30;
    public static final int SWITCH_TO_SDCARD_STORAGE_MODE = 0; // for switch
    // storage mode
    public static final int SWITCH_TO_PHONE_STORAGE_MODE = 1;
    public static final String KEY_STORAGE_SET = "storage_switch_set";
    public static final int ENOUGH_SPACE = 0;
    public static final int PHONE_NO_SPACE = 1;
    public static final int SD_NO_SPACE = 2;
    public static final int PHONE_SD_NO_SPACE = 3;

    private static final int MSG_SHOW_HINT = 1;

    private static StorageManager sInstance;
    private static SharedPreferences sPref;
    private final Context mContext;
    private final Handler mHandMessage = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            DebugUtil.v(TAG, "mHandMessage msg what = " + msg.what + " thread = " + Thread.currentThread());
            switch (msg.what) {
                case MSG_SHOW_HINT:
                    showStorageToast(getStorageStatus());
                    break;
                default:
                    break;
            }
        }
    };

    public String mStoragePrefix = null;

    private int mStorageStatus = ENOUGH_SPACE;
    private boolean mIsCmcc = false;

    private StorageManager(Context context) {
        mContext = context;
        mIsCmcc = FeatureOption.OPLUS_NEW_SOUND_RECORDER_SETTING;
    }

    public static synchronized StorageManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new StorageManager(context.getApplicationContext());
        }
        return sInstance;
    }

    public static void setStringPref(Context context, String name, String value) {
        DebugUtil.v(TAG, "setStringPref, name=" + name + " value=" + value);
        if (!checkInitPref(context)) {
            return;
        }
        Editor ed = sPref.edit();
        ed.putString(name, value);
        ed.commit();
    }

    public static String getStringPref(Context context, String name, String def) {
        if (!checkInitPref(context)) {
            return def;
        }
        return sPref.getString(name, def);
    }

    public static void setIntPref(Context context, String name, int value) {
        DebugUtil.v(TAG, "setIntPref, name=" + name + " value=" + value);
        if (!checkInitPref(context)) {
            return;
        }
        Editor ed = sPref.edit();
        ed.putInt(name, value);
        ed.commit();
    }

    public static void setIntPrefApply(Context context, String name, int value) {
        DebugUtil.v(TAG, "setIntPrefApply, name=" + name + " value=" + value);
        if (!checkInitPref(context)) {
            return;
        }
        Editor ed = sPref.edit();
        ed.putInt(name, value);
        ed.apply();
    }

    public static int getIntPref(Context context, String name, int def) {
        if (!checkInitPref(context)) {
            return def;
        }
        return sPref.getInt(name, def);
    }

    /**
     * 判断是否有对应的key
     *
     * @param context
     * @param name
     * @return
     */
    public static boolean containsKey(Context context, String name) {
        if (!checkInitPref(context)) {
            return false;
        }
        return sPref.contains(name);
    }

    public static void setBooleanPref(Context context, String name, boolean value) {
        if (!checkInitPref(context)) {
            return;
        }
        Editor ed = sPref.edit();
        ed.putBoolean(name, value);
        ed.commit();
    }

    public static boolean getBooleanPref(Context context, String name, boolean def) {
        if (!checkInitPref(context)) {
            return false;
        }
        return sPref.getBoolean(name, def);
    }

    private static boolean checkInitPref(Context context) {
        if (context == null) {
            return false;
        }
        if (sPref == null) {
            sPref = PreferenceManager.getDefaultSharedPreferences(context);
        }
        return sPref != null;
    }

    public static boolean isInternalMounted(Context context) {
        String internalPath = AddonAdapterCompatUtil.getInternalPath(context);
        if ((null == internalPath) || internalPath.isEmpty()) {
            return false;
        }
        return AddonAdapterCompatUtil.isVolumeMounted(context, internalPath);
    }

    public static boolean isExternalMounted(Context context) {
        String externalPath = AddonAdapterCompatUtil.getExternalPath(context);
        if ((null == externalPath) || externalPath.isEmpty()) {
            return false;
        }
        return AddonAdapterCompatUtil.isVolumeMounted(context, externalPath);
    }

    public static String getStoragekey(Context context) {
        return context.getString(R.string.key_storage);
    }

    public int getStorageStatus() {
        return mStorageStatus;
    }

    /**
     * 根据status弹出toast
     * 在平板项目时，当status是PHONE_NO_SPACE或者SD_NO_SPACE，统一拦截提示为：tablet_disk_full
     * @param status
     */
    private void showStorageToast(int status) {
        switch (status) {
            case PHONE_NO_SPACE:
                ToastManager.showShortToast(mContext, FeatureOption.IS_PAD ? R.string.device_disk_full : R.string.savefile_switch_to_externalpath_hint);
                break;
            case SD_NO_SPACE:
                ToastManager.showShortToast(mContext, FeatureOption.IS_PAD ? R.string.device_disk_full : R.string.savefile_switch_to_internalpath_hint);
                break;
            case PHONE_SD_NO_SPACE:
                ToastManager.showShortToast(mContext, FeatureOption.IS_PAD ? R.string.device_disk_full : R.string.disk_full);
                break;
            default:
                break;
        }
    }

    public void setStoragePrefix(Context context, boolean autoflag) {
        HashMap<String, String> mStoragePrefixMap = null;
        String externalPathPrefix = null;
        String internalPathPrefix = null;
        String[] storagePosArray = context.getResources().getStringArray(R.array.pref_storage_values_with_sdcard);
        mStoragePrefixMap = new HashMap<String, String>();
        if (AddonAdapterCompatUtil.getExternalSdDirectory(context) != null) {
            externalPathPrefix = AddonAdapterCompatUtil.getExternalSdDirectory(context).getPath();
        } else {
            externalPathPrefix = AddonAdapterCompatUtil.getExternalStorageDirectory().getPath();
        }

        if (AddonAdapterCompatUtil.getInternalSdDirectory(context) != null) {
            internalPathPrefix = AddonAdapterCompatUtil.getInternalSdDirectory(context).getPath();
        }

        mStoragePrefixMap.put(storagePosArray[0], internalPathPrefix);
        mStoragePrefixMap.put(storagePosArray[1], externalPathPrefix);
        String storageKey = getStringPref(context, getStoragekey(context), null);
        if (!mIsCmcc) {
            storageKey = null;
        }
        DebugUtil.v(TAG, "storageKey:" + storageKey + " autoflag:" + autoflag);
        if (storageKey == null) {
            mStoragePrefix = getInternalPath(context);
            setStringPref(context, getStoragekey(context), storagePosArray[0]);
        } else {
            if (storageKey.equals(storagePosArray[1])) {
                if (!isExternalMounted(context)) {
                    mStoragePrefix = getInternalPath(context);
                } else {
                    mStoragePrefix = getExternalPath(context);
                }
            } else {
                mStoragePrefix = getInternalPath(context);
            }
        }
    }

    public String getStoragePrefix() {
        if (mStoragePrefix == null) {
            if (isExternalMounted(mContext)) {
                String[] storagePosArray = mContext.getResources().getStringArray(R.array.pref_storage_values_with_sdcard);
                String storageKey = getStringPref(mContext, getStoragekey(mContext), null);
                if (!mIsCmcc) {
                    storageKey = null;
                }
                DebugUtil.v(TAG, "storageKey:" + storageKey + " storagePosArray[0]:" + storagePosArray[0]);
                if (storageKey == null) {
                    mStoragePrefix = getInternalPath(mContext);
                } else {
                    if (storageKey.equals(storagePosArray[0])) {
                        mStoragePrefix = getInternalPath(mContext);
                    } else {
                        mStoragePrefix = getExternalPath(mContext);
                    }
                }
            } else {
                mStoragePrefix = getInternalPath(mContext);
            }
        }
        DebugUtil.v(TAG, "getStoragePrefix :" + mStoragePrefix);
        return mStoragePrefix;
    }

    public boolean checkStorageSpaceForRecordFile(boolean showToast) {
        String[] storagePosArray = mContext.getResources().getStringArray(R.array.pref_storage_values_with_sdcard);
        String storageKey = getStringPref(mContext, getStoragekey(mContext), null);
        if (!mIsCmcc) {
            storageKey = storagePosArray[0];
        }
        if (isMultiStorage(mContext)) {
            if (!isSystemAvaiableSpace(mContext, MIN_SYSTEM_VALID_SPACE)) {
                mStorageStatus = PHONE_SD_NO_SPACE;
                if (showToast) {
                    mHandMessage.removeMessages(MSG_SHOW_HINT);
                    mHandMessage.sendEmptyMessage(MSG_SHOW_HINT);
                }
                return false;
            }

            if ((storageKey != null) && storageKey.startsWith(storagePosArray[0])
                    && !isInternalAvaiableSpace(mContext, MIN_SYSTEM_VALID_SPACE)) {
                setStringPref(mContext, getStoragekey(mContext), storagePosArray[1]);
                setBooleanPref(mContext, KEY_STORAGE_SET, true);
                mStoragePrefix = getExternalPath(mContext);
                mStorageStatus = PHONE_NO_SPACE;
                if (showToast) {
                    mHandMessage.removeMessages(MSG_SHOW_HINT);
                    mHandMessage.sendEmptyMessage(MSG_SHOW_HINT);
                }
                return false;
            }

            if ((storageKey != null) && storageKey.startsWith(storagePosArray[1])
                    && !isExternalAvaiableSpace(mContext, MIN_SYSTEM_VALID_SPACE)) {
                setStringPref(mContext, getStoragekey(mContext), storagePosArray[0]);
                setBooleanPref(mContext, KEY_STORAGE_SET, false);
                mStoragePrefix = getInternalPath(mContext);
                mStorageStatus = SD_NO_SPACE;
                if (showToast) {
                    mHandMessage.removeMessages(MSG_SHOW_HINT);
                    mHandMessage.sendEmptyMessage(MSG_SHOW_HINT);
                }
                return false;
            }
        } else {
            if ((storageKey != null) && storageKey.startsWith(storagePosArray[0])
                    && !isInternalAvaiableSpace(mContext, MIN_SYSTEM_VALID_SPACE)) {
                setStringPref(mContext, getStoragekey(mContext), storagePosArray[0]);
                setBooleanPref(mContext, KEY_STORAGE_SET, false);
                mStoragePrefix = getInternalPath(mContext);
                mStorageStatus = PHONE_SD_NO_SPACE;
                if (showToast) {
                    mHandMessage.removeMessages(MSG_SHOW_HINT);
                    mHandMessage.sendEmptyMessage(MSG_SHOW_HINT);
                }
                return false;
            }
        }
        return true;
    }

    public boolean isMultiStorage(Context context) {
        boolean result = false;
        String internalPath = AddonAdapterCompatUtil.getInternalPath(context);
        String externalPath = AddonAdapterCompatUtil.getExternalPath(context);
        if ((internalPath != null) && !internalPath.isEmpty() && isInternalMounted(context) && (externalPath != null)
                && !externalPath.isEmpty() && isExternalMounted(context)) {
            if (!internalPath.equals(externalPath)) {
                result = true;
            }
        }
        return result;
    }

    public String getInternalPath(Context context) {
        return AddonAdapterCompatUtil.getInternalPath(context);
    }

    @Nullable
    public String getExternalPath(Context context) {
        return AddonAdapterCompatUtil.getExternalPath(context);
    }

    public boolean isInternalAvaiableSpace(Context context, long sizeMb) {
        if (isInternalMounted(context)) {
            String sdcard = getInternalPath(context);
            StatFs statFs = new StatFs(sdcard);
            long blockSize = statFs.getBlockSize();
            long blocks = statFs.getAvailableBlocks();
            long availableSpare = (blocks * blockSize) / (1024 * 1024);
            DebugUtil.v(TAG, "isInternalAvaiableSpace, availableSpare=" + availableSpare + ", sizeMb is " + sizeMb);
            if (sizeMb <= availableSpare) {
                return true;
            }
        }
        return false;
    }

    public boolean isExternalAvaiableSpace(Context context, long sizeMb) {
        if (isMultiStorage(context)) {
            String sdcard = getExternalPath(context);
            StatFs statFs = new StatFs(sdcard);
            long blockSize = statFs.getBlockSize();
            long blocks = statFs.getAvailableBlocks();
            long availableSpare = (blocks * blockSize) / (1024 * 1024);
            DebugUtil.v(TAG, "isExternalAvaiableSpace, availableSpare=" + availableSpare + ", sizeMb is " + sizeMb);
            if (sizeMb <= availableSpare) {
                return true;
            }
        }
        return false;
    }

    public boolean isSystemAvaiableSpace(Context context, long sizeMb) {
        return isInternalAvaiableSpace(context, sizeMb) || isExternalAvaiableSpace(context, sizeMb);
    }
}
