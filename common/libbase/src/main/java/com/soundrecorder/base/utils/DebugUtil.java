/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  DebugUtil.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 2009/12/1
 * * Author      : zhanghr
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils;

import android.text.TextUtils;
import com.soundrecorder.base.BaseApplication;
import com.oplus.recorderlog.log.RecorderLogger;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DebugUtil {

    public static final Pattern GARBLE_PATTERN = Pattern.compile("(:\\\"[^\\\"]*\\\")");

    /**
     * Priority constant for the println method.
     */

    public static void v(String tag, String msg) {
        v(tag, msg, false);
    }


    public static void v(String tag, String msg, Boolean saveInXLog) {
        RecorderLogger.v(tag, msg, saveInXLog);
    }

    public static void d(String tag, String msg) {
        d(tag, msg, false);
    }

    public static void d(String tag, String msg, Boolean saveInXLog) {
        RecorderLogger.d(tag, msg, saveInXLog);
    }


    public static void i(String tag, String msg) {
        i(tag, msg, false);
    }

    public static void i(String tag, String msg, Boolean saveInXLog) {
        RecorderLogger.i(tag, msg, saveInXLog);
    }

    public static void w(String tag, String msg) {
        w(tag, msg, true);
    }

    public static void w(String tag, String msg, Boolean saveInXLog) {
        RecorderLogger.w(tag, msg, saveInXLog);
    }

    public static void e(String tag, String msg) {
        e(tag, msg, true);
    }

    public static void e(String tag, String msg, Boolean saveInXLog) {
        RecorderLogger.e(tag, msg, saveInXLog);
    }


    public static void e(String tag, String msg, Throwable throwable) {
        e(tag, msg, throwable, true);
    }


    public static void e(String tag, String msg, Throwable throwable, Boolean saveInXLog) {
        RecorderLogger.e(tag, msg, throwable, saveInXLog);
    }

    public static boolean isLogOpen() {
        return RecorderLogger.isLogOpen();
    }

    public static void aSyncPrintDbAndFlushLog() {
        Thread thread = new Thread() {
            @Override
            public void run() {
                //ProcessDBPrint 中已经存在flushLog，不需要单独FlushLog了
                RecorderLogger.INSTANCE.processDbPrint(BaseApplication.getAppContext());
            }
        };
        thread.start();
    }


    public static void log(String tag, String msg) {
        i(tag, msg);
    }

    public static String logGarbleMiddle(final String input) {
        if (TextUtils.isEmpty(input) || (input.length() < 2)) {
            return input;
        }
        final int three = 3;
        final int four = 4;
        final int seven = 7;
        final int len = input.length();
        StringBuilder rtn = new StringBuilder();

        if (len > seven) {
            return rtn.append(input.substring(0, three))
                    .append("****")
                    .append(input.substring(len - four))
                    .toString();
        } else if (len > 2) {
            return rtn.append(input.substring(0, 1))
                    .append("****")
                    .append(input.substring(len - 1))
                    .toString();
        } else {
            return rtn.append(input.substring(0, 1))
                    .append("*")
                    .toString();
        }
    }

    public static String logGarbleEnd(final String input) {
        if (TextUtils.isEmpty(input)) {
            return input;
        }

        final int len = input.length();
        StringBuilder rtn = new StringBuilder();
        if (len > 2) {
            return rtn.append(input.substring(0, 2))
                    .append("**")
                    .toString();
        } else {
            return rtn.append(input.substring(0, 1))
                    .append("**")
                    .toString();
        }
    }

    public static String jsonStringLogGarble(String input) {
        if (TextUtils.isEmpty(input)) {
            return input;
        }

        final int three = 3;
        String rtn = input;

        Matcher matcher = GARBLE_PATTERN.matcher(input);
        String matched = "";

        while (matcher.find()) {
            matched = matcher.group().trim();

            //matched value may be empty
            if (TextUtils.isEmpty(matched) || (matched.length() < three)) {
                continue;
            }

            matched = matched.substring(2, matched.length() - 1);
            rtn = rtn.replace(matched, logGarbleMiddle(matched));
        }
        return rtn;
    }

    public static String recordExceptionMessage(Exception excetpion) {
        StringWriter writer = new StringWriter();
        excetpion.printStackTrace(new PrintWriter(writer, true));
        String message = writer.toString();
        return TextUtils.isEmpty(message) ? "暂无有效信息" : message;
    }
}
