/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SearchUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

object SearchUtil {

    @JvmStatic
    fun binSearch(srcArray: ArrayList<String>, key: Long): Int {
        var mid = 0
        var start = 0
        var end = srcArray.size - 1
        while (start <= end) {
            mid = (end - start) / 2 + start
            val time = srcArray[mid].toLong()
            if (key < time) {
                end = mid - 1
            } else if (key > time) {
                start = mid + 1
            } else {
                return mid
            }
        }
        return -1
    }
}