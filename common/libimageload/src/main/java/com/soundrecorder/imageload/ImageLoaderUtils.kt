package com.soundrecorder.imageload

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.core.graphics.drawable.toBitmap
import coil.*
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.request.ImageRequest
import coil.size.OriginalSize
import coil.transform.RoundedCornersTransformation
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.dp2px

object ImageLoaderUtils {
    private const val TAG = "ImageLoaderUtils"

    private const val MEM_CACHE_PERCENTAGE: Double = 0.05

    private val imageLoaderFactory: ImageLoaderFactory = ImageLoaderFactory {
        DebugUtil.i(TAG, "newImageLoader confige")
        val builder: ImageLoader.Builder = ImageLoader.Builder(context = BaseApplication.getAppContext())
        builder.crossfade(false)
            //这个地方加入日志用于之后调试
            //.logger(DebugLogger(Log.VERBOSE))
            .allowHardware(false)
            .allowRgb565(true)
            .networkCachePolicy(CachePolicy.DISABLED)
            .diskCachePolicy(CachePolicy.ENABLED)
            .memoryCachePolicy(CachePolicy.ENABLED)
            .bitmapConfig(Bitmap.Config.RGB_565)
            //这个地方定义MemoryCache的相比App能够使用的最大内存的比例，默认为0.2，这里人为设置为0.05，设定memoryCache的上限
            .memoryCache(MemoryCache.Builder(BaseApplication.getAppContext()).maxSizePercent(MEM_CACHE_PERCENTAGE).build())
        //.bitmapPoolPercentage(0.1)
        builder.build()
    }


    init {
        Coil.setImageLoader(imageLoaderFactory)
    }


    @JvmStatic
    fun ImageView.into(data: ImageLoadData) {
        this.dispose()
        this.load(data.src) {
            memoryCacheKey(MemoryCache.Key("$data"))
            size(data.width, data.height)
        }
    }

    @JvmStatic
    fun ImageView.intoRoundImage(data: ImageLoadData, cornersRadisInDp: Float) {
        this.dispose()
        this.load(data.src) {
            memoryCacheKey(MemoryCache.Key("$data"))
            if (data.width > 0 && data.height > 0) {
                DebugUtil.i(TAG, "into round image width ${data.width} , height ${data.height} ")
                size(data.width, data.height)
            }
            transformations(RoundedCornersTransformation(BaseApplication.getAppContext().dp2px(cornersRadisInDp.toInt())))
        }
    }

    @JvmStatic
    fun ImageView.intoBigImage(data: ImageLoadData) {
        this.dispose()
        this.load(data.src) {
            memoryCacheKey(MemoryCache.Key("$data"))
            size(OriginalSize)
        }
    }

    suspend fun executeIcon(data: ImageLoadData): Drawable? {
        return try {
            val request = ImageRequest.Builder(BaseApplication.getAppContext())
                .data(data.src)
                .memoryCacheKey(MemoryCache.Key("$data"))
                .size(data.width, data.height)
                .transformations(RoundedCornersTransformation(BaseApplication.getAppContext().dp2px(2)))
                .build()
            BaseApplication.getAppContext().imageLoader.execute(request).drawable
        } catch (e: Exception) {
            null
        }
    }


    suspend fun getScaleBitmap(data: ImageLoadData): Bitmap? {
        try {
            DebugUtil.i(TAG, "src: $data")
            val request = ImageRequest.Builder(BaseApplication.getAppContext())
                .data(data.src)
                .memoryCacheKey(MemoryCache.Key("$data"))
                .allowHardware(false)
                .size(data.width, data.height)
                .build()
            val drawable = BaseApplication.getAppContext().imageLoader.execute(request).drawable
            DebugUtil.i(TAG, "drawable: $drawable")
            return drawable?.toBitmap()
        } catch (e: Exception) {
            return null
        }
    }

    fun clearMemoryCacheByKey(data: ImageLoadData) {
        DebugUtil.e(TAG, "clearMemoryCacheByKey data:$data")
        Coil.imageLoader(BaseApplication.getAppContext()).apply {
            val key = MemoryCache.Key("$data")
//            val bitmap = memoryCache[key]
//            bitmap?.recycle()
            memoryCache?.remove(key)
        }
    }

    fun clearMemoryCache() {
        DebugUtil.e(TAG, "clearMemoryCache")
        Coil.imageLoader(BaseApplication.getAppContext()).apply {
            memoryCache?.clear()
        }
    }
}