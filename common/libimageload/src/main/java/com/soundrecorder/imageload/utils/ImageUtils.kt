package com.soundrecorder.imageload.utils

import android.graphics.Bitmap
import android.graphics.Bitmap.CompressFormat
import android.net.Uri
import androidx.annotation.WorkerThread
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream

object ImageUtils {
    private const val TAG = "ImageUtils"

    @WorkerThread
    @JvmStatic
    suspend fun Uri.uri2File(file: File): ImageParseResult? {
        val data = ImageLoadData(this, 720, 1080)
        var bm = ImageLoaderUtils.getScaleBitmap(data)
        if (bm == null || bm.isRecycled) {
            return null
        }
        if (bm.width <= 0 && bm.height <= 0) {
            bm.recycle()
            bm = null
            return null
        }
        val b = bm.toFile(file)
        val result = ImageParseResult(bm.width, bm.height)
        bm.recycle()
        ImageLoaderUtils.clearMemoryCacheByKey(data)
        return result
    }

    @JvmStatic
    private fun Bitmap.toFile(file: File): Boolean {
        var os: OutputStream? = null
        var ret = false
        try {
            os = FileOutputStream(file)
            ret = compress(CompressFormat.JPEG, 75, os)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Bitmap.toFile e=$e")
        } finally {
            try {
                os?.flush()
                os?.close()
            } catch (e: IOException) {
                DebugUtil.e(TAG, "Bitmap.toFile close e=$e")
            }
        }
        os = null
        return ret
    }
}