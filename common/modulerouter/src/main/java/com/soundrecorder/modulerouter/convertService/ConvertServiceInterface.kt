/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  ConvertServiceInterface.kt
 * * Description : ConvertServiceInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService

import android.content.Context

interface ConvertServiceInterface {

    fun getConvertSavePath(context: Context): String
    fun getConvertFileNameWithPostFix(recordId: Long, title: String): String?
    fun getConvertFilePath(context: Context, fileName: String): String?
    fun <T> writeConvertContent(filePath: String?, dataList: List<T>?)
}

object ConvertTaskStatus {
    const val ALREADY_RUNNING = 1
    const val OVER_LIMIT = 2
    const val CAN_ADD_NEW = 3
}