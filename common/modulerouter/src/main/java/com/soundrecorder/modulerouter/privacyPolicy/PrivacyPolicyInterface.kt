/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  PrivacyPolicyInterface.kt
 * * Description : PrivacyPolicyInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment

interface PrivacyPolicyInterface {
    fun newPrivacyPolicyFragment(): Fragment
    fun newPrivacyPolicyInfoFragment(type: Int): Fragment
    fun newCollectionInfoFragment(type: Int): Fragment
    fun newCollectionInfoContentFragment(title: String?, type: Int, collectionType: Int): Fragment
    fun newPrivacyPolicyDelegate(context: AppCompatActivity, type: Int, resultListener: IPrivacyPolicyResultListener?): IPrivacyPolicyDelegate
    fun newFunctionGuideDelegate(context: AppCompatActivity, functionClickOk: ((fromUserNotice: Boolean) -> Unit)?): IFunctionGuideDelegate
    fun newFunctionPrivacyDelegate(funcType: Int): IFunctionPrivacyDelegate?
}