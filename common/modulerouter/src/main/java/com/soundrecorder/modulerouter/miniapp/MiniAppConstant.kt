/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniAppConstant
 * Description:
 * Version: 1.0
 * Date: 2023/8/22
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/8/22 1.0 create
 */

package com.soundrecorder.modulerouter.miniapp

object MiniAppConstant {
    /**
     * BUNDLE_KEY 传入启动下一个页面action，
     *对应值为：
     * @see NEXT_ACTION_RECORDER_PRIVACY
     * @see NEXT_ACTION_BOOT_PRIVACY
     * @see NEXT_ACTION_APP_SETTING
     * */
    const val EXTRA_NAME_NEXT_ACTION = "NEXT_ACTION"

    /*录音个人信息保护政策*/
    const val NEXT_ACTION_RECORDER_PRIVACY = 0X1
    /*开机向导信息保护政策*/
    const val NEXT_ACTION_BOOT_PRIVACY = 0X2
    /*录音-应用详情*/
    const val NEXT_ACTION_APP_SETTING = 0X3

    const val MINI_PERMISSION_KEY = "mini_permission_key"


    /**
     * 是否需要check显示通知栏拒绝后的snack bar
     * 对应value：boolean
     */
    const val EXTRA_NAME_CHECK_SHOW_NOTIFICATION_SNACK_BAR = "check_show_notification_permission_snack_bar"

    /**
     * ONRESUME PermissionUtil.nextAction = SHOULD_SHOW_ALL_FILE_PERMISSION 无所有文件管理权限，
     * 是否能显示所有文件管理权限弹窗
     */
    const val EXTRA_NAME_SHOW_ALL_FILE_PERMISSION_RESUME = "showAllFilePermissionWhenResume"
}