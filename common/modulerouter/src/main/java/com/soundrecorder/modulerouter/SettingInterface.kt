/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SettingInterface.kt
 * * Description : SettingInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment

interface SettingInterface {
    fun launchForResult(fragment: Fragment, requestCode: Int)
    fun launchBootRegPrivacy(activity: Activity?, disableDialogFun: (dialog: AlertDialog) -> Unit)
    fun launchRecordPrivacy(activity: Activity?, type: Int)
    fun launchCollectionInfo(activity: Activity?, type: Int)
    fun launchCollectionInfoDetails(activity: Activity?, title: String?, type: Int, collectionType: Int)
    fun getPageNameInSetting(): Array<String>
    fun getNeedShowRecordModeRedDot(): Boolean
    fun setRecordModeRedDotShowed()
    fun isSupportTripartiteAudioMonitor(): Boolean
}