/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  FeedBackInterface.kt
 * * Description : FeedBackInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter

import androidx.fragment.app.FragmentActivity

interface FeedBackInterface {
    fun getFeedbackRequestData(callback: ((String?) -> Unit))
    fun launchFeedBack(activity: FragmentActivity, id: String, color: Int)
    fun setFeedbackThemeColor(color: Int)
}