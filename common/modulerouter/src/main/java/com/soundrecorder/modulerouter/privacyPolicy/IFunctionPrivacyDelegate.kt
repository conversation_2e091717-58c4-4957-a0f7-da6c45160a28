/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IFunctionPrivacyDelegate.kt
 ** Description : IFunctionPrivacyDelegate.kt
 ** Version     : 1.0
 ** Date        : 2025/06/04
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/06/04     1.0      create
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

import androidx.fragment.app.FragmentActivity

interface IFunctionPrivacyDelegate {

    /**
     * 显示功能隐私对话框 - 使用回调接口版本（推荐）
     * @param callback 授权回调接口
     */
    fun showFunctionPrivacyDialog(activity: FragmentActivity, callback: IFunctionPrivacyCallback? = null) {}

    /**
     * 关闭功能隐私对话框
     */
    fun dismissFunctionPrivacyDialog() {}
}