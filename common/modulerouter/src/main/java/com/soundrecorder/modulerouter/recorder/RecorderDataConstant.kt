/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecorderUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.recorder

object RecorderDataConstant {
    const val SHOULD_SHOW_ADD_ANIMATOR = "should_show_add_animator"
    const val SHOULD_SHOW_EXCEEDING_ALERT = "should_show_exceeding_alert"
    const val SHOULD_AUTO_FIND_SAVE_ITEM = "should_auto_find_save_item"
    const val SHOULD_AUTO_FIND_FILE_NAME = "should_auto_find_file_name"
    const val SHOULD_AUTO_FIND_FULL_PATH = "should_auto_find_full_path"
    const val SHOULD_AUTO_NEED_SMART_NAME = "should_auto_need_smart_name"

    const val SHOULD_AUTO_FIND_FILE_PATH = "should_auto_find_file_path"
    const val START_FROM_OTHER_SOURCE = "source"
    const val RECORDER_POSITION = "recorder_position"
    const val IS_NEED_RESULT = "isNeedResult"
    const val MAX_SIZE = "MAX_SIZE"
    const val RECORDER_TYPE = "recorder_type"
    const val FROM_GESTURE = "from_gesture"
    const val FROM_CALL_UI = "from_call_ui"
    const val INTENT_EXTRA_NO_ENTER_ANIMATION = "intent_no_enter_animation"
    const val REQUEST_CODE_TO_RECORDER = 10
    const val REQUEST_SHOW_ADD_ANIMATOR = 200

    const val PAGE_FROM_NAME = "launchFrom"
    const val PAGE_FROM_LAUNCHER = "launcher"
    const val PAGE_FROM_CALL = "call"
    const val PAGE_FROM_SLIDE_BAR = "slideBar"
    const val PAGE_FROM_BREENO = "breeno"
    const val PAGE_FROM_BREENO_FRONT = "xiaobuzhushou_front"
    const val PAGE_FROM_SMALL_CARD = "smallCard"
    const val PAGE_FROM_BRACKET_SPACE = "bracketSpace"
    const val PAGE_FROM_DRAGON_FLY = "service_from_app_card"
    const val PAGE_FROM_MINI_APP = "service_from_mini_app"
    const val PAGE_FROM_CUBE_BUTTON = "cubeButton"
    const val PAGE_FROM_LOCK_SCREEN = "lockScreen"

    const val ACTION_RECORDER_STOP_RECORDER = "com.oplus.soundrecorder.stopRecorder.normal"
    const val KEY_SAVE_FROM_WHERE = "key_save_from_where"
    const val ACTION_RECORDER_STOP_CANCEL = "com.oplus.soundrecorder.cancelRecorder.normal"
    const val ACTION_RECORDER_STOP_AUTO_SAVE = "com.oplus.soundrecorder.stopRecorder.autoSave"

    const val SERVICE_IS_FROM_OTHER_APP = "isFromOtherApp"
    const val SERVICE_MAX_FILE_SIZE = "record_max_file_size"
    const val SERVICE_MAX_FILE_LIMIT_BYTES = 3900L * 1024 * 1024
    const val SERVICE_MAX_DURATION = "record_max_duration"
    const val SERVICE_NEED_RESULT = "record_need_result"
    const val SERVICE_RECORD_FORMAT = "record_format"
    const val SERVICE_NEED_UPDATE_RECORDCONFIG = "need_update_recordconfig"
    const val SERVICE_NEED_UPDATE_OTHERCONFIG = "need_update_otherconfig"
    const val SERVICE_AUTO_START_RECORD = "service_auto_start_record"

    //是否需要展示snackBar。在第一次使用侧边栏拒绝通知权限后，在录制期间进入录制界面则需要展示受阻弹窗。
    const val SERVICE_NEED_SHOW_SNACKBAR = "need_show_snackbar"

    const val PLAYBACK_VIEWER_EXTRA_STATUSBAR = "status_bar"

    const val BIND_EXTRA_NEED_START_RECORD_AFTER_BIND = "need_start_record_after_bind"

    const val MSG_ARG2_SAVE_RECORD_FROM_NORMAL = 0
    const val MSG_ARG2_SAVE_RECORD_FROM_XIAO_BU = 1
    const val MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY = 2
    const val MSG_ARG2_SAVE_RECORD_FROM_APP_CARD = 3
    const val MSG_ARG2_SAVE_RECORD_FROM_SEEDLING_CARD = 4
    const val MSG_ARG2_SAVE_RECORD_FROM_NOTIFICATION = 5
    const val MSG_ARG2_SAVE_RECORD_FROM_CUBE_BUTTON = 6
    const val MSG_ARG2_SAVE_RECORD_FROM_CLIP = 7
    const val MSG_ARG2_SAVE_RECORD_FROM_LOCK_SCREEN = 8

    const val ACTION_UPDATE_CALLER_NAME_AVATAR_COLOR = "action.update.callername.avatarcolor"
    const val UPDATE_CALLER_NAME_AVATAR_COLOR_VALUE = "callername.avatarcolor.value"
    const val STOP_UPDATE_CALLER_NAME_AVATAR_COLOR = 0x1
    const val START_UPDATE_CALLER_NAME_AVATAR_COLOR = 0x2

    const val SMART_NAME_FILE_PERMISSION_ACTION = "com.oplus.soundrecorder.smartNameFilePermission"
    const val RECORD_MEDIA_ID = "media_id"
}