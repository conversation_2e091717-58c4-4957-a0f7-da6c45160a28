package com.oplus.recorderlog.log

import android.content.Context
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig

object RecorderLogger {

    private const val APP_TAG = "SoundRecorder-"
    private var localLog: ILogProcess? = null
    private var xLog: ILogProcess? = null

    init {
        localLog = LogProcessFactory.createLogProcess(LogProcessFactory.LOG_TYPE_LOCAL)
        xLog = LogProcessFactory.createLogProcess(LogProcessFactory.LOG_TYPE_X)
    }


    @JvmStatic
    fun v(tag: String?, message: String?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.v(newTag, message)
        if (needUpload) {
            xLog?.v(newTag, message)
        }
    }

    @JvmStatic
    fun d(tag: String?, message: String?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.d(newTag, message)
        if (needUpload) {
            xLog?.d(newTag, message)
        }
    }


    @JvmStatic
    fun i(tag: String?, message: String?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.i(newTag, message)
        if (needUpload) {
            xLog?.i(newTag, message)
        }
    }


    @JvmStatic
    fun w(tag: String?, message: String?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.w(newTag, message)
        if (needUpload) {
            xLog?.w(newTag, message)
        }
    }

    @JvmStatic
    fun e(tag: String?, message: String?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.e(newTag, message)
        if (needUpload) {
            xLog?.e(newTag, message)
        }
    }

    @JvmStatic
    fun e(tag: String?, message: String?, e: Throwable?, needUpload: Boolean) {
        val newTag = "$APP_TAG$tag"
        localLog?.e(newTag, message, e)
        if (needUpload) {
            xLog?.e(newTag, message, e)
        }
    }

    @JvmStatic
    fun isLogOpen(): Boolean {
        return localLog?.isLogOpen() ?: false
    }


    fun initLog(context: Context) {
        localLog?.initLog(context)
        xLog?.initLog(context)
    }

    fun flushLog(isSync: Boolean) {
        localLog?.flushLog(isSync)
        xLog?.flushLog(isSync)
    }

    fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        localLog?.processPushLog(context, cloudLogConfigMsg)
        xLog?.processPushLog(context, cloudLogConfigMsg)
    }

    fun processDbPrint(context: Context?) {
        localLog?.processDBPrint(context)
        xLog?.processDBPrint(context)
    }

    fun processManualReportLog() {
        localLog?.processManualReportLog()
        xLog?.processManualReportLog()
    }
}