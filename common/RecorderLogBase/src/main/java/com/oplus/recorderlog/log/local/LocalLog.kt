package com.oplus.recorderlog.log.local

import android.content.Context
import android.database.ContentObserver
import android.provider.Settings
import android.util.Log
import com.oplus.recorderlog.log.ILogProcess
import com.oplus.recorderlog.log.dbprint.DbPrinterImpl
import com.oplus.recorderlog.util.BaseUtil
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig

class LocalLog : ILogProcess {

    companion object {

        const val TAG = "LocalLog"

        const val VERBOSE = Log.VERBOSE

        /**
         * Priority constant for the println method; use Log.d.
         */
        const val DEBUG = Log.DEBUG

        /**
         * Priority constant for the println method; use Log.i.
         */
        const val INFO = Log.INFO

        /**
         * Priority constant for the println method; use Log.w.
         */
        const val WARN = Log.WARN

        /**
         * Priority constant for the println method; use Log.e.
         */
        const val ERROR = Log.ERROR


        //logkit中的日志开关的SystemProperties中的名称
        const val LOG_KIT_SWITCH = "log_switch_type"
    }



    //日志开关(需要外部读取SystemPropertyNative方式来确认日志是否打开)
    /**
     * try {
        sQeOff = SystemPropertiesNative.get("persist.sys.assert.panic");
        } catch (Exception e) {
        DebugUtil.e("DebugUtil", "qeOff get error", e);
        }
        sLog = ("true".equalsIgnoreCase(sQeOff));
     */
    var sLogOpen: Boolean = false
    set(value) {
        field = value
        if (value) {
            sLogLevel = VERBOSE
        }
    }
    var mDbPrinter = DbPrinterImpl(this)

    //初始日志水平, 没有开启日志开关前为WARN(只有W,E级别的日志直接打), 开启日志开关之后为 VERBOSE(v,i,d级别的日志才会打印)
    var sLogLevel: Int = WARN


    override fun v(tag: String?, message: String?) {
        if (sLogOpen) {
            if (sLogLevel <= VERBOSE) {
                Log.v(tag, message ?: "")
            }
        }
    }

    override fun d(tag: String?, message: String?) {
        if (sLogOpen) {
            if (sLogLevel <= DEBUG) {
                Log.d(tag, message ?: "")
            }
        }
    }


    override fun i(tag: String?, message: String?) {
        if (sLogOpen) {
            if (sLogLevel <= INFO) {
                Log.i(tag, message ?: "")
            }
        }
    }


    override fun w(tag: String?, message: String?) {
        if (sLogLevel <= WARN) {
            Log.w(tag, message ?: "")
        }
    }


    override fun e(tag: String?, message: String?) {
        if (sLogLevel <= ERROR) {
            Log.e(tag, message ?: "")
        }
    }



    override fun e(tag: String?, message: String?, e: Throwable?) {
        if (sLogLevel <= ERROR) {
            Log.e(tag, message, e)
        }
    }


    override fun initLog(context: Context) {
        kotlin.runCatching {
            var logSwitchOn = Settings.System.getInt(context.contentResolver, LOG_KIT_SWITCH, 0) != 0
            val isDebugApk = BaseUtil.isApkInDebug(context)
            sLogOpen = logSwitchOn || isDebugApk
            Log.e(
                TAG,
                "LocalLog init logSwitchOn $logSwitchOn, isDebugApk $isDebugApk, sLogOpen $sLogOpen"
            )
            val uri = Settings.System.getUriFor(LOG_KIT_SWITCH)
            context.contentResolver.registerContentObserver(uri, true, object : ContentObserver(null) {
                override fun onChange(selfChange: Boolean) {
                    super.onChange(selfChange)
                    logSwitchOn =
                        Settings.System.getInt(context.contentResolver, LOG_KIT_SWITCH, 0) != 0
                    sLogOpen = logSwitchOn || isDebugApk
                    Log.e(
                        TAG,
                        "LocalLog onChange logSwitchOn $logSwitchOn, isDebugApk $isDebugApk, sLogOpen $sLogOpen"
                    )
                }
            })
        }.onFailure {
            Log.e(TAG, "LocalLog initLog error", it)
        }
    }

    override fun flushLog(isSync: Boolean) {
        Log.i(TAG, "LocalLog donothing when flushLog")
    }

    override fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        Log.i(TAG, "LocalLog donothing when processPushLog")
    }

    override fun processManualReportLog() {
        Log.w(TAG, "LocalLog donothing when processManualReportLog")
    }


    override fun processDBPrint(context: Context?) {
        mDbPrinter.printMediaDb(context)
        mDbPrinter.printRecordDb(context)
    }

    override fun isLogOpen(): Boolean {
        return sLogOpen
    }
}