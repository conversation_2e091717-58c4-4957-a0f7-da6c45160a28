<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="AnimateSpeakerLayout">
        <attr name="max_padding_start" format="dimension" />
        <attr name="max_padding_end" format="dimension" />
        <attr name="max_padding_top" format="dimension" />
        <attr name="max_padding_bottom" format="dimension" />
        <attr name="background"/>
    </declare-styleable>
    <declare-styleable name="AnimateColorTextView">
        <attr name="selected_image_color" format="color" />
        <attr name="unselected_image_color" format="color" />
        <attr name="selected_text_color" format="color" />
        <attr name="unselected_text_color" format="color" />
        <attr name="animate_selected" format="boolean" />
    </declare-styleable>
    <declare-styleable name="ClickScaleStyle">
        <attr name="exclude_view_tags" format="string"/>
    </declare-styleable>

    <!-- Standard orientation constant. -->
    <attr name="orientation">
        <!-- Defines an horizontal widget. -->
        <enum name="horizontal" value="0" />
        <!-- Defines a vertical widget. -->
        <enum name="vertical" value="1" />
    </attr>

    <!-- Defines the relationship between the ViewGroup and its descendants
     when looking for a View to take focus. -->
    <attr name="descendantFocusability">
        <!-- The ViewGroup will get focus before any of its descendants. -->
        <enum name="beforeDescendants" value="0" />
        <!-- The ViewGroup will get focus only if none of its descendants want it. -->
        <enum name="afterDescendants" value="1" />
        <!-- The ViewGroup will block its descendants from receiving focus. -->
        <enum name="blocksDescendants" value="2" />
    </attr>

    <declare-styleable name="RecyclerView">
        <attr name="layoutManager" format="string" />
        <attr name="orientation" />
        <attr name="descendantFocusability" />
        <attr name="spanCount" format="integer" />
        <attr name="reverseLayout" format="boolean" />
        <attr name="stackFromEnd" format="boolean" />
    </declare-styleable>

    <declare-styleable name="OSImageView">
        <!--普通图片路径-->
        <attr name="img_draw" format="reference" />
        <!--json动画路径-->
        <attr name="anim_raw_json" format="reference" />
        <attr name="anim_raw_json_night" format="reference" />
        <attr name="anim_start_frame" format="integer" />
        <attr name="anim_end_frame" format="integer" />
        <attr name="anim_frame_duration" format="integer" />
        <attr name="anim_json_repeatCount" format="integer" />
        <attr name="anim_json_repeatMode" format="enum">
            <enum name="restart" value="1" />
            <enum name="reverse" value="2" />
        </attr>
        <!--帧率(example:60帧/s)-->
        <attr name="anim_frame_rate" format="integer" />
    </declare-styleable>

    <declare-styleable name="AnimatedCircleButton">
        <attr name="state_change" format="boolean" />
        <attr name="vibrate_toggle" format="boolean" />
        <attr name="circle_radius" format="dimension" />
        <attr name="circle_color" format="color" />
        <attr name="circle_scale" format="float" />
        <attr name="circle_gradient_start_color" format="color" />
        <attr name="circle_gradient_end_color" format="color" />
        <attr name="circle_shadow_color" format="color" />
        <attr name="circle_shadow_size" format="dimension" />
    </declare-styleable>
    <declare-styleable name="ItemBrowsePlayCustomSeekBar">
        <attr name="thumbBordersSize" format="dimension" />
        <attr name="thumbBordersColor" format="color"/>
    </declare-styleable>
    <declare-styleable name="CircleTextImage">
        <attr name="circle_background_color" format="color"/>
        <attr name="circle_text_color" format="color"/>
        <attr name="circle_text_size" format="dimension"/>
        <attr name="use_random_color" format="boolean"/>
        <attr name="sub_first_character" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="COUIAnimateTextView">
        <!--动画类型，1为此前的动画，2为新的上屏动画-->
        <attr name="couiAnimateStyle" format="integer"/>
        <!--文字起始颜色，如果couiAnimateStyle设置为2，这个字段设置无效，可以缺省-->
        <attr name="couiAnimateTextStartColor" format="color" />
        <!--文字结束颜色，如果couiAnimateStyle设置为2，这个字段设置无效，可以缺省-->
        <attr name="couiAnimateTextEndColor" format="color" />
        <!--文字最终颜色，一般和couiAnimateTextEndColor一致，建议和textColor一致
        //可以缺省，缺省就和textColor一样的颜色-->
        <attr name="couiAnimateTextStableColor" format="color" />

        <!--文字动画类型，包含Aplha、color、offsetY，分别取值1， 2， 4，一般传入7，就是所有的都要，不可缺省-->
        <attr name="couiAnimateTextType" format="integer" />

        <!--动画时长，如果如果couiAnimateStyle设置为2，则这个最小为420，小于420，则以420为准，可缺省-->
        <attr name="couiAnimateTextDuration" format="float" />
        <!--文字动画速度，默认为8ms，建议用目前的值30ms，不建议缺省-->
        <attr name="couiAnimateTextDelay" format="float" />
        <!--文字y轴offset的取值，默认10，可缺省-->
        <attr name="couiAnimateTextOffset" format="float" />
    </declare-styleable>
</resources>