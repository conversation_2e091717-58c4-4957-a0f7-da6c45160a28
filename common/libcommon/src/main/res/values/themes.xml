<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.Demo" parent="@style/Theme.COUI.Red" />

    <style name="AppBaseTheme" parent="Theme.Demo">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.
        -->
        <!-- avoid pressing two button at the same time -->
        <item name="android:windowEnableSplitTouch">false</item>
        <item name="android:splitMotionEvents">false</item>
    </style>

    <style name="AppBaseTheme.NoActionBar" parent="AppBaseTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/common_background_color</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">true</item>
    </style>

    <style name="AppBaseTheme.NoActionBar.ActionMode" parent="AppBaseTheme.NoActionBar">
        <item name="windowActionBarOverlay">true</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="actionModeStyle">@style/actionModeStyle</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <style name="AppBaseTheme.NoActionBar.ActionMode.LocalDirection" parent="AppBaseTheme.NoActionBar.ActionMode">
        <item name="android:textDirection">locale</item>
        <item name="couiSearchViewAnimateStyle">@style/customSearchViewAnimate</item>
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.COUITheme.WithToolBar</item>
    </style>

    <style name="AppBaseTheme.NoActionBar.ActionMode.LocalDirection.CardPage" parent="AppBaseTheme.NoActionBar.ActionMode.LocalDirection">
        <item name="android:windowBackground">@color/common_background_color</item>
    </style>

    <style name="actionModeStyle">
        <item name="background">?attr/couiColorBackground</item>
        <item name="height">44dp</item>
    </style>

    <style name="AppTheme" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    </style>

    <!-- Application notitle theme. -->
    <style name="AppNoTitleTheme" parent="AppTheme">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
    </style>

    <!-- 支持卡片样式的Preference列表theme, 非卡片式列表，请勿使用 -->
    <style name="AppNoTitleTheme.PreferenceFragment">
        <!-- 必须设置 -->
        <item name="android:windowBackground">?attr/couiColorBackgroundWithCard</item>
        <item name="android:navigationBarColor">?attr/couiColorBackgroundWithCard</item>
        <item name="preferenceTheme">@style/PreferenceThemeOverlay.COUITheme.WithToolBar</item>
        <!-- 必须设置 -->
    </style>

    <style name="TransparentActivityTheme" parent="@style/AppNoTitleTheme">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="background">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:focusable">false</item>
        <item name="android:clickable">false</item>
        <item name="android:forceDarkAllowed" tools:targetApi="q">true</item>
        //重写页面finish动画为空，处理外销T上授权弹窗点击后立即执行finish，导致弹窗闪烁
        <item name="android:windowAnimationStyle">@style/ActivityAnimationNoExistAnim</item>
    </style>

    <style name="ActivityAnimationNoExistAnim" parent="@style/Animation.COUI.Activity">
        <item name="android:activityCloseEnterAnimation">@null</item>
        <item name="android:activityCloseExitAnimation">@null</item>
    </style>

    <style name="CommonAppBarStyle" parent="Widget.COUI.Toolbar">
        <item name="android:background">?attr/couiColorBackgroundWithCard</item>
    </style>
</resources>