/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: MediaUtil
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import com.soundrecorder.base.utils.DebugUtil

object MediaUtil {

    const val TAG = "MediaUtil"

    @JvmStatic
    fun getPathFromNewUri(context: Context, inputUri: Uri): String? {
        var cursor: Cursor? = null
        var result: String? = null
        val queryBundle: Bundle = Bundle().apply {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                putInt(MediaStore.QUERY_ARG_MATCH_PENDING, MediaStore.MATCH_INCLUDE)
            }
        }
        val projection: Array<String> = arrayOf(MediaStore.Audio.Media.DATA)
        try {
            cursor = context.contentResolver?.query(inputUri, projection, queryBundle, null)
            if ((cursor != null) && (cursor.count > 0)) {
                cursor.moveToFirst()
                val index = cursor.getColumnIndex(MediaStore.Audio.Media.DATA)
                if (index >= 0) {
                    result = cursor.getString(index)
                }
                DebugUtil.i(TAG, "getPathFromNewUri index: $index, result: $result")
            }
        } catch (ignored: Exception) {
            Log.e(TAG, "deleteFile error", ignored)
        } finally {
            cursor?.close()
        }
        Log.i(TAG, "getPathFromNewUri Uri $inputUri, result : $result")
        return result
    }
}