/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrightScreenUtil.kt
 * * Description : 亮屏相关
 * * Version     : 1.0
 * * Date        : 2024/12/25
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import android.os.PowerManager
import com.soundrecorder.base.utils.DebugUtil

object BrightScreenUtil {

    private const val TAG = "BrightScreenUtil"
    private const val WAKE_LOCK_TIMEOUT = 60L * 1000

    @JvmStatic
    fun acquireWakeLock(context: Context, tagName: String? = null): PowerManager.WakeLock? {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as? PowerManager
            ?: return null
        val wakeLock = powerManager.newWakeLock(
            PowerManager.SCREEN_BRIGHT_WAKE_LOCK
                    or PowerManager.ACQUIRE_CAUSES_WAKEUP
                    or PowerManager.ON_AFTER_RELEASE,
            tagName ?: TAG
        )
        if (wakeLock != null) {
            wakeLock.acquire(WAKE_LOCK_TIMEOUT)
            DebugUtil.i(TAG, "wakelock aquired $wakeLock, timeout = default")
        }
        return wakeLock
    }

    @JvmStatic
    fun releaseWakeLock(wakeLock: PowerManager.WakeLock?) {
        wakeLock?.release()
        DebugUtil.i(TAG, "wakelock released $wakeLock")
    }
}