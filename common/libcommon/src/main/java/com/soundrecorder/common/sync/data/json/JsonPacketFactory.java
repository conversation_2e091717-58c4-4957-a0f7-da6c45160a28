/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: JsonPacketFactory
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author:huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/

package com.soundrecorder.common.sync.data.json;

import com.soundrecorder.common.sync.data.Packet;
import com.soundrecorder.common.sync.data.PacketArray;
import com.soundrecorder.common.sync.data.PacketFactory;

public class JsonPacketFactory implements PacketFactory {

    private volatile static JsonPacketFactory sInstance = null;

    public static JsonPacketFactory getInstance() {
        if (null == sInstance) {
            synchronized (JsonPacketFactory.class) {
                if (null == sInstance) {
                    sInstance = new JsonPacketFactory();
                }
            }
        }
        return sInstance;
    }


    @Override
    public Packet newKv() {
        return new JsonPacketObject();
    }

    @Override
    public PacketArray newKvArray() {
        return new JsonPacketArray();
    }

}
