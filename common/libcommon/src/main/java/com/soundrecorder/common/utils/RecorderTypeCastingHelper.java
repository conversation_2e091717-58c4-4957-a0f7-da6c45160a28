/********************************************************************************
 ** Copyright (C), 2008-2108, OPPO Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File: - ColorTypeCastingHelper.java
 ** Description:
 **     Helper class for type casting
 **
 ** Version: 1.0
 ** Date: 2019-07-17
 ** Author: <EMAIL>
 ** TAG: OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <data>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>      2019-07-17   1.0         Create this moudle
 ********************************************************************************/

package com.soundrecorder.common.utils;

public final class RecorderTypeCastingHelper {
    public static <T> T typeCasting(Class<T> type, Object object) {
        if (object != null && type.isInstance(object)) {
            return (T)object;
        }
        return null;
    }
}
