/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CircleTextImage.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R

class CircleTextImage : AppCompatImageView {
    private var mCircleColor = Color.RED //Default background color
    private var mCircleTextColor = Color.WHITE //Text color
    private var mCircleTextSize = DEFAULT_TEXT_SIZE //Text color
    private var mUseRandomBackgroundColor = false //Use random background color
    private var mSubFirstCharacter = false //Is whether intercept first character
    private val mPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var mPaintTextForeground: Paint? = null
    private var mPaintTextBackground: Paint? = null
    private val textSizeRatio = DEFAULT_TEXT_SIZE_RATIO
    private var mFontMetrics: Paint.FontMetrics? = null
    private var mText: String? = null
    private var mRadius = 0
    private var mCenterX = 0
    private var mCenterY = 0
    private var mCircleImageColor = 0

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initAttrs(context, attrs)
        init()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initAttrs(context, attrs)
        init()
    }

    private fun initAttrs(context: Context, attrs: AttributeSet?) {
        if (attrs == null) {
            return
        }
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CircleTextImage)
        mCircleColor =
            typedArray.getColor(R.styleable.CircleTextImage_circle_background_color, Color.RED)
        mCircleTextColor =
            typedArray.getColor(R.styleable.CircleTextImage_circle_text_color, Color.WHITE)
        mCircleTextSize = typedArray.getDimension(
            R.styleable.CircleTextImage_circle_text_size, resources.getDimensionPixelSize(
                R.dimen.sp12
            ).toFloat()
        )
        mUseRandomBackgroundColor =
            typedArray.getBoolean(R.styleable.CircleTextImage_use_random_color, false)
        mSubFirstCharacter =
            typedArray.getBoolean(R.styleable.CircleTextImage_sub_first_character, false)
        typedArray.recycle()
    }

    private fun init() {
        mPaintTextForeground = Paint()
        mPaintTextForeground?.color = mCircleTextColor
        mPaintTextForeground?.isAntiAlias = true
        mPaintTextForeground?.textAlign = Paint.Align.CENTER
        mPaintTextForeground?.textSize = mCircleTextSize
        mPaintTextBackground = Paint()
        mPaintTextBackground?.color = mCircleTextColor
        mPaintTextBackground?.isAntiAlias = true
        mPaintTextBackground?.style = Paint.Style.FILL
        mPaintTextForeground?.textSize = mCircleTextSize
        if (mUseRandomBackgroundColor) {
            //mPaint.setColor(Color.parseColor(CircleTextImageUtil.randomColor));
            mPaint.color = mCircleImageColor
        } else {
            mPaint.color = mCircleColor
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val desiredWidth = DEFAULT_DESIRED_WIDTH * 2
        val desiredHeight = DEFAULT_DESIRED_WIDTH * 2
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        val width: Int
        val height: Int
        if (null != mText && mText?.trim { it <= ' ' } != "") {
            //mPaintTextForeground.setTextSize(textSizeRatio * 2 * 100);//adapt some telphone
            mPaintTextForeground?.textSize = mCircleTextSize //adapt some telphone
            val measureTextSize = mText?.length?.let { mPaintTextForeground?.measureText(mText, 0, it) }
            var realSize = (measureTextSize ?: 0f).toInt() + REAL_SIZE_60
            if (realSize < REAL_SIZE_200) {
                realSize = DEFAULT_DESIRED_WIDTH * 2
            }

            //Measure Width
            width = if (widthMode == MeasureSpec.EXACTLY) {
                //Must be this size
                widthSize
            } else if (widthMode == MeasureSpec.AT_MOST) {
                //Can't be bigger than...
                realSize
            } else {
                //Be whatever you want
                realSize
            }

            //Measure Height
            height = if (heightMode == MeasureSpec.EXACTLY) {
                //Must be this size
                heightSize
            } else if (heightMode == MeasureSpec.AT_MOST) {
                //Can't be bigger than...
                realSize
            } else {
                //Be whatever you want
                realSize
            }
        } else {
            //Measure Width
            width = if (widthMode == MeasureSpec.EXACTLY) {
                //Must be this size
                widthSize
            } else if (widthMode == MeasureSpec.AT_MOST) {
                //Can't be bigger than...
                Math.min(desiredWidth, widthSize)
            } else {
                //Be whatever you want
                desiredWidth
            }

            //Measure Height
            height = if (heightMode == MeasureSpec.EXACTLY) {
                //Must be this size
                heightSize
            } else if (heightMode == MeasureSpec.AT_MOST) {
                //Can't be bigger than...
                Math.min(desiredHeight, heightSize)
            } else {
                //Be whatever you want
                desiredHeight
            }
        }

        //MUST CALL THIS
        setMeasuredDimension(width, height)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        //get padding
        val paddingLeft = paddingLeft
        val paddingRight = paddingRight
        val paddingTop = paddingTop
        val paddingBottom = paddingBottom
        //deal padding
        val width = width - paddingLeft - paddingRight
        val height = height - paddingTop - paddingBottom
        val radius = Math.min(width, height) / 2
        if (null != mText && mText?.trim { it <= ' ' } != "") {
            drawText(canvas)
        } else {
            canvas.drawCircle(
                (width / 2).toFloat(),
                (height / 2).toFloat(),
                radius.toFloat(),
                mPaint
            )
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val paddingLeft = paddingLeft
        val paddingTop = paddingTop
        val contentWidth = w - paddingLeft - paddingRight
        val contentHeight = h - paddingTop - paddingBottom
        mRadius = if (contentWidth < contentHeight) contentWidth / 2 else contentHeight / 2
        mCenterX = paddingLeft + mRadius
        mCenterY = paddingTop + mRadius
        refreshTextSizeConfig()
    }

    private fun drawText(canvas: Canvas) {
        canvas.drawCircle(mCenterX.toFloat(), mCenterY.toFloat(), mRadius.toFloat(), mPaint)
        mPaintTextForeground?.let { textPaint ->
            textPaint.color = mCircleTextColor
            mFontMetrics?.let {
                mText?.let { text ->
                    canvas.drawText(
                        text, 0, text.length, mCenterX.toFloat(), mCenterY + Math.abs(
                            it.top + it.bottom
                        ) / 2, textPaint
                    )
                }
            }
        }
    }

    private fun refreshTextSizeConfig() {
        //mPaintTextForeground.setTextSize(textSizeRatio * 2 * 100);
        mPaintTextForeground?.textSize = mCircleTextSize
        mFontMetrics = mPaintTextForeground?.fontMetrics
    }

    /**
     * setText for the view.
     * @param text
     */
    fun setCircleImageText(text: String?) {
        if (mSubFirstCharacter) {
            mText = text?.let { CircleTextImageUtil.subFirstCharacter(it) }
        } else {
            mText = text
        }
        invalidate()
    }

    /**
     * 设置字体大小
     */
    fun setCircleTextSize(textSize: Float) {
        mCircleTextSize = textSize
        invalidate()
    }

    /**
     * 设置圆形背景色
     */
    fun setCircleImageColor(color: String?) {
        if (color != null) {
            kotlin.runCatching {
                mCircleImageColor = Color.parseColor(color)
            }.onFailure {
                //数据库获取的色值可能有异常，为非#开头的string。
                DebugUtil.e(TAG, "Unknown color:$color")
                mCircleImageColor = Color.parseColor(CircleTextImageUtil.randomColor)
            }
            mPaint.color = mCircleImageColor
            invalidate()
        }
    }

    var circleImageColor: Int
        get() = mCircleImageColor
        set(color) {
            mCircleImageColor = color
            mPaint.color = color
            invalidate()
        }

    companion object {
        private const val TAG = "CircleTextImage"
        private const val DEFAULT_TEXT_SIZE = 14.0f
        private const val DEFAULT_TEXT_SIZE_RATIO = 0.4f
        private const val DEFAULT_DESIRED_WIDTH = 100
        private const val REAL_SIZE_200 = 200
        private const val REAL_SIZE_60 = 60
    }
}