/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FollowCOUIAlertDialog
 * Description:跟手弹窗
 * Version: 1.0
 * Date: 2023/03/15
 * Author: W9010241(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9010241 2023/03/15 1.0 create
 */

package com.soundrecorder.common.flexible

import android.content.Context
import android.content.DialogInterface
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.ViewUtils

class FollowCOUIAlertDialog(context: Context) {
    companion object {
        /**
         * 录音是否支持锚点，该开关只处理了
         * - 播放页面：转文本分享弹窗
         * - 全局系统分享弹窗
         */
        const val RECORDER_DIALOG_SUPPORT_FOLLOW = false
    }

    private var mBuilder: COUIAlertDialogBuilder? = null
    private var mDialog: AlertDialog? = null
    private var mAnchor: View? = null
    private var mSupportFollow = false

    init {
        mBuilder = COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_List_Bottom)
    }

    fun initBuilder(): COUIAlertDialogBuilder? {
        mBuilder?.setCancelable(true)
        mBuilder?.setBlurBackgroundDrawable(true)
        return mBuilder
    }

    fun setAnchorView(anchorView: View?) {
        if (RECORDER_DIALOG_SUPPORT_FOLLOW) {
            mAnchor = anchorView
        }
    }

    /**
     * 小屏,跟手 gravity 不变
     *  其他gravity 变为center
     */
    fun showDialog(supportFollow: Boolean) {
        mSupportFollow = supportFollow
        val isSmall = ScreenUtil.isSmallScreen(BaseApplication.getAppContext())
        mDialog = if (isSmall || !supportFollow) {
            mBuilder?.create()
        } else {
            mBuilder?.create(mAnchor)
        }
        mDialog?.show()
        ViewUtils.updateWindowLayoutParams(mDialog?.window)
    }

    fun showDialogDisableFollow() {
        mSupportFollow = false
        mDialog = mBuilder?.show()
        ViewUtils.updateWindowLayoutParams(mDialog?.window)
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener) {
        mDialog?.setOnDismissListener(listener)
    }

    fun getAlertDialog(): AlertDialog? {
        return mDialog
    }

    fun isDialogSupportFollow(): Boolean {
        return mSupportFollow
    }

    fun isShowing(): Boolean? {
        return mDialog?.isShowing
    }

    fun dismiss() {
        mDialog?.dismiss()
        mBuilder = null
        mDialog = null
        mAnchor = null
    }
}

fun COUIAlertDialogBuilder.setFollowNegativeButton(
    supportFollow: Boolean,
    textId: Int,
    listener: DialogInterface.OnClickListener?
) {
    if (!FollowCOUIAlertDialog.RECORDER_DIALOG_SUPPORT_FOLLOW) {
        setNegativeButton(textId, listener)
        return
    }

    if (!supportFollow || ScreenUtil.isSmallScreen(BaseApplication.getAppContext())) {
        setNegativeButton(textId, listener)
    }
}

interface FollowRestoreCallBack {
    fun restoreCallBack()
}