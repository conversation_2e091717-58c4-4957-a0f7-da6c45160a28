/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : RecorderProvider
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON><PERSON>, create
 ***********************************************************/


package com.soundrecorder.common.db;

import static com.soundrecorder.common.constant.DatabaseConstant.RecordUri.NO_NOTIFY_KEY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecordUri.NO_NOTIFY_VALUE;

import android.content.ContentProvider;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.UriMatcher;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.text.TextUtils;

import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.base.utils.DebugUtil;

public class RecorderProvider extends ContentProvider {

    private static final String TAG = "RecorderProvider";

    private static final UriMatcher URI_MATCHER = new UriMatcher(UriMatcher.NO_MATCH);
    private static final String HASHID = "mHashId";

    static {
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "records", TABLESCODE.RECORDS);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "records/#", TABLESCODE.RECORD_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "status", TABLESCODE.GLOBAL_STATUS);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "status/#", TABLESCODE.GLOBAL_STATUS_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "convert", TABLESCODE.CONVERT);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "convert/#", TABLESCODE.CONVERT_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "upload", TABLESCODE.UPLOAD);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, "upload/#", TABLESCODE.UPLOAD_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME, TABLESCODE.PICTURE_MARK);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, KeyWordDbUtils.TABLE_KEY_WORD_NAME, TABLESCODE.KEY_WORD);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, KeyWordDbUtils.TABLE_KEY_WORD_NAME + "/#", TABLESCODE.KEY_WORD_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, NoteDbUtils.TABLE_NOTE_NAME, TABLESCODE.NOTE);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_RECYCLE_BIN, TABLESCODE.RECYCLE_BIN);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_RECYCLE_BIN + "/#", TABLESCODE.RECYCLE_BIN);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO, TABLESCODE.COLLECTION_INFO);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO + "/#", TABLESCODE.KEY_COLLECTION_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_GROUP_INFO, TABLESCODE.GROUP_INFO);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_GROUP_INFO + "/#", TABLESCODE.GROUP_INFO_ID);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_SEARCH_HISTORY, TABLESCODE.SEARCH_HISTORY);
        URI_MATCHER.addURI(DatabaseConstant.AUTHORITY, DatabaseConstant.TABLE_NAME_SEARCH_HISTORY + "/#", TABLESCODE.SEARCH_HISTORY_ID);
    }

    private RecorderDatabaseHelper mDatabaseHelper;

    @Override
    public boolean onCreate() {
        mDatabaseHelper = new RecorderDatabaseHelper(getContext());
        return true;
    }

    @Override
    public int bulkInsert(Uri uri, ContentValues[] values) {
        if ((mDatabaseHelper == null)) {
            throw new IllegalStateException("Couldn't open database for " + uri + ", mDatabaseHelper is null");
        }
        SQLiteDatabase db = mDatabaseHelper.getWritableDatabase();
        if (db == null) {
            throw new IllegalStateException("Couldn't open database for " + uri + ", db is null");
        }
        int numInserted = 0;
        db.beginTransaction();
        try {
            int len = values.length;
            for (int i = 0; i < len; i++) {
                Uri newUri = insertInternal(uri, values[i]);
                if (newUri != null) {
                    numInserted++;
                }
            }
            db.setTransactionSuccessful();
        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "bulkInsert insertInternal SQLiteException", e);
        } finally {
            db.endTransaction();
        }
        getContext().getContentResolver().notifyChange(uri, null);
        return numInserted;
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        Uri newUri = insertInternal(uri, values);
        if (newUri != null) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        return newUri;
    }

    private Uri insertInternal(Uri uri, ContentValues initialValues) {
        if ((mDatabaseHelper == null)) {
            return null;
        }
        SQLiteDatabase db = null;
        try {
            db = mDatabaseHelper.getWritableDatabase();
        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "query, getReadableDatabase failed.", e);
        }
        if (db == null) {
            return null;
        }
        ContentValues values = (initialValues == null) ? new ContentValues() : new ContentValues(initialValues);
        int match = URI_MATCHER.match(uri);
        String table = null;
        switch (match) {
            case TABLESCODE.RECORDS:
                table = DatabaseConstant.TABLE_NAME_RECORDER;
                break;
            case TABLESCODE.GLOBAL_STATUS:
                table = DatabaseConstant.TABLE_NAME_STATUS;
                break;
            case TABLESCODE.CONVERT:
                table = DatabaseConstant.TABLE_NAME_CONVERT;
                break;
            case TABLESCODE.UPLOAD:
                table = DatabaseConstant.TABLE_NAME_UPLOAD;
                break;
            case TABLESCODE.PICTURE_MARK:
                table = PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME;
                break;
            case TABLESCODE.KEY_WORD:
                table = KeyWordDbUtils.TABLE_KEY_WORD_NAME;
                break;
            case TABLESCODE.NOTE:
                table = NoteDbUtils.TABLE_NOTE_NAME;
                break;
            case TABLESCODE.RECYCLE_BIN:
                table = DatabaseConstant.TABLE_NAME_RECYCLE_BIN;
                break;

            case TABLESCODE.COLLECTION_INFO:
                table = CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO;
                break;
            case TABLESCODE.GROUP_INFO:
                table = DatabaseConstant.TABLE_NAME_GROUP_INFO;
                break;
            case TABLESCODE.SEARCH_HISTORY:
                table = DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
                break;
            default:
                throw new IllegalStateException("Unknown URL: " + uri.toString());
        }
        long rowId = db.insert(table, null, values);
        DebugUtil.d(TAG, "insertInternal, rowId=" + rowId);
        if (rowId > 0) {
            return ContentUris.withAppendedId(uri, rowId);
        }
        return null;
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        if ((mDatabaseHelper == null)) {
            return 0;
        }
        SQLiteDatabase db = null;
        try {
            db = mDatabaseHelper.getWritableDatabase();
        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "delete, getReadableDatabase failed.", e);
        }
        if (db == null) {
            return 0;
        }
        int match = URI_MATCHER.match(uri);
        String table = null;
        String where = null;
        switch (match) {
            case TABLESCODE.RECORDS:
                table = DatabaseConstant.TABLE_NAME_RECORDER;
                break;
            case TABLESCODE.RECORD_ID:
                table = DatabaseConstant.TABLE_NAME_RECORDER;
                where = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;

            case TABLESCODE.GLOBAL_STATUS:
                table = DatabaseConstant.TABLE_NAME_STATUS;
                break;
            case TABLESCODE.GLOBAL_STATUS_ID:
                table = DatabaseConstant.TABLE_NAME_STATUS;
                where = DatabaseConstant.StatusColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.CONVERT:
                table = DatabaseConstant.TABLE_NAME_CONVERT;
                break;
            case TABLESCODE.CONVERT_ID:
                table = DatabaseConstant.TABLE_NAME_CONVERT;
                where = DatabaseConstant.ConvertColumn._ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.UPLOAD:
                table = DatabaseConstant.TABLE_NAME_UPLOAD;
                break;
            case TABLESCODE.UPLOAD_ID:
                table = DatabaseConstant.TABLE_NAME_UPLOAD;
                where = DatabaseConstant.UploadColumn._ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.PICTURE_MARK:
                table = PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME;
                break;
            case TABLESCODE.KEY_WORD:
                table = KeyWordDbUtils.TABLE_KEY_WORD_NAME;
                break;
            case TABLESCODE.KEY_WORD_ID:
                table = KeyWordDbUtils.TABLE_KEY_WORD_NAME;
                where = KeyWordDbUtils.ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.NOTE:
                table = NoteDbUtils.TABLE_NOTE_NAME;
                break;
            case TABLESCODE.RECYCLE_BIN:
                table = DatabaseConstant.TABLE_NAME_RECYCLE_BIN;
                break;
            case TABLESCODE.COLLECTION_INFO:
                table = CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO;
                break;
            case TABLESCODE.KEY_COLLECTION_ID:
                table = CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO;
                where = CollectionInfoDbUtils.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.GROUP_INFO:
                table = DatabaseConstant.TABLE_NAME_GROUP_INFO;
                break;
            case TABLESCODE.GROUP_INFO_ID:
                table = DatabaseConstant.TABLE_NAME_GROUP_INFO;
                where = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.SEARCH_HISTORY:
                table = DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
                break;
            case TABLESCODE.SEARCH_HISTORY_ID:
                table = DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
                where = DatabaseConstant.SearchHistoryColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            default:
                throw new IllegalStateException("Unknown URL: " + uri.toString());
        }
        // Add in the user requested WHERE clause, if needed
        if (!TextUtils.isEmpty(selection)) {
            if (!TextUtils.isEmpty(where)) {
                where = where + " AND (" + selection + ")";
            } else {
                where = selection;
            }
        }
        int count = 0;
        try {
            count = db.delete(table, where, selectionArgs);
        } catch (Throwable t) {
            DebugUtil.e(TAG, "delete, error=" + t);
        }
        if ((count > 0) && !db.inTransaction()) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        DebugUtil.d(TAG, "delete, selection=" + selection + ", count=" + count);
        return count;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection, String[] selectionArgs) {
        DebugUtil.v(TAG, "update, uri=" + uri + ", values=" + replaceAllUUID(values.toString()));
        int count = 0;
        if ((mDatabaseHelper == null)) {
            return count;
        }
        int match = URI_MATCHER.match(uri);
        SQLiteDatabase db = null;
        try {
            db = mDatabaseHelper.getWritableDatabase();
        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "query, getReadableDatabase failed.", e);
        }
        if (db == null) {
            return count;
        }
        String table = null;
        String where = null;
        switch (match) {
            case TABLESCODE.RECORDS:
                table = DatabaseConstant.TABLE_NAME_RECORDER;
                break;
            case TABLESCODE.RECORD_ID:
                table = DatabaseConstant.TABLE_NAME_RECORDER;
                where = DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + " = " + ContentUris.parseId(uri);
                break;
            case TABLESCODE.GLOBAL_STATUS:
                table = DatabaseConstant.TABLE_NAME_STATUS;
                break;
            case TABLESCODE.GLOBAL_STATUS_ID:
                table = DatabaseConstant.TABLE_NAME_STATUS;
                where = DatabaseConstant.StatusColumn.COLUMN_NAME_ID + " = " + ContentUris.parseId(uri);
                break;
            case TABLESCODE.CONVERT:
                table = DatabaseConstant.TABLE_NAME_CONVERT;
                break;
            case TABLESCODE.CONVERT_ID:
                table = DatabaseConstant.TABLE_NAME_CONVERT;
                where = DatabaseConstant.ConvertColumn._ID + " = " + ContentUris.parseId(uri);
                break;
            case TABLESCODE.UPLOAD:
                table = DatabaseConstant.TABLE_NAME_UPLOAD;
                break;
            case TABLESCODE.UPLOAD_ID:
                table = DatabaseConstant.TABLE_NAME_UPLOAD;
                where = DatabaseConstant.UploadColumn._ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.PICTURE_MARK:
                table = PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME;
                break;
            case TABLESCODE.KEY_WORD:
                table = KeyWordDbUtils.TABLE_KEY_WORD_NAME;
                break;
            case TABLESCODE.KEY_WORD_ID:
                table = KeyWordDbUtils.TABLE_KEY_WORD_NAME;
                where = KeyWordDbUtils.ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.NOTE:
                table = NoteDbUtils.TABLE_NOTE_NAME;
                break;
            case TABLESCODE.RECYCLE_BIN:
                table = DatabaseConstant.TABLE_NAME_RECYCLE_BIN;
                break;
            case TABLESCODE.COLLECTION_INFO:
                table = CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO;
                break;
            case TABLESCODE.KEY_COLLECTION_ID:
                table = CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO;
                where = CollectionInfoDbUtils.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.GROUP_INFO:
                table = DatabaseConstant.TABLE_NAME_GROUP_INFO;
                break;
            case TABLESCODE.GROUP_INFO_ID:
                table = DatabaseConstant.TABLE_NAME_GROUP_INFO;
                where = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            case TABLESCODE.SEARCH_HISTORY:
                table = DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
                break;
            case TABLESCODE.SEARCH_HISTORY_ID:
                table = DatabaseConstant.TABLE_NAME_SEARCH_HISTORY;
                where = DatabaseConstant.SearchHistoryColumn.COLUMN_NAME_ID + "=" + ContentUris.parseId(uri);
                break;
            default:
                throw new IllegalStateException("Unknown URL: " + uri.toString());
        }
        if (!TextUtils.isEmpty(selection)) {
            if (!TextUtils.isEmpty(where)) {
                where = where + " AND (" + selection + ")";
            } else {
                where = selection;
            }
        }
        count = db.update(table, values, where, selectionArgs);
        if ((count > 0) && !db.inTransaction()) {
            getContext().getContentResolver().notifyChange(uri, null);
        }
        return count;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection, String[] selectionArgs, String sortOrder) {

        if ((mDatabaseHelper == null)) {
            return null;
        }
        int table = URI_MATCHER.match(uri);
        DebugUtil.v(TAG, "query, uri=" + uri + ", selection:" + selection + ", table:" + table);
        SQLiteDatabase db = null;
        try {
            db = mDatabaseHelper.getReadableDatabase();

        } catch (SQLiteException e) {
            DebugUtil.e(TAG, "query, getReadableDatabase failed.", e);
        }
        if (db == null) {
            return null;
        }

        SQLiteQueryBuilder qb = new SQLiteQueryBuilder();
        List<String> prependArgs = new ArrayList<String>();
        String limit = uri.getQueryParameter("limit");
        switch (table) {
            case TABLESCODE.RECORDS:
                qb.setTables(DatabaseConstant.TABLE_NAME_RECORDER);
                break;
            case TABLESCODE.RECORD_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_RECORDER);
                qb.appendWhere(DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.GLOBAL_STATUS:
                qb.setTables(DatabaseConstant.TABLE_NAME_STATUS);
                break;
            case TABLESCODE.GLOBAL_STATUS_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_STATUS);
                qb.appendWhere(DatabaseConstant.StatusColumn.COLUMN_NAME_ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.CONVERT:
                qb.setTables(DatabaseConstant.TABLE_NAME_CONVERT);
                break;
            case TABLESCODE.CONVERT_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_CONVERT);
                qb.appendWhere(DatabaseConstant.ConvertColumn._ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.UPLOAD:
                qb.setTables(DatabaseConstant.TABLE_NAME_UPLOAD);
                break;
            case TABLESCODE.UPLOAD_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_UPLOAD);
                qb.appendWhere(DatabaseConstant.UploadColumn._ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.PICTURE_MARK:
                qb.setTables(PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME);
                break;
            case TABLESCODE.KEY_WORD:
                qb.setTables(KeyWordDbUtils.TABLE_KEY_WORD_NAME);
                break;
            case TABLESCODE.KEY_WORD_ID:
                qb.setTables(KeyWordDbUtils.TABLE_KEY_WORD_NAME);
                qb.appendWhere(KeyWordDbUtils.ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.NOTE:
                qb.setTables(NoteDbUtils.TABLE_NOTE_NAME);
                break;
            case TABLESCODE.RECYCLE_BIN:
                qb.setTables(DatabaseConstant.TABLE_NAME_RECYCLE_BIN);
                break;
            case TABLESCODE.COLLECTION_INFO:
                qb.setTables(CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO);
                break;
            case TABLESCODE.KEY_COLLECTION_ID:
                qb.setTables(CollectionInfoDbUtils.TABLE_NAME_COLLECTION_INFO);
                qb.appendWhere(CollectionInfoDbUtils.COLUMN_NAME_ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.GROUP_INFO:
                qb.setTables(DatabaseConstant.TABLE_NAME_GROUP_INFO);
                break;
            case TABLESCODE.GROUP_INFO_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_GROUP_INFO);
                qb.appendWhere(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            case TABLESCODE.SEARCH_HISTORY:
                qb.setTables(DatabaseConstant.TABLE_NAME_SEARCH_HISTORY);
                break;
            case TABLESCODE.SEARCH_HISTORY_ID:
                qb.setTables(DatabaseConstant.TABLE_NAME_SEARCH_HISTORY);
                qb.appendWhere(DatabaseConstant.SearchHistoryColumn.COLUMN_NAME_ID + "=?");
                prependArgs.add(ContentUris.parseId(uri) + "");
                break;
            default:
                throw new IllegalStateException("Unknown URL: " + uri.toString());
        }
        Cursor c = null;
        try {
            c = qb.query(db, projection, selection, combine(prependArgs, selectionArgs), null, null, sortOrder, limit);
        } catch (Exception e) {
            DebugUtil.e(TAG, "query, failed ", e);
        }
        if (c != null) {
            String nonotify = uri.getQueryParameter(NO_NOTIFY_KEY);
            if (TextUtils.isEmpty(nonotify) || !nonotify.equals(NO_NOTIFY_VALUE)) {
                c.setNotificationUri(getContext().getContentResolver(), uri);
            }
        }
        return c;
    }

    private String[] combine(List<String> prepend, String[] userArgs) {
        int presize = prepend.size();
        if (presize == 0) {
            return userArgs;
        }
        int usersize = (userArgs != null) ? userArgs.length : 0;
        String[] combined = new String[presize + usersize];
        for (int i = 0; i < presize; i++) {
            combined[i] = prepend.get(i);
        }
        for (int i = 0; i < usersize; i++) {
            combined[presize + i] = userArgs[i];
        }
        return combined;
    }

    private String replaceAllUUID(String uuidStr) {
        return uuidStr.replaceAll(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, HASHID);
    }

    @Override
    public String getType(Uri uri) {
        switch (URI_MATCHER.match(uri)) {
            case TABLESCODE.RECORDS:
            case TABLESCODE.RECORD_ID:
                return DatabaseConstant.PROVIDER_RECORD_TYPE;
            case TABLESCODE.GLOBAL_STATUS:
            case TABLESCODE.GLOBAL_STATUS_ID:
                return DatabaseConstant.PROVIDER_STATUS_TYPE;
            case TABLESCODE.CONVERT:
            case TABLESCODE.CONVERT_ID:
                return DatabaseConstant.PROVIDER_CONVERT_TYPE;
            case TABLESCODE.UPLOAD:
            case TABLESCODE.UPLOAD_ID:
                return DatabaseConstant.PROVIDER_UPLOAD_TYPE;
            case TABLESCODE.PICTURE_MARK:
                return PictureMarkDbUtils.PROVIDER_PICTURE_MARK_TYPE;
            case TABLESCODE.KEY_WORD:
            case TABLESCODE.KEY_WORD_ID:
                return KeyWordDbUtils.PROVIDER_KEY_WORD_TYPE;
            case TABLESCODE.NOTE:
                return NoteDbUtils.PROVIDER_NOTE_TYPE;
            case TABLESCODE.RECYCLE_BIN:
                return DatabaseConstant.TABLE_NAME_RECYCLE_BIN;
            case TABLESCODE.COLLECTION_INFO:
            case TABLESCODE.KEY_COLLECTION_ID:
                return CollectionInfoDbUtils.PROVIDER_COLLECTION_INFO_TYPE;
            case TABLESCODE.GROUP_INFO:
            case TABLESCODE.GROUP_INFO_ID:
                return DatabaseConstant.PROVIDER_GROUP_INFO_TYPE;
            case TABLESCODE.SEARCH_HISTORY:
            case TABLESCODE.SEARCH_HISTORY_ID:
                return DatabaseConstant.PROVIDER_SEARCH_HISTORY_TYPE;
        }
        throw new IllegalStateException("Unknown URL: " + uri);
    }
}
