/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameParam
 * * Description: SmartNameParam
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.common.databean

import androidx.annotation.VisibleForTesting
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.LanguageUtil
import java.util.UUID
import kotlin.math.max

/**
 * 其中dialogs字段为ASR后的内容数组，结构如下:
 * data class DialogContent (
var id: Int = 1,                                    //序号，从1开始
val content: String,
val timemap: Long,
val contentType: String? = null,                    //内容类型：文字-text，图片-picture，文件-file，url
val contentDetailType: String? = null,              //内容细分类型
val speakerType: String,                            //语音类必填（ourside /otherside /thirdSide/videoSide）
val language: String? = null
)*/
data class SmartNameParam internal constructor(
    val sessionId: String,
    val dialogs: List<SmartParamsContent>,
    val timeout: Long,
    var otherName: String? = "",    //对话者名字
    val inputLanguage: String = LANGUAGE_DEFAULT,
    val outputLanguage: String = LANGUAGE_DEFAULT,
    val summaryType: Int = SUMMARY_TYPE_SMART_NAME
) {

    companion object {
        private const val TAG = "ConvertTitleParam"
        const val LANGUAGE_DEFAULT = "zh"

        const val SPEAKER_TYPE_OURSIDE = "ourside"
        const val SPEAKER_TYPE_OTHERSIDE = "otherside"
        const val SPEAKER_TYPE_THIRDSIDE = "thirdSide"
        const val SPEAKER_TYPE_VIDEOSIDE = "videoSide"

        /*其中语音类请求，summaryType填入 SummaryResultType.MINI ,则输出标题*/
        const val SUMMARY_TYPE_DEFAULT = 0
        const val SUMMARY_TYPE_SMART_NAME = 1

        @JvmStatic
        @JvmOverloads
        fun createSmartNameParam(
            sessionId: String,
            dialogs: List<SmartParamsContent>,
            otherName: String? = "",
            inputLanguage: String = LANGUAGE_DEFAULT,
            outputLanguage: String = LANGUAGE_DEFAULT,
            summaryType: Int = SUMMARY_TYPE_SMART_NAME
        ): SmartNameParam {
            var contentTxtSize = 0
            dialogs.forEach {
                contentTxtSize += it.content?.length ?: 0
            }
            return SmartNameParam(
                sessionId = sessionId,
                dialogs = dialogs,
                timeout = SmartNameTimeoutStrategy.calculateGenerateTimeout(contentTxtSize),
                otherName = otherName,
                inputLanguage = inputLanguage,
                outputLanguage = outputLanguage,
                summaryType = summaryType
            )
        }

        @JvmStatic
        fun toJson(param: SmartNameParam): String {
            return GsonUtil.toJson(param)
        }

        /**{
        "id": 1,
        "timestamp": 1696902759369,
        "content": "你好，李总，最近还好吗？明天有没有空？",
        "speakerType": "我方"
        }*/
        @JvmStatic
        fun toConvertSmartNameParam(beanConvertText: BeanConvertText?): SmartNameParam? {
            if (beanConvertText == null || beanConvertText.sublist.isNullOrEmpty()) {
                return null
            }
            val language = LanguageUtil.getCurrentLanguageFromSystem()
            val contents: ArrayList<SmartParamsContent> = arrayListOf()
            var contentTxtSize = 0
            beanConvertText.sublist.forEachIndexed { index, subItem ->
                val contentId = index + 1
                val contentParam = SmartParamsContent(
                    id = contentId,
                    content = subItem.recgText,
                    timemap = System.currentTimeMillis(),
                    speakerType = SPEAKER_TYPE_OURSIDE,
                    language = language
                )
                contents.add(contentParam)
                contentTxtSize += contentParam.content?.length ?: 0
            }
            val sessionId = if (beanConvertText.traceId.isNullOrBlank()) {
                UUID.randomUUID().toString()
            } else {
                beanConvertText.traceId ?: UUID.randomUUID().toString()
            }
            if (contents.isNotEmpty()) {
                return SmartNameParam(
                    sessionId = sessionId,
                    dialogs = contents,
                    timeout = SmartNameTimeoutStrategy.calculateGenerateTimeout(contentTxtSize),
                    summaryType = SUMMARY_TYPE_SMART_NAME,
                    inputLanguage = language,
                    outputLanguage = language
                )
            }
            return null
        }

        @JvmStatic
        fun toConvertSmartNameParam(convertContentItems: ArrayList<ConvertContentItem>?): SmartNameParam? {
            if (convertContentItems.isNullOrEmpty()) {
                return null
            }
            val language = LanguageUtil.getCurrentLanguageFromSystem()
            val contents: ArrayList<SmartParamsContent> = arrayListOf()
            var contentTxtSize = 0
            convertContentItems.forEachIndexed { index, subItem ->
                val contentId = index + 1
                val contentParam = SmartParamsContent(
                    contentId,
                    content = subItem.textContent,
                    timemap = System.currentTimeMillis(),
                    speakerType = SPEAKER_TYPE_OURSIDE,
                    language = language
                )
                contents.add(contentParam)
                contentTxtSize += contentParam.content?.length ?: 0
            }
            return SmartNameParam(
                sessionId = UUID.randomUUID().toString(),
                dialogs = contents,
                timeout = SmartNameTimeoutStrategy.calculateGenerateTimeout(contentTxtSize),
                inputLanguage = language,
                outputLanguage = language,
                summaryType = SUMMARY_TYPE_SMART_NAME
            )
        }
    }
}

data class SmartParamsContent(
    var id: Int = 0,
    var content: String?,
    var contentDetailType: String? = null,
    var contentType: String? = null,
    var language: String? = "zh",
    var speakerType: String = SmartNameParam.SPEAKER_TYPE_OURSIDE,
    var timemap: Long = 0L
) {
    override fun toString(): String {
        return "SmartParamsContent(id=$id, content.length=${content?.length}, contentDetailType=$contentDetailType, " +
                "contentType=$contentType, language:$language, speakerType:$speakerType, timemap:$timemap)"
    }
}

/**
 * https://odocs.myoas.com/docs/5rk9dE5galC66dqx/ 《AI录音超时限制优化方案》访问密码 warcuw
 */
@VisibleForTesting
internal object SmartNameTimeoutStrategy {
    private const val DEFAULT_TIMEOUT = 5 * 60 * 1000L
    private const val TO_MILLISECONDS = 1000L
    private const val TIMEOUT_PRE_TXT = 2f / 16000

    private const val GEAR1_TXT_SIZE_END = 3000
    private const val GEAR1_BASE_TIMEOUT = 6.5f * 6
    private const val GEAR1_MIN_TIMEOUT = 7 * 6

    private const val GEAR2_TXT_SIZE_START = GEAR1_TXT_SIZE_END
    private const val GEAR2_TXT_SIZE_END = 10000
    private const val GEAR2_BASE_TIMEOUT = 8.5f * 6
    private const val GEAR2_MIN_TIMEOUT = 9 * 6
    private val GEAR2_TXT_SIZE_RANGE = GEAR2_TXT_SIZE_START until GEAR2_TXT_SIZE_END

    private const val GEAR3_TXT_SIZE_START = GEAR2_TXT_SIZE_END
    private const val GEAR3_TXT_SIZE_END = 30000
    private const val GEAR3_BASE_TIMEOUT = 12.5f * 6
    private const val GEAR3_MIN_TIMEOUT = 13 * 6
    private val GEAR3_TXT_SIZE_RANGE = GEAR3_TXT_SIZE_START..GEAR3_TXT_SIZE_END

    /**
     * 计算智能标题生成超时时间。
     *
     * 根据输入的语音识别文本大小（asrTxtSize），计算智能标题生成操作的超时时间。
     * 超时时间基于文本大小动态调整，确保处理效率与响应时间。
     *
     * @param asrTxtSize 语音识别文本的大小，单位为字符数。
     * @return Long 智能标题生成操作的超时时间，单位为毫秒。
     */
    @JvmStatic
    fun calculateGenerateTimeout(asrTxtSize: Int): Long {
        val (baseTimeout, minTimeout) = when {
            asrTxtSize < GEAR1_TXT_SIZE_END -> GEAR1_BASE_TIMEOUT to GEAR1_MIN_TIMEOUT
            asrTxtSize in GEAR2_TXT_SIZE_RANGE -> GEAR2_BASE_TIMEOUT to GEAR2_MIN_TIMEOUT
            asrTxtSize in GEAR3_TXT_SIZE_RANGE -> GEAR3_BASE_TIMEOUT to GEAR3_MIN_TIMEOUT
            else -> return DEFAULT_TIMEOUT
        }
        val timeoutSecond = baseTimeout + asrTxtSize * TIMEOUT_PRE_TXT
        return max(minTimeout, timeoutSecond.toInt()) * TO_MILLISECONDS
    }
}