/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppCompat
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/1/3 19:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/1/3       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.removableapp

import android.content.ComponentName
import android.content.Intent
import android.net.Uri
import com.soundrecorder.base.utils.BaseUtil

object RemovableAppCompat {

    private const val PACKAGE_NAME_SAFE_CENTER = "com.oplus.safecenter"
    private const val CLASS_NAME_SAFE_CENTER_REMOVABLE_APP_SERVICE =
        "com.oplus.safecenter.removableapp.service.RemovableAppService"

    private const val PACKAGE_NAME_EX_SYSTEM_SERVICE = "com.oplus.exsystemservice"
    private const val CLASS_NAME_EX_SYSTEM_REMOVABLE_APP_SERVICE =
        "com.oplus.exsystemservice.removableapp.service.RemovableAppService"

    private const val TABLE_NAME_REMOVABLEAPP = "removableapp"

    private const val AUTHORITY_COLOR = "com.color.provider.removableapp"
    private const val AUTHORITY_OPLUS = "com.oplus.provider.removableapp"

    /**
     * Obtain bind service intent. Depending on the Android version, use different componentName.
     */
    fun obtainBindServiceIntent(): Intent {
        val serviceIntent = Intent()
        val componentName = if (BaseUtil.isAndroidTOrLater) {
            ComponentName(PACKAGE_NAME_EX_SYSTEM_SERVICE, CLASS_NAME_EX_SYSTEM_REMOVABLE_APP_SERVICE)
        } else {
            ComponentName(PACKAGE_NAME_SAFE_CENTER, CLASS_NAME_SAFE_CENTER_REMOVABLE_APP_SERVICE)
        }
        serviceIntent.component = componentName
        return serviceIntent
    }

    /**
     * Obtain query app info Uri. Depending on the Android version, use different authority.
     *
     * AndroidS and earlier versions [AUTHORITY_COLOR], Android T [AUTHORITY_OPLUS].
     */
    fun obtainQueryAppInfoUri(): Uri {
        val authority = if (BaseUtil.isAndroidTOrLater) {
            AUTHORITY_OPLUS
        } else {
            AUTHORITY_COLOR
        }
        val baseUri = Uri.parse("content://$authority")
        return Uri.withAppendedPath(baseUri, TABLE_NAME_REMOVABLEAPP)
    }
}