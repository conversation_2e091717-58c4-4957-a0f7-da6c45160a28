/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OnFileEventListener
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.fileobserve

interface OnFileEventListener {
    fun onFileObserver(event: Int, path: String?, allPath: String?)
}