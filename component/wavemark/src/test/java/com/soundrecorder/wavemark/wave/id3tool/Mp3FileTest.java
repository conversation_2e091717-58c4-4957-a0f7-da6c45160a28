package com.soundrecorder.wavemark.wave.id3tool;

import static org.mockito.Mockito.doReturn;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Map;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class Mp3FileTest {
    private static final int TWELVE = 12;
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_returnNotnull_when_initMarkSizeTag() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(13).when(fileChannel).read(ByteBuffer.allocate(TWELVE));
        Mp3File mp3File = new Mp3File();
        boolean isMark = Whitebox.invokeMethod(mp3File, "initMarkSizeTag", fileChannel);
        Assert.assertFalse(isMark);
        Assert.assertTrue(mp3File.hasMarkSizeTag());
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnNotnull_when_init() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        Whitebox.invokeMethod(mp3File, "init", 45, true);
        Assert.assertNull(mp3File.getId3v1Tag());
    }

    @Test
    public void should_returnNotnull_when_initId3v2Tag() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        Whitebox.setInternalState(mp3File, "mFileChannel", fileChannel, Mp3File.class);
        mp3File.extractMp3File(123, true);
        boolean scanFile = Whitebox.getInternalState(mp3File, "scanFile");
        Assert.assertTrue(scanFile);
    }

    @Test
    public void should_returnNotnull_when_preScanFile() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        int size = mp3File.preScanFile(fileChannel);
        Assert.assertEquals(size, 0);
    }

    @Test
    public void should_returnNotnull_when_scanBlock() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(2l).when(fileChannel).size();
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        Whitebox.setInternalState(mp3File, "mFileChannel", fileChannel, Mp3File.class);
        int size = Whitebox.invokeMethod(mp3File, "scanBlock", "123".getBytes(), 50, 10, 5);
        Assert.assertEquals(size, 5);
    }

    @Test
    public void should_returnNotnull_when_create() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(2l).when(fileChannel).size();
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        Whitebox.setInternalState(mp3File, "mFileChannel", fileChannel, Mp3File.class);
        int size = Whitebox.invokeMethod(mp3File, "scanBlock", "123".getBytes(), 50, 10, 5);
        Assert.assertEquals(size, 5);
    }

    @Test
    public void should_returntrue_isXingFrame() throws Exception {
        byte[] bytes = new byte[29];
        Mp3File mp3File = new Mp3File();
        boolean isflag = Whitebox.invokeMethod(mp3File, "isXingFrame", bytes, 1);
        Assert.assertFalse(isflag);
    }

    @Test
    public void should_returnsize_scanBlockForStart() throws Exception {
        byte[] bytes = new byte[29];
        bytes[1] = (byte) 0xFF;
        bytes[2] = (byte) 0xE0;
        Mp3File mp3File = new Mp3File();
        int size = Whitebox.invokeMethod(mp3File, "scanBlockForStart", bytes, 42, 1, 1);
        Assert.assertEquals(size, 2);
        Assert.assertEquals(mp3File.getBitrate(), 0);
        Assert.assertEquals(mp3File.getEndOffset(), -1);
        Assert.assertNull(mp3File.getEmphasis());
        Assert.assertNull(mp3File.getChannelMode());
    }

    @Test
    public void should_returnsize_addBitrate() throws Exception {
        Mp3File mp3File = new Mp3File();
        Whitebox.invokeMethod(mp3File, "addBitrate", 1);
        Map<Integer, MutableInteger> bitrates = Whitebox.getInternalState(mp3File, "bitrates");
        Assert.assertEquals(bitrates.size(), 1);
    }

    @Test
    public void should_returnNull_initId3v1Tag() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(2l).when(fileChannel).size();
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH));
        Mp3File mp3File = new Mp3File();
        Whitebox.invokeMethod(mp3File, "initId3v1Tag", fileChannel);
        ID3v1Tag id3v1Tag = Whitebox.getInternalState(mp3File, "id3v1Tag");
        Assert.assertNull(id3v1Tag);
    }

    @Test
    public void should_returnNull_initId3v2Tag() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(2l).when(fileChannel).size();
        doReturn(129).when(fileChannel).read(ByteBuffer.allocate(2));
        Mp3File mp3File = new Mp3File();
        Whitebox.setInternalState(mp3File, "startOffset", 2);
        Whitebox.invokeMethod(mp3File, "initId3v2Tag", fileChannel);
        Assert.assertNull(mp3File.getId3v2Tag());
    }

    @Test
    public void should_returnNull_initCustomTag() throws Exception {
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        doReturn(2l).when(fileChannel).size();
        doReturn(3).when(fileChannel).read(ByteBuffer.allocate(2));
        Mp3File mp3File = new Mp3File();
        Whitebox.invokeMethod(mp3File, "initCustomTag", fileChannel);
        byte[] customTag = Whitebox.getInternalState(mp3File, "customTag");
        Assert.assertNotNull(customTag);
    }

    @Test
    public void should_returnSize_when_setAndget() throws IOException, NotSupportedException {
        byte[] bytes = new byte[5];
        bytes[1] = 0;
        Mp3File mp3File = new Mp3File();
        ID3v1Tag id3v1Tag = new ID3v1Tag();
        mp3File.setId3v1Tag(id3v1Tag);
        Assert.assertEquals(mp3File.getId3v1Tag(), id3v1Tag);
        ID3v22Tag id3v2Tag = new ID3v22Tag();
        mp3File.setId3v2Tag(id3v2Tag);
        Assert.assertNotEquals(mp3File.getId3v2Tag(), id3v1Tag);
        mp3File.removeId3v1Tag();
        Assert.assertFalse(mp3File.hasId3v1Tag());
        mp3File.setCustomTag(bytes);
        Assert.assertEquals(mp3File.getCustomTag(), bytes);
        mp3File.saveMarkToNewFile(3);
        Assert.assertNull(Whitebox.getInternalState(mp3File, "mSaveFileOutputStream"));
        Assert.assertNotNull(mp3File.getLengthInMilliseconds());
        Assert.assertNotNull(mp3File.byteArrayToInt(bytes));
        Assert.assertNotNull(mp3File.intToByteArray(2));
    }
}
