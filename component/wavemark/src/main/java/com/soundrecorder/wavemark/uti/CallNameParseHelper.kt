/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CallNameParseHelper
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/6 1.0 create
 */

package com.soundrecorder.wavemark.uti

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.wavemark.wave.id3tool.Mp3File
import java.nio.file.Files
import java.nio.file.Paths

object CallNameParseHelper {
    private const val TAG = "CallNameParseHelper"
    private const val HEAD_TAG = "opluscalltag"
    private const val LEN_HEAD = 12
    private const val LEN_CALL_TYPE = 1
    private const val LEN_CALL_APP_NAME = 30
    private const val LEN_CALL_NAME_LEN = 4
    private const val LEN_CALL_NAME = 99
    private const val TOTAL_LEN = 146

    /**
     * 目前场景是AAC文件
     * 1.固定tag：opluscalltag 12
     * 2.通话类型：0x0 传统通话； 0x1 智慧语音通话  1
     * 3.通话软件名称： 如果是传统通话，全部传0，智慧语音录音时，如果是微信录音，需要把微信写入该字段 30
     * 4.联系人名称或昵称的byte长度（对应utf8编码）  4
     * 5.联系人名称或者昵称：（utf8编码），最大长度33个汉字 名称不足99字节时，用0补齐 99
     * 总： 146
     */
    fun parseCallName(path: String): String? {
        runCatching {
            val bytes = Files.readAllBytes(Paths.get(path))
            if (bytes.size <= TOTAL_LEN) {
                DebugUtil.w(TAG, "parseCallName ,return by len not enough，${bytes.size}")
                return null
            }
            val appendBytes = ByteArray(TOTAL_LEN)
            System.arraycopy(bytes, bytes.size - TOTAL_LEN, appendBytes, 0, TOTAL_LEN)

            val headBytes = ByteArray(LEN_HEAD)
            System.arraycopy(appendBytes, 0, headBytes, 0, LEN_HEAD)
            val headInfo = String(headBytes)
            if (HEAD_TAG != headInfo) {
                DebugUtil.w(TAG, "parseCallName ,tag not match, $headInfo")
                return null
            }
            val callTypeBytes = ByteArray(LEN_CALL_TYPE)
            System.arraycopy(appendBytes, LEN_HEAD, callTypeBytes, 0, LEN_CALL_TYPE)
            val callType = callTypeBytes.getOrNull(0)?.toInt()

            val appNameBytes = ByteArray(LEN_CALL_APP_NAME)
            System.arraycopy(appendBytes, LEN_HEAD + LEN_CALL_TYPE, appNameBytes, 0, LEN_CALL_APP_NAME)
            val appName = String(getRealContentByte(appNameBytes))

            val callNameLenBytes = ByteArray(LEN_CALL_NAME_LEN)
            System.arraycopy(appendBytes, LEN_HEAD + LEN_CALL_TYPE + LEN_CALL_APP_NAME, callNameLenBytes, 0, LEN_CALL_NAME_LEN)
            val nameLength = String(getRealContentByte(callNameLenBytes))

            val callNameBytes = ByteArray(LEN_CALL_NAME)
            System.arraycopy(appendBytes, TOTAL_LEN - LEN_CALL_NAME, callNameBytes, 0, LEN_CALL_NAME)
            val realNameBytes = getRealContentByte(callNameBytes)
            if (nameLength.toIntOrNull() != realNameBytes.size) {
                DebugUtil.w(TAG, "parseCallName ,nameLength not match")
            }
            val callName = String(realNameBytes)

            DebugUtil.i(TAG, "parseCallName: callType=$callType,appNameStr=$appName,callNameLen=$nameLength,callNameStr=$callName")
            return callName
        }.onFailure {
            DebugUtil.e(TAG, "parseCallName error $it")
        }
        return null
    }

    private fun getRealContentByte(byteArray: ByteArray): ByteArray {
        val defaultValue = 0.toByte()
        return byteArray.filter {
            it != defaultValue
        }.toByteArray()
    }

    private var mMp3File: Mp3File? = null
    private const val ALBUM_ARTIST = "ALBUM_ARTIST"

    /**
     * 通话模块使用jaudiotagger 开源sdk写入id3v2标签到录音文件
     * FieldKey.ALBUM_ARTIST: 对应的用户昵称
     */
    fun parseCallName(context: Context, mediaId: Long): String? {
        runCatching {
            if (mediaId == -1L) {
                return null
            }
            val uri = MediaDBUtils.genUri(mediaId)
            mMp3File = Mp3File()
            mMp3File?.create(uri, context)
            mMp3File?.extractMp3FileWithDefault(true)

            val id3v2Tag = mMp3File?.id3v2Tag
            if (id3v2Tag == null) {
                DebugUtil.d(TAG, "parseCallName id3v2Tag is null")
                return null
            }
            if (id3v2Tag.albumArtist.isNullOrEmpty()) {
                DebugUtil.d(TAG, "parseCallName id3v2Tag.albumArtist is null")
                return null
            }
            DebugUtil.d(TAG, "parseCallName albumArtist:${id3v2Tag.albumArtist}")
            return id3v2Tag.albumArtist
        }.onFailure {
            DebugUtil.e(TAG, "extract mp3 file error", it)
        }.getOrNull().run {
            mMp3File?.release()
        }
        return null
    }

    fun releaseMp3() {
        if (mMp3File != null) {
            mMp3File?.setCancleScanFlag(true)
            mMp3File = null
        }
    }
}