/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveMarkApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark

import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.mark.IIPictureMarkListener
import com.soundrecorder.modulerouter.mark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.mark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.mark.WaveMarkInterface
import com.soundrecorder.wavemark.picturemark.PictureMarkDelegate
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil

object WaveMarkApi : WaveMarkInterface {

    override fun decodeAmplitudeByUri(uri: Uri?): String {
        return AmplitudeListUtil.decodeAmplitudeByUri(uri)
    }

    override fun <T, R> newPictureMarkDelegate(
        ownerProvider: IPictureMarkLifeOwnerProvider,
        isActivityRecreate: Boolean,
        listener: IIPictureMarkListener<T, R>?,
    ): IPictureMarkDelegate<T> {
        return PictureMarkDelegate(
            ownerProvider,
            isActivityRecreate,
            listener as? IIPictureMarkListener<MarkMetaData, MarkDataBean>
        ) as IPictureMarkDelegate<T>
    }

    override fun getMergeMarkList(path: String, playUri: Uri, isRecycle: Boolean): List<Any> {
        val amplitudeListUtil =
            AmplitudeListUtil(BaseApplication.getAppContext(), path, playUri, isRecycle)
        val markList = amplitudeListUtil.mergeMarkList
        amplitudeListUtil.release()
        return markList
    }
}