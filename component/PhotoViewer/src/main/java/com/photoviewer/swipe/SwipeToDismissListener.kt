package com.photoviewer.swipe

import android.util.DisplayMetrics
import android.view.MotionEvent
import android.view.View
import android.view.animation.AccelerateInterpolator
import kotlin.math.abs

class SwipeToDismissListener(
    private val backgroundView: View,
    private val dismissContainer: View,
    private val onDismiss: () -> Unit,
    private val onSwipe: (alpha: Float) -> Unit
) {
    companion object {
        private const val ANIMATION_DURATION = 200L
        private val DAMPING by lazy {
            48f * DisplayMetrics.DENSITY_DEVICE_STABLE / DisplayMetrics.DENSITY_DEFAULT
        }
    }

    private var startX = 0f
    private var startY = 0f
    private var actionIndex = -1
    private var canSwipe = false

    internal fun onTouch(v: View, event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                canSwipe = true
                actionIndex = event.actionIndex
                startX = event.getX(actionIndex)
                startY = event.getY(actionIndex)
                return true
            }
            MotionEvent.ACTION_POINTER_UP, MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                canSwipe = false
                if (event.actionIndex == actionIndex) {
                    doOnSwipeEnd(v.height)
                }
                return true
            }
            MotionEvent.ACTION_MOVE -> {
                if (canSwipe) {
                    dismissContainer.translationX = event.getX(actionIndex) - startX
                    val translationY = event.getY(actionIndex) - startY
                    dismissContainer.translationY = translationY
                    doOnSwipe(translationY)
                }
                return true
            }
            else -> {
                return false
            }
        }
    }

    private fun doOnSwipeEnd(parentHeight: Int) {
        val animateTo = when {
            dismissContainer.translationY >= DAMPING -> parentHeight.toFloat()
            else -> 0f
        }
        if (animateTo == 0f) {
            animateTranslation(animateTo)
        } else {
            onDismiss()
        }
    }

    private fun animateTranslation(translationTo: Float) {
        dismissContainer.animate()
            .translationY(translationTo)
            .translationX(0f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(AccelerateInterpolator())
            .setUpdateListener {
                doOnSwipe(dismissContainer.translationY)
            }.withEndAction {
                dismissContainer.animate().setUpdateListener(null).withEndAction(null)
            }.start()
    }

    private fun doOnSwipe(translationY: Float) {
        var absTranslationY = abs(translationY)
        val maxTranslationY = DAMPING * 3
        if (absTranslationY > maxTranslationY) {
            absTranslationY = maxTranslationY
        }
        val alpha = 1 - abs(absTranslationY / maxTranslationY)
        onSwipe.invoke(alpha)
        /**
         * 屏蔽位移为负值alpha、scale变化
         */
        if (translationY >= 0) {
            backgroundView.alpha = alpha
            val scale = 1 - abs(absTranslationY / maxTranslationY / 3)
            dismissContainer.scaleX = scale
            dismissContainer.scaleY = scale
        }
    }
}