<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/pv_color_ripple"
    android:radius="23dp">
    <item>
        <layer-list>
            <item>
                <shape>
                    <size
                        android:width="46dp"
                        android:height="46dp" />
                    <solid android:color="#33FFFFFF" />
                    <corners android:radius="23dp" />
                </shape>
            </item>
            <item android:gravity="center">
                <vector
                    android:width="18dp"
                    android:height="13dp"
                    android:viewportWidth="18"
                    android:viewportHeight="13">
                    <path
                        android:fillColor="#00000000"
                        android:pathData="M1.125,6L6.375,11.25L16.875,0.75"
                        android:strokeWidth="1.6"
                        android:strokeColor="#FFFFFFFF"
                        android:strokeLineJoin="round" />
                </vector>
            </item>
        </layer-list>
    </item>
</ripple>