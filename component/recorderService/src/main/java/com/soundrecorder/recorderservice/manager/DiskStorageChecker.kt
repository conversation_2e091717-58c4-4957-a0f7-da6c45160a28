/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: DiskStorageChecker
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import com.oppo.media.OppoRecorder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AddonAdapterCompatUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.base.utils.StorageUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.common.utils.SettingsAdapter
import com.soundrecorder.modulerouter.notification.NotificationUtils.RECORDERSERVICE_CID
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.recorderservice.R
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING

object DiskStorageChecker {

    const val TAG = "DisStorageChecker"

    private const val BYTES_RESERVED = (30 * 1024 * 1024).toLong()
    private const val BYTES_RESERVED_WHEN_RUNNING = (1 * 1024 * 1024).toLong()
    private const val ID_NOTIF_NO_SPACE = 2

    fun checkDiskWhenRunning(): Long {
        var mLastMaxTime = 0L
        if (RecordStatusManager.getCurrentStatus() != RECORDING) {
            return mLastMaxTime
        }
        var path: String? = null
        val space = StorageUtil.getAvailableSpace(BaseApplication.getAppContext())
        val bytesPerSecond = 2 * OppoRecorder.NWAV_BYTES_P_SEC // 16K
        if (space > BYTES_RESERVED_WHEN_RUNNING) {
            mLastMaxTime = (space - BYTES_RESERVED_WHEN_RUNNING) / bytesPerSecond
        } else {
            mLastMaxTime = 0
            processSpaceNotEnoughWhenCheckRunning(path)
        }
        return mLastMaxTime
    }

    fun checkLastMaxTime(): Long {
        DebugUtil.d(TAG, "setLastMaxTime start")
        val context = BaseApplication.getAppContext()
        var lastMaxTime = 0L
        var space: Long = 0
        val hasInternal = AddonAdapterCompatUtil.isInternalSdMounted(context)
        if (!hasInternal) {
            lastMaxTime = 0
            processSpaceNotEnoughWhenCheckLastMaxTime(context, com.soundrecorder.common.R.string.disk_unmount_v2)
            return lastMaxTime
        }

        space = StorageUtil.getAvailableSpace(context)
        val bytesPerSecond = 2 * OppoRecorder.NWAV_BYTES_P_SEC // 16K
        DebugUtil.d(TAG, " setLastMaxTime available space: $space")
        if (space > BYTES_RESERVED) {
            lastMaxTime = (space - BYTES_RESERVED) / bytesPerSecond
        } else {
            lastMaxTime = 0
            processSpaceNotEnoughWhenCheckLastMaxTime(
                context,
                if (FeatureOption.IS_PAD) {
                    com.soundrecorder.common.R.string.device_disk_full
                } else {
                    com.soundrecorder.common.R.string.disk_full
                }
            )
        }
        DebugUtil.i(TAG, "mLastMaxTime = $lastMaxTime")
        return lastMaxTime
    }

    fun checkDistBeforeStartRecord(): Boolean {
        val mContext = BaseApplication.getAppContext()
        val space = StorageUtil.getAvailableSpace(mContext)
        if (space < BYTES_RESERVED) {
            return false
        }
        return true
    }

    private fun processSpaceNotEnoughWhenCheckLastMaxTime(mContext: Context, showToastId: Int) {
        RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH)
        ToastManager.showShortToast(mContext, showToastId)
        sendStopRecordBroadCast()
    }

    private fun processSpaceNotEnoughWhenCheckRunning(path: String?) {
        RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH)
        if (BaseUtil.isAndroidQOrLater) {
            notifySystemNoSpace(false)
        } else {
            if (path != null) {
                notifySystemNoSpace(!path.contains(SettingsAdapter.getInstance().storagePhone))
            }
        }
        sendStopRecordBroadCast()
    }

    private fun sendStopRecordBroadCast() {
        val intent = Intent(RecorderDataConstant.ACTION_RECORDER_STOP_RECORDER)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun notifySystemNoSpace(isSD: Boolean) {
        DebugUtil.d(TAG, " notifySystemNoSpace isSD $isSD")
        var intent = Intent()
        intent = OplusCompactUtil.getActionForIntent(
            intent,
            OplusCompactConstant.LOWMEMORY_BROADCAST_ACTION_BEFOR,
            OplusCompactConstant.LOWMEMORY_BROADCAST_ACTION_AFTER
        )
        intent.putExtra("package", "com.coloros.soundrecorder")
        if (isSD) {
            intent.putExtra("space", "sd")
        } else {
            intent.putExtra("space", "Phone")
        }
        BaseApplication.getAppContext().sendBroadcast(intent, RecorderService.COMPONENT_SAFE_PERMISSION)
        showNotificationForNoSpace(BaseApplication.getAppContext(), isSD)
    }

    private fun showNotificationForNoSpace(context: Context, isSD: Boolean) {
        DebugUtil.i(TAG, "showNotificationForNoSpace")
        var titleId = com.soundrecorder.common.R.string.phone_no_storage
        var descId = com.soundrecorder.common.R.string.no_storage_desc
        if (isSD) {
            titleId = com.soundrecorder.common.R.string.sd_no_storage
        }
        if (FeatureOption.IS_PAD) {
            titleId = com.soundrecorder.common.R.string.device_no_storage
            descId = com.soundrecorder.common.R.string.device_no_storage_desc
        }
        val title: String = context.getString(titleId)
        val description: String = context.getString(descId)
        val nm = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        nm.notify(ID_NOTIF_NO_SPACE, getNotificationForN(title, description))
    }

    private fun getNotificationForN(title: String, discription: String): Notification? {
        val mContext = BaseApplication.getAppContext()
        var builder: Notification.Builder? = null
        builder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(mContext, RECORDERSERVICE_CID)
        } else {
            Notification.Builder(mContext)
        }
        builder.setAutoCancel(false)
        builder.setWhen(System.currentTimeMillis())
        builder.setShowWhen(true)
        builder.setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
        builder.setContentTitle(title)
        builder.setContentText(discription)
        if (RecordStatusManager.getCurrentStatus() == RECORDING) {
            builder.setOnlyAlertOnce(true)
        }
        return builder.build()
    }

    interface StorageSpaceCallback {
        //todo 调用stop（）
        fun onStorageNotEnough()
    }
}