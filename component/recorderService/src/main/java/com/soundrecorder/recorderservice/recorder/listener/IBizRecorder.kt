/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: IBizRecorder
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.recorder.listener

import android.media.MediaRecorder
import java.io.FileDescriptor
import java.io.IOException

interface IBizRecorder {
    fun start()
    fun stop()
    fun stopForPause()

    @Throws(IllegalStateException::class, IOException::class)
    fun prepare()
    fun release()
    fun getMaxAmplitude(): Int
    fun getTime(): Long
    fun setMaxFileSize(mLimit: Long)
    fun setOnInfoListener(listener: IOnInfoListener?)
    fun setOnErrorListener(listener: IOnErrorListener?)
    fun setMaxDuration(duration: Int)
    fun expandFile(fd: FileDescriptor, offset: Long, length: Long, audioSource: Int)
    fun expandFile(path: String, audioSource: Int)
    fun setAppendTime(time: Long)
//    fun setAudioSource(audioSource: Int)
//    fun setOutputFormat(format: Int)
//    fun setAudioSamplingRate(sampleRate: Int)
//    fun setAudioChannels(channels: Int)
//    fun setAudioEncoder(encoder: Int)
//    fun setAudioEncodingBitRate(encodingBitRate: Int)

    @Throws(IllegalStateException::class)
    fun setOutputFile(fd: FileDescriptor?)
    fun setOutputFile(file: String?)

    fun getRecorderSuffix(): String
    fun getRecorderMimeType(): String

    /**
     * 暂停播放是否是通过释放recorder实现
     * true：是，继续录制需要重新给recorder设置参数，调用start方法
     * false：否，继续录制不需要重新设置参数，如：mediaRecorder是通过pause、resume实现暂停、继续录制的
     */
    fun isPausedByResetRecorder(): Boolean

    fun getMediaRecorder(): MediaRecorder? {
        return null
    }
}