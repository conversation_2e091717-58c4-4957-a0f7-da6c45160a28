/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordResult
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

//currentAction 取值定义
//开始录音，接口值，调用startRecord 的时候，录音返回为这个值
const val CURRENT_ACTION_STARTRECORD = 0
//继续录音,  接口值，调用resumeRecord 的时候，录音返回为这个值
const val CURRENT_ACTION_RESUMERECORD = 1
//暂停录音， 接口值，调用pauseRecord 的时候，录音返回为这个值
const val CURRENT_ACTION_PAUSERECORD = 2
//停止录音， 接口值，调用stopRecord 的时候，录音返回为这个值
const val CURRENT_ACTION_STOPRECORD = 3
//保存录音， 接口值，调用saveRecord 的时候，录音返回该值
const val CURRENT_ACTION_SAVERECORD = 4

//currentResult 取值定义
//操作成功
const val CURRENT_RESULT_SUC = 0
//操作失败
const val CURRENT_RESULT_FAILED = 1


//errorCode 取值定义
const val RESULT_ERROR_CODE_DEFAULT = 0

//开始录音失败原因：媒体库插入失败
const val ERROR_CODE_STARTRECORD_MEDIADB_INSERT_ERROR = 1
//开始录音失败原因：多媒体Recorder错误，录音失败
const val ERROR_CODE_STARTRECORD_OPLUS_RECORD_ERROR = 2
//开始录音失败原因：文件找不到失败
const val ERROR_CODE_STARTRECORD_FILENOTFOUND = 3
//开始录音失败原因：存储空间不够
const val ERROR_CODE_STARTRECORD_DISK_NOT_ENOUGH = 4
//开始录音失败原因：录音权限未开
const val ERROR_CODE_STARTRECORD_RECORD_PERMISSION_NOT_GRANTED = 5
//开始录音失败原因：存储权限未开
const val ERROR_CODE_STARTRECORD_STORAGE_PERMISSION_NOT_GRANTED = 6
//开始录音失败原因：其他应用已经开始录音
const val ERROR_CODE_STARTRECORD_INVOKEERROR = 7
//开始录音失败原因：录音处于其他页面
const val ERROR_CODE_STARTRECORD_NOT_BROWSEFILE = 8

//录音非录制状态下调用暂停接口，返回错误
const val ERROR_CODE_RESUMERECORD_INVOKEERROR = 50
//测试开始录音失败原因：媒体库插入失败
const val ERROR_CODE_RESUMERECORD_MEDIADB_ERROR = 51
//继续录音失败原因：多媒体Recorder错误，录音失败
const val ERROR_CODE_RESUMERECORD_RECORDER_ERROR = 52
//继续录音失败原因：文件找不到失败
const val ERROR_CODE_RESUMERECORD_FILE_NOT_FOUND = 53
//继续录音失败原因：文件时长达到上限
const val ERROR_CODE_RESUMERECORD_FILE_DURATION_LIMIT_REACH = 54
//继续录音失败原因：文件大小达到上限
const val ERROR_CODE_RESUMERECORD_FILE_SIZE_LIMIT_REACH = 55
//继续录音失败原因：expandFile调用失败
const val ERROR_CODE_RESUMERECORD_EXPAND_FILE_ERROR = 56
//继续录音失败原因：重试次数超限失败
const val ERROR_CODE_RESUMERECORD_RETRY_EXEED_LIMIT = 57


//非录制状态下，调用暂停接口，返回错误
const val ERROR_CODE_PAUSE_INVOKEERROR = 100
// 继续录音失败原因：媒体库插入失败
const val ERROR_CODE_PAUSE_MEDIADB_ERROR = 101
// 继续录音失败原因：多媒体Recorder错误，录音失败
const val ERROR_CODE_PAUSE_RECORD_ERROR = 102



//停止录音失败ErrorCode
//150 --- 非录制状态下，调用停止接口，返回错误
const val ERROR_CODE_STOP_INVOKEERROR = 150
//151 --- 停止录音失败原因：媒体库插入失败
const val ERROR_CODE_STOP_MEDIADB_ERROR = 151
//152  --- 停止录音失败原因：多媒体Recorder错误，录音失败
const val ERROR_CODE_STOP_RECORDER_ERROR = 152
//153  --- 停止录音失败原因：文件找不到，保存失败
const val ERROR_CODE_STOP_FILENOTFOUND_ERROR = 153
//154  --- 停止录音失败原因：
const val ERROR_CODE_STOP_OTHER_ERROR = 154

@Parcelize
data class RecordResult(
    var currentAction: Int,
    var currentResult: Int = CURRENT_RESULT_SUC,
    var errorCode: Int = RESULT_ERROR_CODE_DEFAULT
) : Parcelable {
    fun isSuc(): Boolean {
        return currentResult == CURRENT_RESULT_SUC
    }
}
