/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : SeedlingStatusBar
 * * Description : 13.2泛在胶囊
 * * Version     : 1.0
 * * Date        : 2023/6/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import org.json.JSONObject

object SeedlingStatusBar : ISeedlingStatusBar {
    const val MAX_SHOW_DURATION = 1 * 60 * 60 * 1000L
    private const val TAG = "SeedlingStatusBar"

    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val showRunnable by lazy {
        Runnable {
            val ctx = BaseApplication.getApplication()
            DebugUtil.d(
                TAG,
                "showRunnable, from = ${RecordStatusBarUpdater.FROM_SHOW_RUNNABLE}",
                true
            )
            show(ctx, true, RecordStatusBarUpdater.FROM_SHOW_RUNNABLE)
        }
    }

    private var lastSeedlingDataInfo: SeedlingDataInfo? = null
    @Volatile
    var isShowing = false
        private set

    @JvmStatic
    fun refreshSeedlingData(from: String, forceRefresh: Boolean = false) {
        DebugUtil.d(
            TAG,
            "refreshSeedlingData from = $from,forceRefresh = $forceRefresh,show = $isShowing",
            true
        )
        if (!isShowing) return
        val newSeedlingDataInfo = createSeedlingDataInfo()
        if (!forceRefresh && lastSeedlingDataInfo == newSeedlingDataInfo) return
        seedingApi?.refreshSeedlingData(getSeedlingData(newSeedlingDataInfo))
    }

    @VisibleForTesting
    @JvmStatic
    fun createSeedlingDataInfo(): SeedlingDataInfo {
        return SeedlingDataInfo(RecorderViewModelApi.getAmplitudeCurrentTime())
    }

    @JvmStatic
    fun getSeedlingData(newSeedlingDataInfo: SeedlingDataInfo = createSeedlingDataInfo()): JSONObject {
        val jsonData = newSeedlingDataInfo.getChangedDataSimple()

        lastSeedlingDataInfo = newSeedlingDataInfo
        return jsonData
    }

    override fun show(ctx: Context, forceShow: Boolean, from: String) {
        DebugUtil.d(
            TAG,
            "seedlingStatusBar show, forceShow = $forceShow, isShowing = $isShowing, from = $from",
            true
        )
        if (forceShow || !isShowing) {
            isShowing = true
            seedingApi?.sendShowSeedlingStatusBar(null, null)
            register()
        }
    }

    override fun dismiss(ctx: Context, forceDismiss: Boolean, from: String) {
        DebugUtil.d(
            TAG,
            "seedlingStatusBar dismiss, forceDismiss = $forceDismiss, from = $from, isShowing = $isShowing",
            true
        )
        if (forceDismiss || isShowing) {
            isShowing = false
            seedingApi?.sendHideSeedlingStatusBar(forceDismiss, null)
            unRegister()
        }
    }

    @VisibleForTesting
    override fun register() {
        mainHandler.removeCallbacks(showRunnable)
        mainHandler.postDelayed(showRunnable, MAX_SHOW_DURATION)
    }

    @VisibleForTesting
    override fun unRegister() {
        mainHandler.removeCallbacksAndMessages(null)
    }

    override fun release() {
        lastSeedlingDataInfo = null
        isShowing = false
    }
}