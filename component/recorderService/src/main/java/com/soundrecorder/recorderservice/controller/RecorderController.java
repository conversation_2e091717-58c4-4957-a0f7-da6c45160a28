/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderController.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */
package com.soundrecorder.recorderservice.controller;

import android.os.Handler;
import android.os.Looper;
import androidx.annotation.NonNull;
import java.util.Timer;

import com.soundrecorder.recorderservice.RecordResult;
import com.soundrecorder.recorderservice.controller.model.RecordAmplitudeModel;
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver;
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver;
import com.soundrecorder.recorderservice.controller.observer.WaveObserver;
import com.soundrecorder.recorderservice.controller.worker.RecordSaveProcessor;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.recorderservice.controller.worker.WaveSampleWorker;
import com.soundrecorder.recorderservice.manager.RecordStatusManager;
import com.soundrecorder.recorderservice.manager.statusbar.SeedlingSavingTimer;
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource;

public class RecorderController extends AbsRecorderController<MaxAmplitudeSource> {

    private static final String TAG = "RecorderController";
    private final Handler mRecorderMainHandler = new Handler(Looper.getMainLooper());
    public boolean mIsNeedResult = false;
    private Timer mWorkerTimer;
    private WaveSampleWorker mWaveSampleWorker;
    private RecordSaveProcessor mRecordSaveProcesser;
    /*this model is used to save the recorder info which can used anywhere to update UI*/
    private RecordAmplitudeModel mRecorderAmplitudeModel;
    private SeedlingSavingTimer mSavingTimer;

    private RecorderController(@NonNull Builder builder) {
        registerControllerObserver(builder.getControllerObserver());
        registerRecordInfoSaveObserver(builder.getRecordInfoSaveObserver());
        registerWaveObserver(builder.getWaveObserver());
    }

    @Override
    public RecorderController initSample() {
        mRecorderAmplitudeModel = new RecordAmplitudeModel();
        initWaveSampleWorker();
        return this;
    }

    @Override
    public void startSample(int delay, int interval) {
        if (getWaveObserver() != null) {
            if (Looper.myLooper() != Looper.getMainLooper()) {
                mRecorderMainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        getWaveObserver().onStartSample();
                    }
                });
            } else {
                getWaveObserver().onStartSample();
            }
            //getWaveObserver().startSample();
        }

        if (mWorkerTimer != null) {
            mWorkerTimer.cancel();
            mWorkerTimer.purge();
            mWorkerTimer = new Timer();
        } else {
            mWorkerTimer = new Timer();
        }

        if (mWaveSampleWorker != null) {
            mWaveSampleWorker.cancel();
            mWaveSampleWorker = new WaveSampleWorker(RecorderController.this);
        } else {
            mWaveSampleWorker = new WaveSampleWorker(RecorderController.this);
        }

        mWorkerTimer.schedule(mWaveSampleWorker, delay, interval);
    }

    @Override
    public void stopSample() {
        DebugUtil.d(TAG, "stopSample");

        if (mWorkerTimer != null) {
            mWorkerTimer.cancel();
            mWorkerTimer.purge();
            mWorkerTimer = null;
        }

        if (mWaveSampleWorker != null) {
            mWaveSampleWorker.cancel();
            mWaveSampleWorker = null;
        }
        if (getWaveObserver() != null) {
            if (Looper.myLooper() != Looper.getMainLooper()) {
                mRecorderMainHandler.post(() -> getWaveObserver().onStopSample());
            } else {
                getWaveObserver().onStopSample();
            }
        }

        if (mSavingTimer != null) {
            mSavingTimer.cancelTimer();
        }
    }

    @Override
    public void release() {
        DebugUtil.d(TAG, "release");

        if (mWorkerTimer != null) {
            mWorkerTimer.cancel();
            mWorkerTimer.purge();
            mWorkerTimer = null;
        }

        if (mWaveSampleWorker != null) {
            mWaveSampleWorker.cancel();
            mWaveSampleWorker.release();
            mWaveSampleWorker = null;
        }

        if (mRecordSaveProcesser != null) {
            mRecordSaveProcesser.release();
            mRecordSaveProcesser = null;
        }

        if (mRecorderAmplitudeModel != null) {
            mRecorderAmplitudeModel = null;
        }

        super.release();
    }

    private void initWaveSampleWorker() {
        if (getControllerObserver() == null) {
            DebugUtil.e(TAG, "should register controller observer call back first!");
            return;
        }

        if (getWaveObserver() == null) {
            DebugUtil.e(TAG, "should register wave observer call back first!");
            return;
        }

        mWorkerTimer = new Timer();
        mWaveSampleWorker = new WaveSampleWorker(RecorderController.this);
    }

    @Override
    public RecordResult saveRecordInfo(String displayName, int saveRecordFromWhere, boolean trigSyncNow)  {
        DebugUtil.w(TAG, "saveRecordInfo,saveRecordFromWhere=" + saveRecordFromWhere);
        /*use this to save the record info through thread*/
        mRecordSaveProcesser = new RecordSaveProcessor(RecorderController.this);
        mSavingTimer = new SeedlingSavingTimer(RecorderController.this, saveRecordFromWhere);
        mSavingTimer.startTimer();
        RecordResult result = mRecordSaveProcesser.saveRecordInfo(displayName, saveRecordFromWhere, trigSyncNow);
        return result;
    }

    public RecordAmplitudeModel getRecorderAmplitudeModel() {
        return mRecorderAmplitudeModel;
    }

    public void setRecorderAmplitudeModel(@NonNull RecordAmplitudeModel model) {
        this.mRecorderAmplitudeModel = model;
        //post data and run in the main thread
        RecordStatusManager.setCurrentTimeMillis(model.getCurrentTimeMillis());
    }

    public static final class Builder {
        private ControllerObserver<MaxAmplitudeSource> mControllerObserver;
        private WaveObserver mWaveObserver;
        private RecordInfoSaveObserver mRecordInfoSaveObserver;

        private ControllerObserver<MaxAmplitudeSource> getControllerObserver() {
            return mControllerObserver;
        }

        public Builder setControllerObserver(@NonNull ControllerObserver<MaxAmplitudeSource> controllerObserver) {
            this.mControllerObserver = controllerObserver;
            return this;
        }

        private WaveObserver getWaveObserver() {
            return mWaveObserver;
        }

        public Builder setWaveObserver(WaveObserver waveObserver) {
            this.mWaveObserver = waveObserver;
            return this;
        }

        private RecordInfoSaveObserver getRecordInfoSaveObserver() {
            return mRecordInfoSaveObserver;
        }

        public Builder setRecordInfoSaveObserver(RecordInfoSaveObserver recordInfoSaveObserver) {
            this.mRecordInfoSaveObserver = recordInfoSaveObserver;
            return this;
        }

        public RecorderController build() {
            return new RecorderController(this);
        }
    }
}
