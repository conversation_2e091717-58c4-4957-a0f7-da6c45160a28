/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveObserver.java
 Description:
 Version: 1.0
 Date: 2020/1/7
 Author: liuyulong
 -----------Revision History-----------
 <author> <date> <version> <desc>
 liuyulong 2020/1/7 1.0 create
 */

package com.soundrecorder.recorderservice.controller.observer;

public interface WaveObserver {

    void onStartSample();

    void onStopSample();

    void onUpdateWave();
}
