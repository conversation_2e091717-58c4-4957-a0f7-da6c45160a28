<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.recorderservice">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk,com.oplus.keyguard.style.library"/>
    <uses-permission android:name="com.oplus.permission.safe.KEYGUARD" />

    <application>

        <service
            android:name="com.soundrecorder.recorderservice.RecorderService"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT"
            android:foregroundServiceType="microphone">
            <intent-filter>
                <action android:name="oplus.intent.action.RECORDER_SERVICE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
    </application>
</manifest>