package com.soundrecorder.recorderservice;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.recorderservice.recorder.AndroidMediaRecorder;
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import java.io.FileDescriptor;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class AndroidMediaRecorderTest {
    @Test
    public void should_returnNotNull_when_initWavFile() throws Exception {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        Whitebox.invokeMethod(androidMediaRecorder, "initWavFile");
        String mSuffix = Whitebox.getInternalState(androidMediaRecorder, "mSuffix");
        Assert.assertNotNull(mSuffix);
    }

    @Test
    public void should_returnNotNull_when_initAACFile() throws Exception {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        Whitebox.invokeMethod(androidMediaRecorder, "initAACFile");
        String mSuffix = Whitebox.getInternalState(androidMediaRecorder, "mSuffix");
        Assert.assertNotNull(mSuffix);
    }

    @Test
    public void should_returnTrue_when_expand() {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        androidMediaRecorder.expandFile("test", 1);
        androidMediaRecorder.expandFile(new FileDescriptor(), 1, 1, 1);
        boolean mAudioSourceSetted = Whitebox.getInternalState(androidMediaRecorder, "mAudioSourceSetted");
        Assert.assertTrue(mAudioSourceSetted);
    }

    @Test
    public void should_returnFalse_when_start() {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        androidMediaRecorder.start();
        androidMediaRecorder.setAppendTime(100l);
        androidMediaRecorder.setAudioSource(1);
        androidMediaRecorder.stop();
        boolean mAudioSourceSetted = Whitebox.getInternalState(androidMediaRecorder, "mAudioSourceSetted");
        Assert.assertFalse(mAudioSourceSetted);
    }

    @Test
    public void should_returnFalse_when_stopForPause() {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        androidMediaRecorder.start();
        androidMediaRecorder.stopForPause();
        boolean mAudioSourceSetted = Whitebox.getInternalState(androidMediaRecorder, "mAudioSourceSetted");
        Assert.assertFalse(mAudioSourceSetted);
    }

    @Test
    public void should_returnTrue_when_getMaxAmplitude() {
        AndroidMediaRecorder androidMediaRecorder = new AndroidMediaRecorder(2);
        Assert.assertTrue(androidMediaRecorder.getMaxAmplitude() == 0);
    }
}
