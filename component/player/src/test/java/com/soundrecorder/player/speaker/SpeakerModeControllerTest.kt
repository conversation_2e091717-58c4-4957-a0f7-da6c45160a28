/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SpeakerModeController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player.speaker

import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.player.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.Shadows.shadowOf
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SpeakerModeControllerTest {

    private var mActivity: FragmentActivity? = null

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(FragmentActivity::class.java).get()
    }

    @After
    fun tearDown() {
        mActivity = null
    }

    @Test
    fun should_inited_when_loadInitMode() {
        val controller = SpeakerModeController()
        controller.loadInitMode()
        shadowOf(Looper.getMainLooper()).idle()
        Assert.assertNotNull(controller.mSpeakerUiMode.value)
    }

    @Test
    fun should_success_when_performSpeakerMenuItemClick() {
        val controller = SpeakerModeController()
        ClickUtils.isQuickClick()
        controller.performSpeakerMenuItemClick(1)
    }

    @Test
    fun should_success_when_doWithBlueTooth() {
        val controller = SpeakerModeController()
        controller.doWithBlueTooth(SpeakerReceiver.HEADSET_CONNECTED)
        var headSetPluged = SpeakerStateManager.getInstance().isHeadSetPluged
        Assert.assertTrue(headSetPluged)

        controller.doWithBlueTooth(SpeakerReceiver.HEADSET_DISCONNECTED)
        headSetPluged = SpeakerStateManager.getInstance().isHeadSetPluged
        Assert.assertFalse(headSetPluged)
    }

    @Test
    fun should_success_when_doWithWired() {
        val controller = SpeakerModeController()
        controller.doWithWired(SpeakerReceiver.HEADSET_CONNECTED)
        var headSetPluged = SpeakerStateManager.getInstance().isHeadSetPluged
        Assert.assertTrue(headSetPluged)

        controller.doWithWired(SpeakerReceiver.HEADSET_DISCONNECTED)
        headSetPluged = SpeakerStateManager.getInstance().isHeadSetPluged
        Assert.assertFalse(headSetPluged)
    }

    @Test
    fun should_success_when_doWithReceiveNoisy() {
        val controller = SpeakerModeController()
        controller.doWithReceiveNoisy()

        val headSetPluged = SpeakerStateManager.getInstance().isHeadSetPluged
        Assert.assertFalse(headSetPluged)
    }

    @Test
    fun should_equals_when_getUIStateForSpStateAndHeadSetState() {
        val controller = SpeakerModeController()
        val methodName = "getUIStateForSpStateAndHeadSetState"
        var result = Whitebox.invokeMethod<Int>(
            controller,
            methodName,
            SpeakerStateManager.SP_SPEAKER_ON,
            true
        )
        Assert.assertEquals(SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET, result)

        result = Whitebox.invokeMethod<Int>(
            controller,
            methodName,
            SpeakerStateManager.SP_SPEAKER_OFF,
            true
        )
        Assert.assertEquals(SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET, result)

        result = Whitebox.invokeMethod<Int>(
            controller,
            methodName,
            SpeakerStateManager.SP_SPEAKER_ON,
            false
        )
        Assert.assertEquals(SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET, result)

        result = Whitebox.invokeMethod<Int>(
            controller,
            methodName,
            SpeakerStateManager.SP_SPEAKER_OFF,
            false
        )
        Assert.assertEquals(SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET, result)
    }

    @Test
    fun should_equals_when_updateUiWhenHeadSetPlugInOrOut() {
        val controller = SpeakerModeController()
        val methodName = "updateUiWhenHeadSetPlugInOrOut"
        Whitebox.invokeMethod<Int>(controller, methodName, true)
        val isSpeakerOn =
            Whitebox.getInternalState<MutableLiveData<Boolean>>(controller, "mIsSpeakerOn")
        Assert.assertNotNull(isSpeakerOn)
    }

    @Test
    fun should_equals_when_pausePlayWhenHeadSetPlugOut() {
        val controller = SpeakerModeController()
        Whitebox.invokeMethod<Int>(controller, "pausePlayWhenHeadSetPlugOut")
        val handler = Whitebox.getInternalState<Handler>(controller, "mMainHandler")
        Assert.assertNotNull(handler)
    }
}