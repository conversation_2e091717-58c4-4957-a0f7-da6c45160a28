/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CollectionInfoHeaderPreference.kt
 * * Description : 个人信息收集明示清单-筛选
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.AdapterView
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.preference.Preference
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.poplist.COUIClickSelectMenu
import com.coui.appcompat.poplist.COUIContextMenu.MenuShowStateListener
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.rotateview.COUIRotateView
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.CollectionMenuTime

class CollectionInfoHeaderPreference @JvmOverloads constructor(
    context: Context,
    attr: AttributeSet? = null
) : Preference(context, attr) {

    private val menuList = listOf(
        context.getString(R.string.collection_info_content_time_day7),
        context.getString(R.string.collection_info_content_time_month1),
        context.getString(R.string.collection_info_content_time_month3),
        context.getString(R.string.collection_info_content_time_year)
    )
    private val menuListDay = listOf(
        CollectionMenuTime.COLLECTION_TIME_DAY_7,
        CollectionMenuTime.COLLECTION_TIME_MONTH_1,
        CollectionMenuTime.COLLECTION_TIME_MONTH_3,
        CollectionMenuTime.COLLECTION_TIME_YEAR
    )
    private var mCurrIndex: Int = 0
    private var mCouiClickSelectMenu: COUIClickSelectMenu? = null
    private var mTvMenuDay: TextView? = null
    private var mRotateView: COUIRotateView? = null
    private var mRotateContainer: ConstraintLayout? = null

    init {
        layoutResource = R.layout.layout_collection_info_header
    }

    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        bindView(holder)
    }

    @VisibleForTesting
    fun bindView(holder: PreferenceViewHolder?) {
        val itemView = getItemView(holder)
        mTvMenuDay = itemView?.findViewById<TextView>(R.id.tv_recently_menu_day)
        mRotateView = itemView?.findViewById<COUIRotateView>(R.id.recently_rotate_view)
        mRotateContainer = itemView?.findViewById<ConstraintLayout>(R.id.cl_rotate_container)
        setMenuClickEvent(context)
    }

    @VisibleForTesting
    fun getItemView(holder: PreferenceViewHolder?): View? {
        return holder?.itemView
    }

    private fun setMenuClickEvent(context: Context) {
        if (mCouiClickSelectMenu == null) {
            mCouiClickSelectMenu = COUIClickSelectMenu(context)
        }
        addDataToMenu(context)
        mCouiClickSelectMenu?.setHelperEnabled(true)
        mCouiClickSelectMenu?.setOnPreciseClickListener { view, x, y ->
            if (view.id == R.id.cl_rotate_container) {
                addDataToMenu(context)
            }
        }

        mCouiClickSelectMenu?.setOnItemClickListener(AdapterView.OnItemClickListener { parent, view, position, id ->
            if (ClickUtils.isQuickClick()) {
                return@OnItemClickListener
            }
            mCurrIndex = position
            mTvMenuDay?.text = menuList[position]
            mOnMenuSelectDayListener?.onMenuSelectDay(menuListDay[position])
            mCouiClickSelectMenu?.dismiss()
        })

        mCouiClickSelectMenu?.setMenuShowStateListener(object : MenuShowStateListener {
            override fun onShow() {
                mRotateView?.startExpandAnimation()
                DebugUtil.d(TAG, "onClick = onShow")
            }

            override fun onDismiss() {
                mRotateView?.startCollapseAnimation()
                DebugUtil.d(TAG, "onClick = onDismiss")
            }
        })
    }

    private fun addDataToMenu(context: Context) {
        val popListItem = ArrayList<PopupListItem>()
        for (i in menuList.indices) {
            val builder = PopupListItem.Builder()
            builder.setTitle(menuList[i])
            builder.setIsEnable(true)
            builder.setIsChecked(i == mCurrIndex)
            popListItem.add(builder.build())
        }
        mRotateContainer?.let { mCouiClickSelectMenu?.registerForClickSelectItems(it, popListItem) }
        mCouiClickSelectMenu?.popup?.setUseBackgroundBlur(true)
    }

    companion object {
        const val TAG = "CollectionInfoHeaderPreference"
        const val SEVEN_DAY = 7
        const val ONE_MONTH = 30
        const val THREE_MONTH = 90
        const val ONE_YEAR = 365
    }

    private var mOnMenuSelectDayListener: OnMenuSelectDayListener? = null
    fun setOnMenuSelectDayListener(mOnMenuSelectDay: OnMenuSelectDayListener?) {
        this.mOnMenuSelectDayListener = mOnMenuSelectDay
    }

    interface OnMenuSelectDayListener {
        fun onMenuSelectDay(day: Int)
    }
}