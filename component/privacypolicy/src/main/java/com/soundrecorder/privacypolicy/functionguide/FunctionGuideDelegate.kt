/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FunctionGuideDelegate
 * Description:
 * Version: 1.0
 * Date: 2024/5/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/5/31 1.0 create
 */

package com.soundrecorder.privacypolicy.functionguide

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.UniDirectionalRecordUtils
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionGuideDelegate
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector

class FunctionGuideDelegate(val context: AppCompatActivity, private val functionClickOk: ((fromUserNotice: Boolean) -> Unit)?) :
    IFunctionGuideDelegate, DefaultLifecycleObserver {
    private var directSummaryGuideDialog: DirectSummaryGuideDialog? = null

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    override fun resumeShowFunctionDialog(fromUserNotice: Boolean) {
            if (directSummaryGuideDialog == null) {
                directSummaryGuideDialog = DirectSummaryGuideDialog {
                    // 点击知道了，保存SP值，下次不再弹
                    PermissionUtils.setFirstApplyFunctionIntroDialog(context.applicationContext)
                    functionClickOk?.invoke(fromUserNotice)
                }
                context.lifecycle.addObserver(this)
            }
            directSummaryGuideDialog?.showDialog(context)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        owner.lifecycle.removeObserver(this)
        directSummaryGuideDialog?.releaseDialog()
        super.onDestroy(owner)
        directSummaryGuideDialog = null
    }

    /**
     * ID:7667229 标题:【录音】【8/9解决】定向录音引导重复
     * 去掉定向录音弹窗
     */
    override fun canShowFunctionDialog(): Boolean {
//      return checkFunctionSupport() && !PermissionUtils.checkFunctionIntroDialogAlreadyApply(context.applicationContext)
        return false
    }

    private fun checkFunctionSupport(): Boolean {
        return (summaryApi?.getSupportRecordSummaryValue()?.value == true || UniDirectionalRecordUtils.isSupportDirectionalRecording(
            BaseApplication.getAppContext()))
    }
}