/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveViewEntityTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/14 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.entity

import android.content.Context
import android.os.Build
import android.view.ViewGroup
import android.widget.ImageView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.soundrecorder.breenocardlibrary.WaveViewEntity
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
@Ignore
class WaveViewEntityTest {

    private var mContext: Context? = null
    private var mWaveViewEntity: WaveViewEntity? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mWaveViewEntity = WaveViewEntity()
    }

    @Test
    fun createView() {
        mContext?.let { mWaveViewEntity?.createView(it) }
    }

    @Test
    fun onInVisible() {
        val view = ImageView(mContext)
        mContext?.let { mWaveViewEntity?.onInVisible(view) }
    }

    @Test
    fun onRelease() {
        val view = ImageView(mContext)
        mContext?.let { mWaveViewEntity?.onRelease(view) }
    }

    @Test
    fun onVisible() {
        val view = ImageView(mContext)
        mContext?.let { mWaveViewEntity?.onVisible(view) }
    }

    @Test
    fun setViewParams() {
        val view = ImageView(mContext)
        val viewGroup = Mockito.mock(ViewGroup::class.java)
        mContext?.let { mWaveViewEntity?.setViewParams(it, view, viewGroup) }
    }
}