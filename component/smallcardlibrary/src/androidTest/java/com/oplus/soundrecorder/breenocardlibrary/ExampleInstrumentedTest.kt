/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ExampleUnitTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author:
 -----------Revision History-----------
 <author> <date> <version> <desc>
 */

package com.oplus.soundrecorder.breenocardlibrary

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert.assertEquals

import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.oplus.soundrecorder.breenocardlibrary.test", appContext.packageName)
    }
}