/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardScaleTransition
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.transition

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.ViewGroup
import androidx.transition.Transition
import androidx.transition.TransitionValues

internal class AppCardScaleTransition(private vararg val ids: Int) : Transition() {
    companion object {
        private const val PROP_NAME_CHANGE_SCALE = "com.soundrecorder.breenocardlibrary:change:scale"
    }

    override fun captureEndValues(transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_SCALE] = view.scaleX
        }
    }

    override fun captureStartValues(transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_SCALE] = view.scaleX
        }
    }

    override fun createAnimator(sceneRoot: ViewGroup, startValues: TransitionValues?, endValues: TransitionValues?): Animator? {
        if (startValues == null || endValues == null || endValues.view == null) {
            return null
        }
        val view = endValues.view
        val startScale = startValues.values[PROP_NAME_CHANGE_SCALE] as Float
        val endScale = endValues.values[PROP_NAME_CHANGE_SCALE] as Float
        if (startScale != endScale) {
            val animator = ValueAnimator.ofFloat(startScale, endScale)
            animator.addUpdateListener { animation ->
                val value = animation.animatedValue as Float
                view.scaleX = value
                view.scaleY = value
            }
            return animator
        }
        return null
    }

    override fun getTransitionProperties() = arrayOf(PROP_NAME_CHANGE_SCALE)
}