package com.oplus.soundrecorder.breenocardlibrary.views.button;

import android.view.animation.PathInterpolator;

public class AppCardInEaseInterpolator extends PathInterpolator {
    private static final float CONTROL_X_1 = 0f;
    private static final float CONTROL_Y_1 = 0f;
    private static final float CONTROL_X_2 = 0.1f;
    private static final float CONTROL_Y_2 = 1.0f;

    public AppCardInEaseInterpolator() {
        super(CONTROL_X_1, CONTROL_Y_1, CONTROL_X_2, CONTROL_Y_2);
    }
}
