/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveView
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.animation.AccelerateInterpolator
import androidx.annotation.ColorInt
import androidx.annotation.VisibleForTesting
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.children
import androidx.core.view.updateLayoutParams
import com.oplus.soundrecorder.breenocardlibrary.R
import com.oplus.soundrecorder.breenocardlibrary.runnable.SmallCardRunnable
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.doOnLayoutChange
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.dp2px
import com.oplus.soundrecorder.breenocardlibrary.utils.WaveViewUtil
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min

@Suppress("TooGenericExceptionCaught")
internal class WaveView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : LinearLayoutCompat(context, attrs) {
    companion object {
        const val RECORD_INIT = 0
        const val RECORD_RECORDING = 1
        const val RECORD_STOPPED = 3
        const val AMP_MOVE_DURATION = 140L
        const val DEFAULT_AMP_MARGIN = 6
        const val AMP_MAX_HEIGHT = 60
        const val AMP_MIN_HEIGHT = 12
        const val AMP_MIN_WIDTH = 6
        const val AMP_MAX_WIDTH = 6


        const val DEFAULT_VALUE_INT = -1
        const val DEFAULT_AMP_RADIO = 20F
        const val DEFAULT_AMP_FIRST_PERCENT_F = 0.57F
        const val DEFAULT_AMP_SECOND_PERCENT_F = 0.72F
        const val DEFAULT_AMP_THIRD_PERCENT_F = 0.375F
        const val DEFAULT_AMP_FORTH_PERCENT_F = 0.47F
        const val DEFAULT_AMP_FIFTH_PERCENT_F = 0.28F
        const val DEFAULT_AMP_CENTER_PERCENT_F = 1F
        const val DEFAULT_AMP_RANDOM_PERCENT_F = -1F

        private const val SELF = 0
        private const val FIRST = 1
        private const val SECOND = 2
        private const val THREE = 3
        private const val FOUR = 4
        private const val FIVE = 5

        private const val MAX_SCALE = 2F
        private const val MID_SCALE = 3F
        private const val MIN_SCALE = 1F

        /* 波形最大高度的临界值*/
        private var mMaxScaleLineLen = 40f

        /* 波形中等高度的临界值*/
        private var mMidScaleLineLen = 10f

        /* 波形最小高度的临界值*/
        private var mMinScaleLineLen = 5f
    }

    private var drawingState = RECORD_INIT

    private var ampMinWidth = AMP_MIN_WIDTH
    private var ampWidth = AMP_MIN_WIDTH
    private var ampMaxWidth = AMP_MAX_WIDTH

    private var ampMinHeight = AMP_MIN_HEIGHT
    private var ampHeight = AMP_MIN_HEIGHT
    private var ampMaxHeight = AMP_MAX_HEIGHT

    private var ampMaxRandomHeight = AMP_MAX_HEIGHT

    private var ampMargin = DEFAULT_AMP_MARGIN
    private var ampAnimator: ValueAnimator? = null
    private var ampAnimatorDuration: Long = AMP_MOVE_DURATION
    private var lastStopHeight = 0F
    private var preAmpHeight = 0

    //波形的颜色和圆角大小
    private var ampColor: Int = DEFAULT_VALUE_INT
    private var defaultAmpColor: Int = ContextCompat.getColor(context, R.color.default_amp_color)
    private var ampRadius: Float = DEFAULT_AMP_RADIO

    //假波形数据列表
    private val currentRandomWaveHeightList = mutableListOf<Float>()

    //上一次假波形数据列表
    private val lastRandomWaveHeightList = mutableListOf<Float>()

    private val addWaveItemRunnable = SmallCardRunnable(this) {
        addWaveItemView()
    }

    init {
        val attributes = context.obtainStyledAttributes(attrs, R.styleable.WaveView)
        //获取最小最大宽度
        ampWidth = attributes.getDimension(R.styleable.WaveView_wave_view_width, resources.getDimension(R.dimen.record_wave_amp_width)).toInt()
        ampMinWidth = attributes.getDimension(R.styleable.WaveView_wave_view_min_width, resources.getDimension(R.dimen.record_wave_min_width)).toInt()
        ampMaxWidth = attributes.getDimension(R.styleable.WaveView_wave_view_max_width, resources.getDimension(R.dimen.record_wave_max_width)).toInt()
        //获取最小最大高度
        ampMaxHeight =
            attributes.getDimension(R.styleable.WaveView_wave_view_max_height, resources.getDimension(R.dimen.record_wave_max_height)).toInt()
        ampMinHeight =
            attributes.getDimension(R.styleable.WaveView_wave_view_min_height, resources.getDimension(R.dimen.record_wave_min_height)).toInt()
        //获取每个波形之间的间隔
        ampMargin = attributes.getDimension(R.styleable.WaveView_wave_view_margin, resources.getDimension(R.dimen.record_wave_amp_margin)).toInt()
        //获取波形高度变化的动画时长
        ampAnimatorDuration = attributes.getInt(R.styleable.WaveView_wave_view_animator_duration, AMP_MOVE_DURATION.toInt()).toLong()
        //波形颜色和圆角
        ampColor = attributes.getColor(R.styleable.WaveView_wave_view_amp_color, defaultAmpColor)
        ampRadius = attributes.getDimension(R.styleable.WaveView_wave_view_amp_radio, DEFAULT_AMP_RADIO)
        ampMaxRandomHeight = (ampHeight * DEFAULT_AMP_SECOND_PERCENT_F).toInt()
        gravity = Gravity.CENTER
        orientation = HORIZONTAL
        attributes.recycle()
        doOnLayoutChange { _, newRect, _ ->
            if (newRect.width() > 0) {
                post(addWaveItemRunnable)
            }
        }
    }

    @Synchronized
    fun setAmpAnimatorDuration(duration: Long) {
        if (duration <= 0 || (duration == ampAnimatorDuration)) {
            return
        }
        ampAnimatorDuration = duration
    }

    @Synchronized
    fun setAmpMargin(margin: Int) {
        if (margin < 0 || (margin == ampMargin)) {
            return
        }
        ampMargin = margin
    }

    @Synchronized
    fun setAmpMinWidth(minWidth: Int) {
        if ((minWidth < 0) || (minWidth == ampMinWidth) || (minWidth > ampMaxWidth)) {
            return
        }
        ampMinWidth = minWidth
    }

    @Synchronized
    fun setAmpMaxWidth(maxWidth: Int) {
        if (maxWidth < 0 || (maxWidth == ampMaxWidth) || (maxWidth < ampMinWidth)) {
            return
        }
        ampMaxWidth = maxWidth
    }

    @Synchronized
    fun setAmpMinHeight(minHeight: Int) {
        /*DebugUtil.i(TAG, "setAmpMinHeight >>>>>> minHeight = $minHeight, ampMinHeight = $ampMinHeight")
        if (minHeight < 0 || (minHeight == ampMinHeight) || (minHeight > ampMaxHeight)) {*/
        if (minHeight < 0) {
            return
        }
        val dp2px = context.dp2px(minHeight).toInt()
        if (dp2px == ampMinHeight) {
            return
        }
        ampMinHeight = dp2px
    }

    @Synchronized
    fun setAmpMaxHeight(maxHeight: Int) {
        /* DebugUtil.i(TAG, "setAmpMaxHeight >>>>>> maxHeight = $maxHeight")
         if (maxHeight < 0 || (maxHeight == ampMaxHeight) || (maxHeight < ampMinHeight)) {*/
        if (maxHeight <= 0 || (maxHeight == ampMaxHeight)) {
            return
        }
        val dp2px = context.dp2px(maxHeight).toInt()
        if (dp2px == ampMaxHeight) {
            return
        }
        ampMaxHeight = dp2px
    }

    @Synchronized
    fun setMaxRandomHeightPX(height: Int) {
//        DebugUtil.i(TAG, "setMaxRandomHeightPX >>>>>> height = $height")
        if (height < 0) {
            return
        }
        val offset = height - lastStopHeight
        val newHeight = (height + Math.random() * offset).toInt()
//        DebugUtil.i(TAG, "setMaxRandomHeightPX >>>>>> height = $height, offset = $offset, newHeight = $newHeight")
        ampMaxRandomHeight = max(newHeight, ampMinHeight)
    }

    @Synchronized
    fun setMaxRandomHeightDP(height: Int) {
//        DebugUtil.i(TAG, "setMaxRandomHeightDP >>>>>> height = $height")
        if (height < 0) {
            return
        }
        val dp2px = context.dp2px(height).toInt()
        if (dp2px == ampMaxRandomHeight) {
            return
        }
        ampMaxRandomHeight = max(dp2px, ampMinHeight)
    }

    @Synchronized
    private fun initRandomWaveHeight(startHeight: Float, endHeight: Float) {
        val count = childCount
        //记录上次的高度数据
        lastRandomWaveHeightList.clear()
        //如果未初始化,则使用默认值
        if (currentRandomWaveHeightList.isEmpty()) {
            val defaultHeight = ampMinHeight.toFloat()
            repeat((0 until count).count()) {
                currentRandomWaveHeightList.add(defaultHeight)
            }
        }
        lastRandomWaveHeightList.addAll(currentRandomWaveHeightList)
        //重置随机高度数据
        currentRandomWaveHeightList.clear()
        repeat((0 until count).count()) {
            val last = lastRandomWaveHeightList[it]
            val offset = (endHeight - startHeight)
            var newHeight = (Math.random() * offset + last).toFloat()
            if (newHeight > ampMaxHeight) {
                newHeight = ampMinHeight.toFloat()
            }
            if (newHeight < 0) {
                newHeight = 0F
            }
//            DebugUtil.i(TAG, "initRandomWaveHeight>>>>>>index=$it, last = $last, offset = $offset, newHeight = $newHeight")
            currentRandomWaveHeightList.add(newHeight)
        }
    }

    /**
     * @param startHeight   波形开始的高度
     * @param endHeight     波形结束的高度
     *
     * percentReal   真实波形高度占总高度的百分比  动画当前的值 / 新的值, X% -Y%,有可能大于100%,
     *          example:        假设真实波形是从 5 - 10变化， 那么 percentFake = x / 10, x从5 -10变化，percentReal:50%-100%
     *                          假设真实波形是从 10 - 5变化， 那么 percentFake = x / 5, x从10 - 5变化，percentReal:200%-100%
     * percentFake   假波形  (动画当前的值 - 旧的值) / (新的值 - 旧的值)， 0%-100%；
     *          example:        假设假波形是从 5 - 10变化， 那么 percentFake = (x - 5) / (10 - 5), x从5 -10变化，percentFake:0%-100%
     *                          假设假波形是从 10 - 5变化， 那么 percentFake = (x - 10) / (5 - 10), x从10 - 5变化，percentReal:0%-100%
     *
     */
    private fun startAmpAnimator(startHeight: Float, endHeight: Float, function: (Float, Float) -> Unit) {
//        DebugUtil.i(TAG, "startAmpAnimator startHeight = : $startHeight, endHeight = $endHeight")
        cancelAnimator()
        ampAnimator = ValueAnimator.ofFloat(startHeight, endHeight).also {
            it.addUpdateListener { animator ->
                val animatedValue = (animator.animatedValue ?: 0F) as Float
                //真实波形的百分比
                val percentReal = (animatedValue / endHeight)
                //假波形的百分比
                val percentFake = if (endHeight == startHeight) {
                    1F
                } else {
                    (animatedValue - startHeight) / (endHeight - startHeight)
                }
//                DebugUtil.e(TAG, "startAmpAnimator animatedValue = : $animatedValue, percentFake = $percentFake, percentReal = $percentReal")
                function(percentReal, percentFake)
            }
            it.duration = ampAnimatorDuration
            // 检查插值器
            it.interpolator = AccelerateInterpolator()
            it.doOnStart {
                initRandomWaveHeight(startHeight, endHeight)
            }
            it.doOnEnd {
                lastStopHeight = (ampAnimator?.animatedValue ?: 0F) as Float
                preAmpHeight = ampHeight

                lastRandomWaveHeightList.clear()
                currentRandomWaveHeightList.clear()
                children.iterator().forEach { view ->
                    lastRandomWaveHeightList.add(view.height.toFloat())
                    currentRandomWaveHeightList.add(view.height.toFloat())
                }
//                DebugUtil.e(TAG, "doOnEnd lastStopHeight = : $lastStopHeight")
            }
            it.doOnCancel {
                lastStopHeight = (ampAnimator?.animatedValue ?: 0F) as Float
//                DebugUtil.i(TAG, "doOnCancel lastStopHeight = : $lastStopHeight")
            }
            it.start()
        }
    }

    @Synchronized
    fun setAmpViewLayout(width: Int, height: Int) {
//        DebugUtil.i(TAG, "setAmpViewLayout width= : $width, height = $height")
        val w = if (width < ampMinWidth) {
            ampMinWidth
        } else if (width > ampMaxWidth) {
            ampMaxWidth
        } else {
            width
        }
        val h = if (height < ampMinHeight) {
            ampMinHeight
        } else if (width > ampMaxHeight) {
            ampMaxHeight
        } else {
            height
        }
        if (w == ampWidth && h == ampHeight) {
            return
        }
        if (drawingState == RECORD_RECORDING) {
            restartDrawAmp()
        }
    }

    @Synchronized
    fun setAmpHeight(height: Int) {
        if (height < 0) {
            return
        }
        ampHeight = getWaveHeight(height)
        setMaxRandomHeightPX(ampHeight)
        if (drawingState == RECORD_RECORDING) {
            restartDrawAmp()
        }
    }

    /**
     * 根据传递的上一个波形和当前波形，计算出一个相对差距较小的波形值，使整体波形看起来更平滑
     *
     * @param currAmpHeight     当前波形的时间高度
     *
     * preAmpHeight 上一个波形的时间高度
     *
     * @return 重新计算后的当前位置波形高度
     */
    @VisibleForTesting
    fun getWaveHeight(currAmpHeight: Int): Int {
//        DebugUtil.i(TAG, "getWaveHeight currAmpHeight= : $currAmpHeight， preAmpHeight = $preAmpHeight")

        var angle: Float
        val value: Int = (currAmpHeight + preAmpHeight) / WaveViewUtil.NUM_TWO
        angle = WaveViewUtil.AMPLITUDE_SCALE * value / WaveViewUtil.MAX_AMPLITUDE
        if (angle > 1) {
            angle = 1f
        }
        if (angle < 0) {
            angle = 0f
        }
        var height: Float = ampMaxHeight * angle

        if (height.compareTo(mMaxScaleLineLen) < 0) {
            height = if (height.compareTo(mMinScaleLineLen) < 0) {
                height * MAX_SCALE
            } else if (height.compareTo(mMidScaleLineLen) < 0) {
                height * MID_SCALE
            } else {
                height * MIN_SCALE
            }
        }
        if (height.compareTo(mMinScaleLineLen) <= 0) {
            height = ampMinHeight.toFloat()
        }
        preAmpHeight = currAmpHeight
//        DebugUtil.i(TAG, "getWaveHeight currAmpHeight= : $currAmpHeight， preAmpHeight = $preAmpHeight, height= : ${height.toInt()}")
        return height.toInt()
    }

    /**
     * 设置波形圆角半径
     */
    fun setAmpRadius(radio: Float) {
//        DebugUtil.i(TAG, "setAmpRadius radio = $radio")
        if ((radio < 0) || (radio == ampRadius)) {
            return
        }
        ampRadius = radio
        updateAmpBackground(updateRadius = true, updateColor = false)
    }

    @Suppress("TooGenericExceptionCaught")
    fun setAmpColor(@ColorInt color: Int) {
//        DebugUtil.i(TAG, "setAmpColor color = $color")
        try {
            ampColor = color
            updateAmpBackground(updateRadius = false, updateColor = true)
        } catch (e: Exception) {
            ampColor = defaultAmpColor
            updateAmpBackground(updateRadius = false, updateColor = true)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun setAmpColor(colorString: String) {
        if (colorString.isBlank()) {
            return
        }
        try {
            setAmpColor(Color.parseColor(colorString))
        } catch (e: Exception) {
            setAmpColor(ContextCompat.getColor(context, R.color.default_amp_color))
        }
    }

    private fun updateAmpBackground(updateRadius: Boolean, updateColor: Boolean) {
//        DebugUtil.i(TAG, "updateAmpBackground ampColor = $ampColor, ampRadius = $ampRadius, childCount = $childCount")
        children.iterator().forEach { view ->
            val drawable = (view.background) as? GradientDrawable
            drawable?.let {
                if ((updateColor) && (ampColor != DEFAULT_VALUE_INT)) {
                    it.setColor(ampColor)
                }
                if (updateRadius) {
                    it.cornerRadius = ampRadius
                }
            }
        }
    }

    @Synchronized
    fun startDrawAmp() {
//        DebugUtil.i(TAG, "startDrawAmp...")
        try {
            if (drawingState == RECORD_RECORDING) {
                return
            }
            cancelAnimator()
            drawingState = RECORD_RECORDING
            doStartAnimation()
        } catch (_: Exception) {
        }
    }

    @Synchronized
    private fun doStartAnimation() {
        if (drawingState == RECORD_RECORDING) {
            startAmpAnimator(lastStopHeight, ampHeight.toFloat(), object : (Float, Float) -> Unit {
                override fun invoke(percentReal: Float, percentFake: Float) {
                    prepareUpdateAmpViewLayout(percentReal, percentFake)
                }
            })
        }
    }

    @Synchronized
    fun stopDrawAmp() {
//        DebugUtil.i(TAG, "stopDrawAmp...")
        try {
            cancelAnimator()
            drawingState = RECORD_STOPPED
            doStopAnimation()
            lastRandomWaveHeightList.clear()
            currentRandomWaveHeightList.clear()
        } catch (_: Exception) {
        }
    }

    @Synchronized
    private fun doStopAnimation() {
        if (drawingState == RECORD_STOPPED) {
            setAmpViewLayout(ampMinWidth, ampMinHeight)
            startAmpAnimator(lastStopHeight, ampMinHeight.toFloat(), object : (Float, Float) -> Unit {
                override fun invoke(percentReal: Float, percentFake: Float) {
                    prepareUpdateAmpViewLayout(percentReal, percentFake)
                }
            })
        }
    }

    @Synchronized
    fun restartDrawAmp() {
        drawingState = RECORD_RECORDING
        cancelAnimator()
        doStartAnimation()
    }

    @Synchronized
    fun cancelAnimator() {
        ampAnimator?.apply {
            if (isRunning) {
                cancel()
            }
        }
    }

    /**
     * @param percentReal   真实波形高度占总高度的百分比  动画当前的值 / 新的值, X% -Y%,有可能大于100%,
     *          example:        假设真实波形是从 5 - 10变化， 那么 percentFake = x / 10, x从5 -10变化，percentReal:50%-100%
     *                          假设真实波形是从 10 - 5变化， 那么 percentFake = x / 5, x从10 - 5变化，percentReal:200%-100%
     * @param percentFake   假波形  (动画当前的值 - 旧的值) / (新的值 - 旧的值)， 0%-100%；
     *          example:        假设假波形是从 5 - 10变化， 那么 percentFake = (x - 5) / (10 - 5), x从5 -10变化，percentFake:0%-100%
     *                          假设假波形是从 10 - 5变化， 那么 percentFake = (x - 10) / (5 - 10), x从10 - 5变化，percentReal:0%-100%
     * @param needScale     波形是否需要缩放(中间波形高，两侧波形依次递减),false ->所有波形整齐排列
     * @param outRange      是否允许波形超出最大最小宽高的限制
     */
    @VisibleForTesting
    fun prepareUpdateAmpViewLayout(percentReal: Float, percentFake: Float, needScale: Boolean = true, outRange: Boolean = false) {
        val totalCount = childCount
        if (totalCount == 1) {
//            DebugUtil.i(TAG, "prepareUpdateAmpViewLayout >>> 只有一个child")
            doUpdateAmpViewLayout(
                0,
                calculateRealAmpWidth(percentReal, needScale, DEFAULT_AMP_CENTER_PERCENT_F, outRange),
                calculateRealAmpHeight(percentReal, needScale, DEFAULT_AMP_CENTER_PERCENT_F, outRange)
            )
        } else {
            var waveWidth: Int
            var waveHeight: Int
            //得到中心波形的index，因为index从0开始计算，所以向下取整数
            val centerIndex = floor(totalCount.toDouble() / 2).toInt()
//            DebugUtil.i(TAG, "prepareUpdateAmpViewLayout >>> childCount = $totalCount,centerIndex = $centerIndex, percentReal = $percentReal, percentFake = $percentFake")
            for (index in 0 until totalCount) {
                val scalePercent = getScalePercentByIndex(index, centerIndex)
                if (scalePercent == DEFAULT_AMP_RANDOM_PERCENT_F) {
                    waveWidth = calculateFakeAmpWidth()
                    waveHeight = calculateFakeAmpHeight(index, percentFake, needScale, outRange)
                } else {
                    waveWidth = calculateRealAmpWidth(percentReal, needScale, scalePercent, outRange)
                    waveHeight = calculateRealAmpHeight(percentReal, needScale, scalePercent, outRange)
                }
                doUpdateAmpViewLayout(index, waveWidth, waveHeight)
            }
        }
    }

    @VisibleForTesting
    fun getScalePercentByIndex(index: Int, centerIndex: Int): Float {
        return when (abs(centerIndex - index)) {
            SELF -> {
                //绘制中心波形本身
                DEFAULT_AMP_CENTER_PERCENT_F
            }
            FIRST -> {
                //绘制中心波形旁边第一个波形
                DEFAULT_AMP_FIRST_PERCENT_F
            }
            SECOND -> {
                //绘制中心波形旁边第二个波形
                DEFAULT_AMP_SECOND_PERCENT_F
            }
            THREE -> {
                //绘制中心波形旁边第三个波形
                DEFAULT_AMP_THIRD_PERCENT_F
            }
            FOUR -> {
                //绘制中心波形旁边第四个波形
                DEFAULT_AMP_FORTH_PERCENT_F
            }
            FIVE -> {
                //绘制中心波形旁边第五个波形
                DEFAULT_AMP_FIFTH_PERCENT_F
            }
            else -> {
                //绘制其他假波形
                DEFAULT_AMP_RANDOM_PERCENT_F
            }
        }
    }

    @VisibleForTesting
    fun doUpdateAmpViewLayout(index: Int, newWidth: Int, newHeight: Int) {
//        DebugUtil.i(TAG, "doUpdateAmpViewLayout >>> index = $index, newWidth = $newWidth, newHeight = $newHeight")
        getChildAt(index)?.updateLayoutParams<LayoutParams> {
            width = newWidth
            height = newHeight
        }
    }

    @Synchronized
    private fun calculateRealAmpWidth(percent: Float, needScale: Boolean, scalePercent: Float, outRange: Boolean): Int {
        var newWidth = if (needScale) {
            (ampWidth * percent * scalePercent).toInt()
        } else {
            (ampWidth * percent).toInt()
        }
        return if (outRange) {
            newWidth
        } else {
            newWidth = min(newWidth, ampMaxWidth)
            newWidth = max(newWidth, ampMinWidth)
            newWidth
        }
    }

    @Synchronized
    private fun calculateRealAmpHeight(percent: Float, needScale: Boolean, scalePercent: Float, outRange: Boolean): Int {
        var newHeight = if (needScale) {
            (ampHeight * percent * scalePercent).toInt()
        } else {
            (ampHeight * percent).toInt()
        }
        return if (outRange) {
            newHeight
        } else {
            newHeight = min(newHeight, ampMaxHeight)
            newHeight = max(newHeight, ampMinHeight)
            newHeight
        }
    }

    @VisibleForTesting
    @Synchronized
    fun calculateFakeAmpWidth(): Int {
        return ampMinWidth
    }

    @VisibleForTesting
    @Synchronized
    @Suppress("TooGenericExceptionCaught")
    fun calculateFakeAmpHeight(index: Int, percentFake: Float, needScale: Boolean, outRange: Boolean): Int {
        return try {
            val last = lastRandomWaveHeightList[index]
            val current = currentRandomWaveHeightList[index]
//            DebugUtil.i(TAG, "calculateFakeAmpHeight>>>index= $index, percentFake = $percentFake, last = $last, current = $current")
            return if (needScale) {
                var height = ((current - last) * percentFake + last).toInt()
                if (outRange) {
                    height
                } else {
                    height = min(height, ampMaxHeight)
                    height = max(height, ampMinHeight)
                    height
                }
            } else {
                current.toInt()
            }
        } catch (e: Exception) {
//            DebugUtil.w(TAG, "calculateFakeAmpHeight >>> index = $index, ERROR = ${e.message}")
            ampMinHeight
        }
    }

    private fun addWaveItemView() {
        removeAllViews()
        var number = floor(width.toDouble() / (ampWidth + ampMargin)).toInt()
        if (number % 2 == 0) {
            number -= 1
        }
        for (index in 0 until number) {
            val layoutParams = LayoutParams(ampMinWidth, ampMinHeight)
            layoutParams.gravity = Gravity.CENTER

            if (index % 2 == 0) {
                layoutParams.marginStart = 0
                layoutParams.marginEnd = 0
            } else {
                layoutParams.marginStart = ampMargin
                layoutParams.marginEnd = ampMargin
            }
            val itemView = View(context)
            val drawable = ResourcesCompat.getDrawable(resources, R.drawable.wave_amp_bg, context.theme)
            (drawable as? GradientDrawable)?.apply {
                if (ampColor != DEFAULT_VALUE_INT) {
                    setColor(ampColor)
                }
                cornerRadius = ampRadius
            }
            itemView.background = drawable
            itemView.layoutParams = layoutParams
            itemView.tag = index
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                itemView.isForceDarkAllowed = false
            }
            addView(itemView)
        }
        gravity = Gravity.CENTER
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        cancelAnimator()
        super.onConfigurationChanged(newConfig)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        removeCallbacks(addWaveItemRunnable)
    }
}