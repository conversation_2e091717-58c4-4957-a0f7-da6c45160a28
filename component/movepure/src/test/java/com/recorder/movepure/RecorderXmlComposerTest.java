package com.recorder.movepure;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.robolectric.annotation.Config;

import com.recorder.movepure.shadows.ShadowBaseUtils;
import com.recorder.movepure.shadows.ShadowFeatureOption;
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowBaseUtils.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RecorderXmlComposerTest {
    @Test
    public void should_returnFalse_when_startCompose_and_endCompose() {
        RecorderXmlComposer recorderXmlComposer = new RecorderXmlComposer();
        Assert.assertTrue(recorderXmlComposer.startCompose());
        Assert.assertTrue(recorderXmlComposer.endCompose());
    }

    @Test
    public void should_notNull_when_getXmlInfo() {
        RecorderXmlComposer recorderXmlComposer = new RecorderXmlComposer();
        Assert.assertNull(recorderXmlComposer.getXmlInfo());
    }
}

