package com.recorder.movepure

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.common.db.PictureMarkDbUtils
import com.soundrecorder.common.db.RecorderDatabaseHelper
import com.recorder.movepure.shadows.ShadowBaseUtils
import com.recorder.movepure.shadows.ShadowFeatureOption
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.db.UploadDbUtil
import org.junit.*
import org.junit.Assert.assertEquals
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class BackupFileUtilTest {

    private var mContext: Context? = null
    private var mMockedBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockedBaseApplication?.`when`<Context> {
            BaseApplication.getAppContext()
        }?.thenReturn(mContext)

        val dbHelper = RecorderDatabaseHelper(mContext)
        val db = dbHelper.writableDatabase
        Whitebox.invokeMethod<RecorderDatabaseHelper>(dbHelper, "createConvertVadTable", db)
        db.close()
    }

    @After
    fun tearDown() {
        val dbHelper = RecorderDatabaseHelper(mContext)
        val db = dbHelper.writableDatabase
        db.execSQL("DROP TABLE IF EXISTS ${DatabaseConstant.TABLE_NAME_CONVERT};")
        db.close()
        mMockedBaseApplication?.close()
        mMockedBaseApplication = null
        mContext = null
    }

    @Test
    fun estimateConvertRecordSizeTest() {
        val size1 = BackupFileUtil.estimateConvertRecordSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size1)

        val data: MutableList<ConvertRecord> = arrayListOf()
        val record1 = ConvertRecord(1)
        val record2 = ConvertRecord(2)
        record1.speakerRoleOriginalNumber = 10000
        record2.speakerRoleOriginalNumber = 200000

        val uploadRecordList1: MutableList<UploadRecord> = arrayListOf()
        val uploadRecordList2: MutableList<UploadRecord> = arrayListOf()
        uploadRecordList1.add(UploadRecord(0, "", 0, 0, "etag_1", "", 1))
        uploadRecordList2.add(UploadRecord(0, "", 0, 0, "etag_2", "", 2))
        record1.uploadRecordList = uploadRecordList1
        record2.uploadRecordList = uploadRecordList2

        data.add(record1)
        data.add(record2)
        val result1 = ConvertDbUtil.insert(record1)
        val result2 = ConvertDbUtil.insert(record2)
        val resultAll = ConvertDbUtil.selectAll()

        val size2 = BackupFileUtil.estimateConvertRecordSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size2)
        println("----------result1:$result1 result2:$result2 resultAll:$resultAll")
    }

    @Test
    fun estimateUploadSizeTest() {
        val size1 = BackupFileUtil.estimateUploadSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size1)

        val uploadRecordList1: MutableList<UploadRecord> = arrayListOf()
        val uploadRecordList2: MutableList<UploadRecord> = arrayListOf()
        uploadRecordList1.add(UploadRecord(0, "", 0, 0, "etag_1", "", 1))
        uploadRecordList2.add(UploadRecord(0, "", 0, 0, "etag_2", "", 2))
        UploadDbUtil.insertUploadRecords(mContext, uploadRecordList1)

        val size2 = BackupFileUtil.estimateUploadSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size2)
    }

    @Test
    fun estimatePictureMarkSizeTest() {
        val size1 = BackupFileUtil.estimatePictureMarkSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size1)

        val bean = MarkDataBean(111, 2)
        PictureMarkDbUtils.addPictureMark("key1", bean)
        val size2 = BackupFileUtil.estimatePictureMarkSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size2)
    }

    @Test
    fun estimateKeywordSizeTest() {
        val size1 = BackupFileUtil.estimateKeywordSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size1)

        val keyWord = KeyWord("测试", 0.5f)
        KeyWordDbUtils.addKeyWord(keyWord, 1)
        val size2 = BackupFileUtil.estimateKeywordSize()
        assertEquals(BackupFileUtil.DEFAULT_SIZE, size2)
    }

}