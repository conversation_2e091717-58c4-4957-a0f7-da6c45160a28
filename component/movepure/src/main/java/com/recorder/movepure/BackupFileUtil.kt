package com.recorder.movepure

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.PictureMarkDbUtils
import com.soundrecorder.common.db.PictureMarkDbUtils.queryAllPictureMarks
import com.soundrecorder.common.db.UploadDbUtil.getAllUploadRecords
import com.soundrecorder.common.utils.ConvertDbUtil

object BackupFileUtil {

    /**
     * <?xml version='1.0' standalone='no' ?><picture_mark />
     * 文件头默认大小
     */
    const val DEFAULT_SIZE = 50L
    private const val TAG = "BackupFileUtil"
    private const val CONVERT_RECORD_COLUMNS = 20
    private const val UPLOAD_COLUMNS = 5
    private const val PICTURE_MARK_COLUMNS = 7
    private const val KEYWORD_COLUMNS = 5
    private const val GROUP_INFO_COLUMNS = 18

    fun estimateConvertRecordSize(): Long {
        var size = DEFAULT_SIZE
        val data = ConvertDbUtil.selectAll()
        if (data.isEmpty()) {
            DebugUtil.i(TAG, "ConvertRecord size:$size")
            return size
        }
        for (record in data) {
            val recordId: Long = record.recordId
            if (!FileUtils.isFileExist(MediaDBUtils.genUri(recordId))) {
                continue
            }
            size += (DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER.length
                    + record.speakerRoleOriginalNumber.toString().length) * CONVERT_RECORD_COLUMNS
        }
        DebugUtil.i(TAG, "ConvertRecord size:$size")
        return size
    }

    fun estimateUploadSize(): Long {
        var size = DEFAULT_SIZE
        val data: List<UploadRecord> = getAllUploadRecords(BaseApplication.getAppContext())
        if (data.isNotEmpty()) {
            for (uploadRecord in data) {
                size += (DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START.length
                        + uploadRecord.mFileStartRange.toString().length) * UPLOAD_COLUMNS
            }
        }
        DebugUtil.i(TAG, "Upload size:$size")
        return size
    }

    fun estimatePictureMarkSize(): Long {
        var size = DEFAULT_SIZE
        val markDataBeanList: List<MarkDataBean> = queryAllPictureMarks()
        if (markDataBeanList.isNotEmpty()) {
            for (markDataBean in markDataBeanList) {
                size += (PictureMarkDbUtils.PICTURE_FILE_PATH.length + markDataBean.pictureFilePath.length) * PICTURE_MARK_COLUMNS
            }
        }
        DebugUtil.i(TAG, "PictureMark size:$size")
        return size
    }

    fun estimateKeywordSize(): Long {
        var size = DEFAULT_SIZE
        val keyWords = KeyWordDbUtils.queryAllKeyWords()
        if (keyWords.isNotEmpty()) {
            for (keyWord in keyWords) {
                if (!FileUtils.isFileExist(MediaDBUtils.genUri(keyWord.recordId))) {
                    continue
                }
                size += (KeyWordDbUtils.MEDIA_PATH.length + keyWord.mediaPath.length) * KEYWORD_COLUMNS
            }
        }
        DebugUtil.i(TAG, "Keyword size:$size")
        return size
    }
    @JvmStatic
    fun estimateGroupInfoSize(): Long {
        var size = DEFAULT_SIZE
        val data: List<GroupInfo> = GroupInfoDbUtil.getAllGroupInfoList(BaseApplication.getAppContext())
        if (data.isNotEmpty()) {
            for (groupInfo in data) {
                size += (DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME.length
                        + groupInfo.mGroupName.length) * GROUP_INFO_COLUMNS
            }
        }
        DebugUtil.i(TAG, "groupInfo size:$size")
        return size
    }
}