/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: RecorderXmlComposer
 ** Description:
 ** Version:
 ** Date :
 ** Author: chenlipeng W9001067
 **
 ** v1.0, 2019-3-12, chenlipeng W9001067, create
 ****************************************************************/
package com.recorder.movepure;

import android.text.TextUtils;
import android.util.Xml;

import org.xmlpull.v1.XmlSerializer;

import java.io.IOException;
import java.io.StringWriter;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.Record;

public class RecorderXmlComposer {
    private final String TAG = "RecorderXmlComposer";
    private XmlSerializer mSerializer = null;
    private StringWriter mStringWriter = null;

    public boolean startCompose() {
        boolean result = false;
        mSerializer = Xml.newSerializer();
        mStringWriter = new StringWriter();
        try {
            mSerializer.setOutput(mStringWriter);
            mSerializer.startDocument(null, false);
            mSerializer.startTag("", "recorder");
            result = true;
        } catch (IOException | IllegalStateException | IllegalArgumentException e) {
            e.printStackTrace();
        }

        return result;
    }

    public boolean endCompose() {
        boolean result = false;
        try {
            mSerializer.endTag("", "recorder");
            mSerializer.endDocument();
            result = true;
        } catch (IllegalArgumentException | IOException | IllegalStateException e) {
            e.printStackTrace();
        }

        return result;
    }

    public void addOneRecorderRecord(Record record) {
        try {
            mSerializer.startTag("", DatabaseConstant.ROOT);
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, TextUtils.isEmpty(record.getUuid()) ? "" : record.getUuid());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, TextUtils.isEmpty(record.getData()) ? "" : record.getData());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE, Long.toString(record.getFileSize()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME, TextUtils.isEmpty(record.getDisplayName()) ? "" : record.getDisplayName());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE, TextUtils.isEmpty(record.getMimeType()) ? "" : record.getMimeType());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED, Long.toString(record.getDateCreated()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED, Long.toString(record.getDateModied()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE, Long.toString(record.getRecordType()));

            if (record.getMarkData() != null && record.getMarkData().length != 0) {
                mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA, new String(record.getMarkData(), "ISO-8859-1"));
            }
            if (record.getAmpData() != null && record.getAmpData().length != 0) {
                mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA, new String(record.getAmpData(), "ISO-8859-1"));
            }

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION, Long.toString(record.getDuration()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME, TextUtils.isEmpty(record.getBuckedDisplayName()) ? "" : record.getBuckedDisplayName());

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, Long.toString(record.getDirty()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, Boolean.toString(record.isDeleted()));

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5, TextUtils.isEmpty(record.getMD5()) ? "" : record.getMD5());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID, TextUtils.isEmpty(record.getFileId()) ? "" : record.getFileId());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, TextUtils.isEmpty(record.getGlobalId()) ? "" : record.getGlobalId());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, Long.toString(record.getSysVersion()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD, TextUtils.isEmpty(record.getCheckPayload()) ? "" : record.getCheckPayload());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE, Integer.toString(record.getSyncType()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, Integer.toString(record.getSyncStatus()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS, Integer.toString(record.getSyncDownlodStatus()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE, Integer.toString(record.getErrorCode()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL, Integer.toString(record.getLevel()));

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS, Integer.toString(record.getEditStatus()));

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE, Long.toString(record.getSyncDate()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT, Long.toString(record.getFailedCount()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME, Long.toString(record.getLastFailedTime()));

            if (record.getRelativePath() != null) {
                mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH, record.getRelativePath());
            }

            if (record.getAmpFilePath() != null) {
                mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH, record.getAmpFilePath());
            }

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, Integer.toString(record.getSyncPrivateStatus()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS, Integer.toString(record.getSyncMigrateStatus()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING, Integer.toString(record.getIsMarkListShowing()));

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON, Boolean.toString(record.getDirectOn()));
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME,
                    TextUtils.isEmpty(record.getDirectTime()) ? "" : record.getDirectTime());

            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID,
                    TextUtils.isEmpty(record.getGroupUuid()) ? "" : record.getGroupUuid());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_CALLER_NAME,
                    TextUtils.isEmpty(record.getCallerName()) ? "" : record.getCallerName());
            mSerializer.attribute("", DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME,
                    TextUtils.isEmpty(record.getOriginalName()) ? "" : record.getOriginalName());
            mSerializer.attribute("", PictureMarkDbUtils.TEMP_ID, String.valueOf(record.getId()));
            mSerializer.endTag("", DatabaseConstant.ROOT);
        } catch (IllegalArgumentException | NullPointerException | IOException | IllegalStateException e) {
            e.printStackTrace();
        }

    }

    public String getXmlInfo() {
        try {
            if (mStringWriter != null) {
                String info = mStringWriter.toString();
                mStringWriter.close();
                return info;
            }
        } catch (IOException e) {
            DebugUtil.e(TAG, "mStringWriter close IOException error:" + e);
        }
        return null;
    }
}