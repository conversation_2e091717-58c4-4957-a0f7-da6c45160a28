<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:id="@+id/play_btn_area"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp40"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true">
        <ImageView
            android:id="@+id/play_btn"
            android:layout_alignParentBottom="true"
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp32" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/left_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp12"
        android:layout_toStartOf="@id/play_btn_area">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:lines="1"
            android:textColor="@color/color_notification_play_title"
            android:textSize="@dimen/sp14"
            android:textStyle="bold"
            tools:text="测试标题" />


        <TextView
            android:id="@+id/timestamp_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/title_tv"
            android:layout_marginTop="@dimen/dp2"
            android:ellipsize="end"
            android:lines="1"
            android:textColor="@color/color_notification_timestamp"
            android:textSize="@dimen/sp14"
            tools:text="00:12/12:33" />

    </RelativeLayout>

</RelativeLayout>