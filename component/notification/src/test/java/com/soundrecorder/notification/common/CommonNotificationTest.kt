/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.screenstate.ScreenStateLiveData
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.CommonNotificationModel
import com.soundrecorder.notification.base.BaseNotification
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CommonNotificationTest {

    @Test
    fun should_notNull_when_playNameObserver() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val playNameObserver: Observer<String> = Whitebox.getInternalState(notification, "playNameObserver")
        playNameObserver.onChanged("123")
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_notNull_when_curTimeObserver() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val curTimeObserver: Observer<Long> = Whitebox.getInternalState(notification, "curTimeObserver")
        curTimeObserver.onChanged(1000)
        Assert.assertNull(notification.notification)
    }

    @Test
    fun should_notNull_when_playStatusObserver() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val playStatusObserver: Observer<Int> = Whitebox.getInternalState(notification, "playStatusObserver")
        playStatusObserver.onChanged(1)
        Assert.assertNull(notification.notification)
    }

    @Test
    fun should_notNull_when_isBtnDisabledObserver() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertNull(notification.notification)
        Whitebox.setInternalState(notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        val isBtnDisabledObserver: Observer<Boolean> = Whitebox.getInternalState(notification, "isBtnDisabledObserver")
        isBtnDisabledObserver.onChanged(false)
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_notNull_when_isMarkEnabledObserver() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        val markPoint = MutableLiveData(0L)
        notification.notificationModel = CommonNotificationModel().also {
            it.markPoint = markPoint
        }
        Assert.assertNull(notification.notification)

        Whitebox.setInternalState(notification, "refreshState",
            BaseNotification.REFRESH_STATE_ENABLED
        )
        markPoint.value = 1000
        val isMarkEnabledObserver: Observer<Boolean> = Whitebox.getInternalState(notification, "isMarkEnabledObserver")
        isMarkEnabledObserver.onChanged(false)
        Assert.assertNotNull(notification.notification)
    }

    @Test
    fun should_notNull_when_refreshNotificationAfterTime() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(notification, "refreshNotificationAfterTime")
        Assert.assertNotNull(Whitebox.getInternalState(notification, "timerTask"))
    }

    @Test
    fun should_null_when_stopTimer() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(notification, "stopTimer")
        Assert.assertNull(Whitebox.getInternalState(notification, "timer"))
    }

    @Test
    fun should_return_true_when_observeData() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.observeData()
        Assert.assertEquals(true,
            Whitebox.getInternalState<ScreenStateLiveData>(notification, "screenStateLiveData")
                .hasObservers()
        )
    }

    @Test
    fun should_null_when_onRelease() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.onRelease()
        Assert.assertNull(Whitebox.getInternalState(notification, "timer"))
    }

    @Test
    fun should_equals_when_getContentTitle() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.notificationModel = CommonNotificationModel().also {
            it.setPlayName(MutableLiveData("123"))
        }
        val title: String = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentTitle").first
        Assert.assertEquals("123", title)
    }

    @Test
    fun should_equals_when_getContentText() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.notificationModel = CommonNotificationModel().also {
            it.setCurTime(MutableLiveData(1000))
            it.playDuration = 3000
        }
        val text: String = Whitebox.invokeMethod<Pair<String, String>>(notification, "getContentText").first
        Assert.assertEquals("00:01 / 00:03", text)
    }

    @Test
    fun should_equals_when_isBtnEnabled() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.notificationModel = CommonNotificationModel().also {
            it.setBtnDisabled(MutableLiveData(false))
        }
        Assert.assertEquals(true, Whitebox.invokeMethod(notification, "isBtnEnabled"))
    }

    @Test
    fun should_equals_when_initNotification() {
        Settings.Global.putInt(BaseApplication.getApplication().contentResolver, "oplus_system_folding_mode", 0)
        val mockFeatureOption = Mockito.mockStatic(FeatureOption::class.java)
        mockFeatureOption.`when`<Boolean> {
            FeatureOption.isHasSupportDragonfly()
        }.thenReturn(true)

        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent {
                return Intent()
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.notificationModel = CommonNotificationModel().also {
            it.setPlayName(MutableLiveData("123"))
            it.setCurTime(MutableLiveData(1000))
            it.playDuration = 3000
            it.canJumpIntent = true
        }
        Whitebox.invokeMethod<Unit>(notification, "initNotification")
        Assert.assertNotNull(notification.notification)
        Assert.assertNotEquals(1, notification.notification?.actions?.size)
        mockFeatureOption.close()
    }

    @Test
    fun should_null_when_getSaveButtonAction() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent {
                return Intent()
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        val action = notification.getSaveButtonAction()
        Assert.assertNull(action)
    }

    @Test
    fun should_correct_when_isBtnEnabled() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent {
                return Intent()
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        notification.notificationModel = CommonNotificationModel().also {
            it.setBtnDisabled(MutableLiveData(false))
        }
        Assert.assertTrue(Whitebox.invokeMethod(notification, "isBtnEnabled"))
    }

    @Test
    fun should_returnNull_when_getOtherDisplayContentIntent() {
        val notification = object : CommonNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 2) {
            override fun getJumpIntent(): Intent {
                return Intent()
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertNull(notification.getOtherDisplayContentIntent())
    }
}