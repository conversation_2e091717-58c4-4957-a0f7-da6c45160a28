/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CommonNotificationResourceHelperTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/19
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/19 1.0 create
 */

package com.soundrecorder.notification.common

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CommonNotificationResourceHelperTest {

    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_notNull_when_getPlayButtonText() {
        val context = context ?: return
        var result = CommonNotificationResourceHelper.getPlayButtonText(context, true)
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getPlayButtonText(context, false)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_notNull_when_getPlayButtonRecordContentDec() {
        val context = context ?: return
        var result = CommonNotificationResourceHelper.getPlayButtonRecordContentDec(context, true)
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getPlayButtonRecordContentDec(context, false)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_notNull_when_getPlayButtonRecordText() {
        val context = context ?: return
        var result = CommonNotificationResourceHelper.getPlayButtonRecordText(context, true)
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getPlayButtonRecordText(context, false)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_notNull_when_getRecordStatusContent() {
        val context = context ?: return
        var result = CommonNotificationResourceHelper.getRecordStatusContent(
            context, isRecording = true, isSaving = true
        )
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getRecordStatusContent(
            context, isRecording = true, isSaving = false
        )
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getRecordStatusContent(
            context, isRecording = false, isSaving = false
        )
        Assert.assertNotNull(result)

        result = CommonNotificationResourceHelper.getRecordStatusContent(
            context, isRecording = false, isSaving = true
        )
        Assert.assertNotNull(result)
    }

    @Test
    fun should_notNull_when_getPlayButtonImageResId() {
        var result = CommonNotificationResourceHelper.getPlayButtonImageResId(true)
        Assert.assertNotEquals(0, result)

        result = CommonNotificationResourceHelper.getPlayButtonImageResId(false)
        Assert.assertNotEquals(0, result)
    }

    @Test
    fun should_notNull_when_getMarkButtonResId() {
        val result = CommonNotificationResourceHelper.getMarkButtonResId()
        Assert.assertNotEquals(0, result)
    }
}