/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonPlaybackNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.R
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CommonPlaybackNotificationTest {

    private var context: Context? = null

    private val browseFile = mockk<BrowseFileInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { browseFile }
        })
    }

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        startKoin(koinApp)
    }

    @After
    fun tearDown() {
        context = null
        stopKoin()
    }

    @Test
    fun should_equals_when_getOldChannelId() {
        val notification = CommonPlaybackNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 1)
        Assert.assertEquals(NotificationUtils.PLAYBACK_OLD_CID, notification.getOldChannelId())
    }

    @Test
    fun should_equals_when_getChannelId() {
        val notification = CommonPlaybackNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 1)
        Assert.assertEquals(NotificationUtils.PLAYBACK_CID, notification.getChannelId())
    }

    @Test
    fun should_equals_when_getChannelName() {
        val notification = CommonPlaybackNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 1)
        Assert.assertEquals(context?.resources?.getString(R.string.playback_channel_name), notification.getChannelName())
    }

    @Test
    fun should_contains_when_getJumpIntent() {
        val notification = CommonPlaybackNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 1)
        val jumpIntent = notification.getJumpIntent()
        Assert.assertNull(jumpIntent?.component?.className)
    }

    @Test
    fun should_notNull_when_getOtherDisplayContentIntent() {
        every { browseFile.createBrowseFileIntent(context!!) } returns Intent()
        val notification = CommonPlaybackNotification(NotificationUtils.NOTIFICATION_PLAY_ID, 1)
        val jumpIntent = notification.getJumpIntent()
        Assert.assertNotNull(jumpIntent)

        val contentIntent = notification.getOtherDisplayContentIntent()
        Assert.assertNotNull(contentIntent)
    }
}