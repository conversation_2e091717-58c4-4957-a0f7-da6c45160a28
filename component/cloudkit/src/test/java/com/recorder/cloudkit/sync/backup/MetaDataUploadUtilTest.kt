package com.recorder.cloudkit.sync.backup

import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.soundrecorder.common.db.RecorderDBUtil
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.SyncDataConstants
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn
import com.soundrecorder.common.constant.RecordConstant
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.spy
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.util.*

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MetaDataUploadUtilTest {
    private var mContext: Context? = null
    private var mRecorderDbUtil: RecorderDBUtil? = null

    @Before
    fun setup() {
        ShadowLog.stream = System.out
        mContext = spy(ApplicationProvider.getApplicationContext<Context>())
        mRecorderDbUtil = RecorderDBUtil.getInstance(mContext)
    }

    @After
    fun release() {
        mContext = null
        mRecorderDbUtil = null
    }

    @Test
    fun should_when_deleteType_onServerAddedOrUpdateForBackup() {
        val insertDataId = insertRecordToDb(globalId = "111111")
        val insertDataIdByDelete = insertRecordToDb(globalId = "222222")
        val responseDeleteRecord = CloudBackupResponseRecord().apply {
            operatorType = SyncDataConstants.OPERATOR_TYPE_DELETE
            sysRecordId = "222222"
        }
        val responseRecycleRecord = CloudBackupResponseRecord().apply {
            operatorType = SyncDataConstants.OPERATOR_TYPE_RECYCLE
            sysRecordId = "111111"
        }
        MetaDataUploadUtil.onServerAddedOrUpdateForBackup(mContext, mutableListOf(responseDeleteRecord, responseRecycleRecord))

        val selection =
            RecorderColumn.COLUMN_NAME_ID + " = ? or " + RecorderColumn.COLUMN_NAME_ID + " = ?"
        val updateRecord =
            RecorderDBUtil.getRecordData(mContext, null, selection, arrayOf(insertDataId.toString(), insertDataIdByDelete.toString()), null)
        Assert.assertTrue(updateRecord?.size == 0)
    }

    @Test
    fun should_insert_data_when_onServerAddedOrUpdateForBackup() {
        val contentValues: ContentValues = genValues(null, "1234")
        val newUri = RecorderDBUtil.insertRecordData(mContext, contentValues)
        Assert.assertNotNull(newUri)
        if (newUri != null) {
            val id = ContentUris.parseId(newUri)
            val responseRecord = CloudBackupResponseRecord().apply {
                operatorType = SyncDataConstants.OPERATOR_TYPE_CREATE
                sysRecordId = "1234"
            }
            MetaDataUploadUtil.onServerAddedOrUpdateForBackup(mContext, mutableListOf(responseRecord))

            val selection = RecorderColumn.COLUMN_NAME_ID + " = $id"
            val updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, null, null)
            // expected:1
            Assert.assertEquals(0, updateRecord.size)
        }

    }

    @Test
    fun should_update_data_when_onServerAddedOrUpdateForBackup() {
        insertRecordToDb(globalId = "7777")
        val responseRecord = CloudBackupResponseRecord().apply {
            operatorType = SyncDataConstants.OPERATOR_TYPE_REPLACE
            sysRecordId = "7777"
            sysVersion = 9999
        }
        MetaDataUploadUtil.onServerAddedOrUpdateForBackup(mContext, mutableListOf(responseRecord))

        val selection = RecorderColumn.COLUMN_NAME_GLOBAL_ID + " = ?"
        val updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, arrayOf("1234"), null)
        // expected:1
        Assert.assertEquals(0, updateRecord?.size)
    }

    @Test
    fun should_when_onRecordResumeOperateForBackUp() {
        val insertId = insertRecordToDb(globalId = "7777", "6666")

        val responseRecord = CloudBackupResponseRecord().apply {
            operatorType = SyncDataConstants.OPERATOR_TYPE_RESUME
            sysRecordId = "7777"
            sysVersion = 9999
        }
        val metaData = CloudMetaDataRecord().apply {
            sysRecordId = "7777"
            fields = "{\"itemId\":\"6666\"}"
        }
        MetaDataUploadUtil.onRecordResumeOperateForBackUp(mContext, mutableListOf(responseRecord), mutableListOf(metaData))

        val selection = RecorderColumn.COLUMN_NAME_ID + " = ?"
        val updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, arrayOf(insertId.toString()), null)
        // expected:1
        Assert.assertEquals(0, updateRecord?.size)
    }

    private fun insertRecordToDb(globalId: String? = null, uuid: String? = null): Long? {
        val contentValues: ContentValues = genValues(globalId, uuid)
        val newUri = RecorderDBUtil.insertRecordData(mContext, contentValues)
        Assert.assertNotNull(newUri)
        if (newUri != null) {
            return ContentUris.parseId(newUri)
        }
        return null
    }

    private fun genValues(globalId: String?, uuid: String?): ContentValues {
        val values = ContentValues()
        if (uuid == null || uuid.isEmpty()) {
            values.put(RecorderColumn.COLUMN_NAME_UUID, UUID.randomUUID().toString())
        } else {
            values.put(RecorderColumn.COLUMN_NAME_UUID, uuid)
        }
        values.put(RecorderColumn.COLUMN_NAME_DATA, "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3")
        values.put(RecorderColumn.COLUMN_NAME_SIZE, 512)
        values.put(RecorderColumn.COLUMN_NAME_DISPLAY_NAME, "123.mp3")
        values.put(RecorderColumn.COLUMN_NAME_MIMETYPE, "audio/mpeg")
        values.put(RecorderColumn.COLUMN_NAME_DATE_CREATED, System.currentTimeMillis())
        values.put(RecorderColumn.COLUMN_NAME_DATE_MODIFIED, System.currentTimeMillis())
        values.put(RecorderColumn.COLUMN_NAME_RECORD_TYPE, 0)
        values.put(RecorderColumn.COLUMN_NAME_MARK_DATA, "")
        values.put(RecorderColumn.COLUMN_NAME_AMP_DATA, "")
        values.put(RecorderColumn.COLUMN_NAME_DURATION, 12000)
        values.put(RecorderColumn.COLUMN_NAME_BUCKET_ID, "")
        values.put(RecorderColumn.COLUMN_NAME_DIRTY, 0)
        values.put(RecorderColumn.COLUMN_NAME_DELETE, RecordConstant.RECORD_NORMAL)
        values.put(RecorderColumn.COLUMN_NAME_RELATIVE_PATH, "\\Music\\Recordings\\Standard Recordings\\")
        values.put(RecorderColumn.COLUMN_NAME_AMP_FILE_PATH, "")
        if (globalId != null && globalId.isNotEmpty()) {
            values.put(RecorderColumn.COLUMN_NAME_GLOBAL_ID, globalId)
            values.put(RecorderColumn.COLUMN_NAME_FILE_ID, "222")
            values.put(RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, "3333")
        }
        values.put(RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, 0)
        values.put(RecorderColumn.COLUMN_NAME_MIGRATE_STATUS, 0)
        return values
    }
}