apply from: "../../common_flavor_build.gradle"

android {
    compileSdkVersion = prop_compileSdkVersion
    buildToolsVersion = prop_buildToolsVersion

    namespace "com.oplus.recorder.questionnaire"
    defaultConfig {
        minSdkVersion prop_minSdkVersion
        targetSdkVersion prop_targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }
    buildFeatures {
        buildConfig true
    }
    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
        }
        domestic {
            java.srcDirs += ['questionaire/src/main/java', 'feedback/src/main/java']
            res.srcDirs += ['questionaire/src/main/res', 'feedback/src/main/res', '../../res-strings-domestic']
            manifest.srcFile 'questionaire/src/main/AndroidManifest.xml'
        }

        export {
            java.srcDirs += ['feedback/src/main/java']
            res.srcDirs += ['feedback/src/main/res', '../../res-strings-export']
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "QUESTIONNAIRE_ID", "\"9fbd26fe608450dbc91f7d94adfd6987\""
            buildConfigField "String", "QUESTIONNAIRE_KEY", "\"PmaLVXQHRXz+5JfWvg5UfFEfoA3tL63lW2cAMZiribk=\""
            buildConfigField "String", "QUESTIONNAIRE_CODE", "\"20007\""
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "QUESTIONNAIRE_ID", "\"beacda022a81cb946ba95779137d7436\""
            buildConfigField "String", "QUESTIONNAIRE_KEY", "\"DsxAf6/yjqhB+E33hdGLBWBfDBh0TE3GaOqtCXGap3E=\""
            buildConfigField "String", "QUESTIONNAIRE_CODE", "\"20007\""
        }
    }
    compileOptions {
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }
    kotlinOptions {
        jvmTarget = "${prop_targetCompatibility}"
    }
    testOptions {
        unitTests.returnDefaultValues = true
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material

    //for coroutines
    api libs.kotlinx.coroutines.core
    api libs.kotlinx.coroutines
    //for lifecycle
    api libs.androidx.lifecycle.viewmodel.savedstate
    api libs.androidx.lifecycle.runtime
    api libs.androidx.lifecycle.livedata
    api libs.androidx.lifecycle.viewmodel

    //stdid
    implementation libs.oplus.stdid.sdk
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    compileOnly libs.oplus.addon

    // base包为必须引用的包，prop_versionName需保持一致
    compileOnly (libs.oplus.coui.core)
    compileOnly (libs.oplus.coui.dialog)
    compileOnly (libs.oplus.material)
    testImplementation libs.oplus.coui.core
    testImplementation libs.oplus.coui.dialog
    // WebView
    implementation libs.androidx.webkit

    //retrofit,cloudkit也接入了retrofit
    implementation(libs.square.retrofit2) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation(libs.square.retrofit2.converter) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation(libs.okhttp.logging) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    /*clodkit使用taphttp,为了解冲突，okhttp统一替换使用taphttp*/
    implementation(libs.heytap.nearx.http)
    //room,version same with cloukit
    kapt libs.androidx.room.compiler
    implementation libs.androidx.room.runtime
    implementation libs.androidx.room.ktx
    implementation(libs.coil) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation(libs.okio)


    androidTestImplementation libs.androidx.room.testing

    //feedback_sdk
    domesticImplementation libs.oplus.feedback.env.domestic
    domesticImplementation(libs.oplus.feedback.cdp) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    exportImplementation libs.oplus.feedback.env.export
    exportImplementation(libs.oplus.feedback.sdk) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }

    // Koin for Android
    implementation(libs.koin)

    implementation libs.glide
}
