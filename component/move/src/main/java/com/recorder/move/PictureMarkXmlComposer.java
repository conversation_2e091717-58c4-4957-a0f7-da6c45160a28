/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: ConvertRecordXmlComposer
 ** Description:for move convert vad table generate xml.
 ** Version:1.0
 ** Date :2019-10-11
 ** Author: tianjun
 **
 ** v1.0, 2019-10-11, tianjun, create
 ****************************************************************/
package com.recorder.move;

import android.util.Xml;

import org.xmlpull.v1.XmlSerializer;

import java.io.IOException;
import java.io.StringWriter;

import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.constant.DatabaseConstant;

import com.soundrecorder.base.utils.DebugUtil;

import com.soundrecorder.common.databean.markdata.MarkDataBean;

public class PictureMarkXmlComposer {
    private String TAG = "PictureMarkXmlComposer";
    private XmlSerializer mSerializer = null;
    private StringWriter mStringWriter = null;


    public boolean startCompose() {
        boolean result = false;
        mSerializer = Xml.newSerializer();
        mStringWriter = new StringWriter();
        try {
            mSerializer.setOutput(mStringWriter);
            mSerializer.startDocument(null, false);
            mSerializer.startTag("", "picture_mark");
            result = true;
        } catch (IOException e) {
            DebugUtil.e(TAG, "startCompose IOException error", e);
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e);
        }

        return result;
    }

    public boolean endCompose() {
        boolean result = false;
        try {
            mSerializer.endTag("", "picture_mark");
            mSerializer.endDocument();
            result = true;
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "endCompose IllegalStateException error", e);
        } catch (IOException e) {
            DebugUtil.e(TAG, "endCompose IOException error", e);
        }

        return result;
    }

    public void addPictureMark(MarkDataBean markDataBean) {
        try {
            mSerializer.startTag("", DatabaseConstant.ROOT);

            mSerializer.attribute("", PictureMarkDbUtils.MARK_TEXT,markDataBean.getMarkText());
            mSerializer.attribute("", PictureMarkDbUtils.PICTURE_FILE_PATH, markDataBean.getPictureFilePath());
            mSerializer.attribute("", PictureMarkDbUtils.DEFAULT_NO, String.valueOf(markDataBean.getDefaultNo()));
            mSerializer.attribute("", PictureMarkDbUtils.TIME_IN_MILLS, String.valueOf(markDataBean.getTimeInMills()));
            mSerializer.attribute("", PictureMarkDbUtils.VERSION, String.valueOf(markDataBean.getVersion()));
            mSerializer.attribute("", PictureMarkDbUtils.KEY_ID, markDataBean.getKeyId());

            mSerializer.endTag("", DatabaseConstant.ROOT);
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "addConvertRecord IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "addConvertRecord IllegalStateException error", e);
        } catch (IOException e) {
            DebugUtil.e(TAG, "addConvertRecord IOException error", e);
        } catch (NullPointerException e) {
            DebugUtil.e(TAG, "addConvertRecord NullPointerException error", e);
        }
    }

    public String getXmlInfo() {
        try {
            if (mStringWriter != null) {
                String info = mStringWriter.toString();
                mStringWriter.close();
                return info;
            }
        } catch (IOException e) {
            DebugUtil.e(TAG, "mStringWriter close error", e);
        }
        return null;
    }
}