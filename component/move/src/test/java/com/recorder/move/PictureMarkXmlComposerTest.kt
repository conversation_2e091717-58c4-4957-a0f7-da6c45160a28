package com.recorder.move

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.move.shadows.ShadowBaseUtils
import com.recorder.move.shadows.ShadowFeatureOption
import com.recorder.move.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_PICTURE
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class PictureMarkXmlComposerTest {
    private var composer: PictureMarkXmlComposer? = null

    @Before
    fun setUp() {
        composer = PictureMarkXmlComposer()
    }

    @Test
    fun should_return_String_getXmlInfo() {
        composer?.startCompose()
        composer?.addPictureMark(MarkDataBean(0, VERSION_PICTURE))
        composer?.endCompose()
        val xmlInfo = composer?.xmlInfo
        Assert.assertNotNull(xmlInfo)
    }


    @Test
    fun should_return_true_startComposeAndEndCompose() {
        val startResult = composer?.startCompose() ?: false
        Assert.assertTrue(startResult)
        val endResult = composer?.endCompose() ?: false
        Assert.assertTrue(endResult)
    }
}