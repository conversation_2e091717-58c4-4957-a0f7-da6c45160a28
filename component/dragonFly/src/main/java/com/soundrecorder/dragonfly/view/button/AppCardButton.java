/*********************************************************************************
 ** Copyright (C), 2008-2020, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 ** Oplus Coding Static Checking Skip
 ** File: - public class COUIButton extends AppCompatButton {.java
 ** Description:
 **     This widget is used for ButtonUI for COUI.
 **
 ** Version: 1.0
 ** Date: 2019/11/7
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>             <data>           <version>         <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON>@ROM.SDK      2019/11/7           1.0       Annotate this module
 ********************************************************************************/

package com.soundrecorder.dragonfly.view.button;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.graphics.ColorUtils;

import com.soundrecorder.dragonfly.R;
import com.soundrecorder.dragonfly.utils.AppCardUtils;

public class AppCardButton extends AppCompatImageView {
    private static final int MAX_COLOR_VALUE = 255;
    private static final int FILL_BUTTON_ANIM = 1;
    private final int mAnimType;
    private final Paint mFillPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final int mEnableColor;
    private final int mDisabledColor;
    private final float mStrokeWidth;
    private final float[] mColorHsl = new float[3];
    private final AppCardPressFeedbackHelper mFeedbackUtils;
    //某些场景下，想置灰按钮，但是又想响应点击事件，即可使用这个值，表示假的置灰
    private boolean mFakeDisable;

    public AppCardButton(Context context) {
        this(context, null);
    }

    public AppCardButton(Context context, AttributeSet attrs) {
        this(context, attrs, androidx.appcompat.R.attr.buttonStyle);
    }

    public AppCardButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setScaleType(ScaleType.CENTER);
        final TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.AppCardButton, defStyleAttr, 0);
        mAnimType = a.getInteger(R.styleable.AppCardButton_animType, FILL_BUTTON_ANIM);
        mDisabledColor = a.getColor(R.styleable.AppCardButton_disabledColor, 0);
        mEnableColor = a.getColor(R.styleable.AppCardButton_enableColor, 0);
        mStrokeWidth = a.getDimension(R.styleable.AppCardButton_strokeWidth, context.getResources().getDimension(R.dimen.dp1));
        a.recycle();
        mFillPaint.setStrokeWidth(mStrokeWidth);
        mFeedbackUtils = new AppCardPressFeedbackHelper(this);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        int save = canvas.save();
        canvas.translate(getScrollX(), getScrollY());
        float halfWidth = getWidth() / 2f;
        float halfHeight = getHeight() / 2f;
        float r = Math.min(halfWidth, halfHeight) - mStrokeWidth;
        if (mAnimType == FILL_BUTTON_ANIM) {
            mFillPaint.setStyle(Paint.Style.FILL_AND_STROKE);
            if (isEnabled() && !mFakeDisable) {
                mFillPaint.setColor(getFillAnimatorColor(mEnableColor));
            } else {
                mFillPaint.setColor(mDisabledColor);
            }
        } else {
            if (isEnabled() && !mFakeDisable) {
                mFillPaint.setStyle(Paint.Style.FILL);
                mFillPaint.setColor(getStrokeButtonAnimatorColor(mEnableColor));
                canvas.drawCircle(halfWidth, halfHeight, r, mFillPaint);
                mFillPaint.setColor(mEnableColor);
            } else {
                mFillPaint.setColor(mDisabledColor);
            }
            mFillPaint.setStyle(Paint.Style.STROKE);
        }
        canvas.drawCircle(halfWidth, halfHeight, r, mFillPaint);
        canvas.restoreToCount(save);
        super.onDraw(canvas);
    }

    public synchronized void setFakeDisable(boolean fakeDisable) {
        if (this.mFakeDisable == fakeDisable) return;
        this.mFakeDisable = fakeDisable;
        invalidate();
    }

    public synchronized boolean getFakeDisable() {
        return mFakeDisable;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        try {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    AppCardUtils.log("id = " + getId() + "MotionEvent.ACTION_DOWN");
                    if (isEnabled() && !mFakeDisable) {
                        mFeedbackUtils.executeFeedbackAnimator(true);
                    }
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    AppCardUtils.log("id = " + getId() + "MotionEvent.ACTION_UP");
                    if (isEnabled() && !mFakeDisable) {
                        mFeedbackUtils.executeFeedbackAnimator(false);
                    } else {
                        mFeedbackUtils.reset();
                    }
                    break;
                default:
            }
        } catch (Exception ignore) {
        }
        return super.onTouchEvent(event);
    }

    private int getFillAnimatorColor(int srcColor) {
        ColorUtils.colorToHSL(srcColor, mColorHsl);
        mColorHsl[2] *= mFeedbackUtils.getBrightness();
        int color = ColorUtils.HSLToColor(mColorHsl);
        int r = Math.min(MAX_COLOR_VALUE, Color.red(color));
        int g = Math.min(MAX_COLOR_VALUE, Color.green(color));
        int b = Math.min(MAX_COLOR_VALUE, Color.blue(color));
        int a = Color.alpha(srcColor);
        return Color.argb(a, r, g, b);
    }

    private int getStrokeButtonAnimatorColor(int srcColor) {
        int r = Math.min(MAX_COLOR_VALUE, Color.red(srcColor));
        int g = Math.min(MAX_COLOR_VALUE, Color.green(srcColor));
        int b = Math.min(MAX_COLOR_VALUE, Color.blue(srcColor));
        int a = (int) (mFeedbackUtils.getAlpha() * 255);
        return Color.argb(a, r, g, b);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mFeedbackUtils.reset();
    }
}
