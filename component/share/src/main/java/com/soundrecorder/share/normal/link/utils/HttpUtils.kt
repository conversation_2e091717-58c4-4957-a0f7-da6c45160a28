/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - HttpUtils.kt
 ** Description: HttpUtils.
 ** Version: 1.0
 ** Date : 2025/3/10
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/10    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

import android.os.Build
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.share.normal.link.bean.ActionResult
import com.soundrecorder.share.normal.link.bean.GenerateSignData
import com.soundrecorder.share.normal.link.bean.ResponseContent
import com.soundrecorder.share.normal.link.utils.AesUtils.IV_LENGTH
import com.soundrecorder.share.normal.link.utils.AesUtils.NONCE_LENGTH
import okhttp3.Call
import okhttp3.Headers
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.apache.commons.codec.digest.HmacAlgorithms
import org.apache.commons.codec.digest.HmacUtils
import java.util.Locale
import java.util.TreeMap
import java.util.stream.Collectors

object HttpUtils {
    private const val TAG = "HttpUtils"
    private const val JSON_MEDIA_TYPE = "application/json; charset=utf-8"
    private const val RSA_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPDyzSRYHGL0O43" +
            "EmP1oGcSKChlMLbDXl33ItyWutdoHN6JgmQba61hfqfKPRJhXK+q/u9BlbWufkLqVLOEVncYUu+9TpTgSPf4gG" +
            "XjDqIJW6qylV0gAUAagvpuflxGf02mTOPcvHYAAOd2hdCbkWDydU4dnD0nLWfzCz7G4eVfDUi06xX5yEuMHAgA" +
            "vbczTPw80xDHeLZm6mw+8Ng9sGG679vyodhHnPVPcOeK0Gp7asJN7lEeu6d7S23VjECX98+JRV56uTvL03dyfN" +
            "u0VLdGQMSP1BTMMDdkfwPxavPzE7I09Y8qhPQKsrEGZOV7l4+9fMoLRCLM78/A3mkX2ywIDAQAB"
    private const val APP_KEY_CONTENT = "prod_record"
    private const val SECRET = "578c8f80c5e96baa2affff21c51384c1e4c9c77c90"
    private const val SPLIT_UNDERLINE = "_"

    private const val REQUEST_HEADER_APP_KEY = "appKey"
    private const val REQUEST_HEADER_TIME_TAMP = "timestamp"
    private const val REQUEST_HEADER_NONCE = "nonce"
    private const val REQUEST_HEADER_SIGN = "sign"
    private const val REQUEST_HEADER_PHONE_MODEL = "model"
    private const val REQUEST_HEADER_PHONE_BRAND = "brand"
    private const val REQUEST_HEADER_OPLUS_OS_VERSION = "oplusOSVersion"
    private const val REQUEST_HEADER_APP_VERSION = "appVersion"

    private const val ENCRYPTED_BODY_IV = "iv"
    private const val ENCRYPTED_BODY_KEY = "key"
    private const val ENCRYPTED_BODY_ENCRYPT_CONTENT = "encryptContent"

    private val deviceModel by lazy { Build.MODEL }
    private val brand by lazy { Build.BRAND }
    private val oplusOSVersion by lazy { Build.DISPLAY.split(SPLIT_UNDERLINE).getOrElse(1) { "" } }
    private val appVersion by lazy { AppUtil.getAppVersionCode().toString() }

    @JvmStatic
    fun sendAsynPostRequest(
        address: String,
        headers: Headers,
        body: RequestBody,
        callback: okhttp3.Callback
    ): Call  {
        val client = OkHttpClient()
        val request = Request.Builder()
            .url(address)
            .headers(headers)
            .post(body)
            .build()
        val call = client.newCall(request)
        call.enqueue(callback)
        return call
    }

    @JvmStatic
    fun sendAsynPostRequest(address: String, body: RequestBody, callback: okhttp3.Callback) {
        val client = OkHttpClient()
        val request = Request.Builder()
            .url(address)
            .post(body)
            .build()
        client.newCall(request).enqueue(callback)
    }

    @JvmStatic
    fun sendAsynPutRequest(address: String, body: RequestBody, callback: okhttp3.Callback): Call {
        val client = OkHttpClient()
        val request = Request.Builder()
            .url(address)
            .put(body)
            .build()
        val call = client.newCall(request)
        call.enqueue(callback)
        return call
    }

    @JvmStatic
    fun sendAsynPostEncryptedRequest(baseUrl: String, path: String, requestBody: String, aesKey: String, callback: okhttp3.Callback): Call {
        val url = baseUrl + path
        val encryptedJsonBody = getEncryptedJsonBody(requestBody, aesKey)
        val jsonMediaType = JSON_MEDIA_TYPE.toMediaType()
        val encryptedRequestBody = encryptedJsonBody.toRequestBody(jsonMediaType)
        val headers = buildRequestHeader(encryptedJsonBody, path)
        return sendAsynPostRequest(url, headers, encryptedRequestBody, callback)
    }

    @JvmStatic
    private fun getEncryptedJsonBody(requestBody: String, aesKey: String): String {
        val iv = AesUtils.randomToken(IV_LENGTH)
        val encryptContentBase64 = AesUtils.encrypt(requestBody, aesKey, iv)                 // 加密requestBody
        val encryptAesKey = RsaUtils.encryptByPublicKeyEncodeBase64(aesKey, RSA_PUBLIC_KEY)  // 加密aesKey
        val map = mutableMapOf<String, String>()
        map[ENCRYPTED_BODY_IV] = iv
        map[ENCRYPTED_BODY_KEY] = encryptAesKey
        map[ENCRYPTED_BODY_ENCRYPT_CONTENT] = encryptContentBase64
        val encryptedJsonBody = Gson().toJson(map)
        return encryptedJsonBody
    }

    @JvmStatic
    fun getDecryptedRealResponseBody(jsonData: String?, aesKey: String, callMethodName: String = ""): String? {
        var realResponseBody: String? = null
        val typeToken = object : TypeToken<ActionResult<ResponseContent>>() {}.type
        val actionResult = Gson().fromJson<ActionResult<ResponseContent>>(jsonData, typeToken)
        if (actionResult.code == 0) {
            realResponseBody = actionResult.data?.let { AesUtils.decrypt(it.encryptContent, aesKey, it.iv) }
        }
        DebugUtil.i(TAG, "getDecryptedRealResponseBody, $callMethodName realResponseBody=$realResponseBody\n" +
                "jsonData=$jsonData")
        return realResponseBody
    }

    @JvmStatic
    private fun buildRequestHeader(encryptedJsonBody: String, requestPath: String): Headers {
        val timestamp = System.currentTimeMillis().toString()
        val nonce = AesUtils.randomToken(NONCE_LENGTH)
        val generateSignData = GenerateSignData(encryptedJsonBody, timestamp, APP_KEY_CONTENT, SECRET, nonce, requestPath, null)
        val serverSign = generateSign(generateSignData)
        val headers = Headers.Builder()
            .add(REQUEST_HEADER_APP_KEY, APP_KEY_CONTENT)
            .add(REQUEST_HEADER_TIME_TAMP, timestamp)
            .add(REQUEST_HEADER_NONCE, nonce)        // 6位随机字符
            .add(REQUEST_HEADER_SIGN, serverSign)   // 签名
            .add(REQUEST_HEADER_PHONE_MODEL, deviceModel)
            .add(REQUEST_HEADER_PHONE_BRAND, brand)
            .add(REQUEST_HEADER_OPLUS_OS_VERSION, oplusOSVersion)
            .add(REQUEST_HEADER_APP_VERSION, appVersion)
            .build()
        return headers
    }

    @JvmStatic
    private fun generateSign(generateSignData: GenerateSignData): String {
        val paramsString = getParamsString(generateSignData.requestParams)
        val signTemp = java.lang.String.join(
            "&",
            generateSignData.appKey,
            generateSignData.timestamp,
            generateSignData.nonce,
            generateSignData.requestPath,
            generateSignData.requestBody,
            paramsString,
            generateSignData.secret
        )
        val hmac = HmacUtils(HmacAlgorithms.HMAC_SHA_256, generateSignData.secret)
        return hmac.hmacHex(signTemp)
    }

    @JvmStatic
    private fun getParamsString(params: Map<String, String?>?): String? {
        // 如果没有请求参数，则序列化为 null
        if (params.isNullOrEmpty()) {
            return null
        }
        val treeMap = TreeMap<String, String?>()
        // key 转为小写
        for ((key, value) in params) {
            if (value?.isNotEmpty() == true) {
                treeMap[key.lowercase(Locale.getDefault())] = value
            }
        }
        return treeMap.entries.stream()
            .filter { entry: Map.Entry<String, String?> -> null != entry.value }
            .map { entry: Map.Entry<String, String?> -> entry.key + "=" + entry.value }
            .collect(Collectors.joining("&"))
    }
}