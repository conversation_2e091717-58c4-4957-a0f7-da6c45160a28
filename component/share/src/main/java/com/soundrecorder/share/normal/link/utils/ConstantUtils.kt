/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - ConstantUtils.kt
 ** Description: ConstantUtils.
 ** Version: 1.0
 ** Date : 2025/3/28
 ** Author: zhangmeng
 **
 ** ---------------------Revision History: ---------------------
 ** <author> <data> <version > <desc>
 ** zhangmeng    2025/3/28    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.utils

class ConstantUtils {

    companion object {
        const val HTTP = "https://"
        // 全球域名URL
        const val GLOBAL_BASE_URL = "https://share-api-gl.allawntech.com"

        // 通过区域编码获取区域服务器地址接口路径
        const val GET_GLOBAL_DOMAIN_REQUEST_PATH = "/share/mobile/common/v1/simple-region-info"
        // 分享注册接口路径
        const val REGISTER_PATH = "/share/mobile/record/v1/register"
        // 获取预签名接口路径
        const val GET_PRE_SIGNATURE_PATH = "/share/mobile/file/v1/copy-check-single-url"
        // 音频文件上传上报状态接口路径
        const val REPORT_STATUS_PATH = "/share/mobile/file/v1/report-status"
        // 音频文件内容安全审核
        const val COMPLETE_VERIFY_PATH = "/share/mobile/file/v1/complete-verify"

        const val REGION = "region"
        const val FILE_ID = "fileId"
    }

    object Convert {
        const val TIME = "time"
        const val  ROLE = "role"
        const val ROLE_NAME = "roleName"
        const val TEXT = "text"
    }
    object RegisterExtra {
        const val TITLE = "title"
        const val COVERT_TIME = "covertTime"
        const val TYPE = "type"
        const val CALL_SOURCE = "callSource"
        const val FILE_LOCAL_ID = "fileLocalId"
        const val FILE_DURATION = "fileDuration"
        const val FILE_SIZE = "fileSize"
        const val ROLES = "roles"
        const val COVERT_ITEMS = "covertItems"
    }
    object RegisterParam {
        const val USER_ID = "userId"
        const val DEVICE_ID = "deviceId"
        const val TITLE = "title"
        const val VERSION = "version"
        const val LANG = "lang"
        const val REGION = "region"
        const val EXTRA = "extra"
    }

    object PreSignUrlParam {
        const val FILE_NAME = "fileName"
        const val FILE_SIZE = "fileSize"
        const val DEVICE_ID = "deviceId"
        const val USER_ID = "userId"
        const val FILE_LOCAL_ID = "fileLocalId"
        const val FILE_TYPE = "fileType"
        const val BUSINESS_TYPE = "businessType"
        const val BUSINESS_ID = "businessId"
        const val REGION = "region"
    }
}