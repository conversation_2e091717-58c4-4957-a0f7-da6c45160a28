package com.soundrecorder.convertservice.convert;

import android.net.Uri;
import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ConvertServiceUtilsTest {

    private static final String TEST_STRING = "TEST_STRING";
    private static final int TEST_SECONDS = 121;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
    }

    @Test
    public void should_not_null_when_getSHA256HEX() {
        Uri uri = Mockito.mock(Uri.class);
        String actualRes = ConvertServiceUtils.getSHA256HEX(uri);
        Assert.assertNotNull(actualRes);
    }

    @Test
    public void should_return_encrypted_string_when_getSHA256HEX() {
        String encryptedString = ConvertServiceUtils.getSHA256HEX(TEST_STRING);
        Assert.assertNotNull(encryptedString);
    }
}
