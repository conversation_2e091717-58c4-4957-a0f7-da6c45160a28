/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.util;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;


import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RoleNameUtilTest {

    private static final String ROLE_NAME_3 = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.convert_speaker, 3);
    private static final String SPLIT_STRING = "split/String/To/List/";
    private static final String SPLIT_STRING_WITH_EMPTY = "////";
    private static final Long TEST_RECORD_ID = 0L;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
    }

    @Test
    public void should_empty_when_getHistoryRoleName() {
        List<String> historyRoleNameList = new ArrayList<>();
        historyRoleNameList = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.getHistoryRoleName(TEST_RECORD_ID);
        Assert.assertEquals(0, historyRoleNameList.size());
    }

    @Test
    public void should_equals_when_combineListToString() {
        String combineString = "";
        List<String> combineList = new ArrayList<>();
        combineString = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.combineListToString(combineList);
        Assert.assertEquals("", combineString);
        combineList.add("split");
        combineList.add("String");
        combineList.add("To");
        combineList.add("List");
        combineString = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.combineListToString(combineList);
        Assert.assertEquals(SPLIT_STRING, combineString);
    }

    @Test
    public void should_equals_when_splitStringToList() {
        List<String> splitList = new ArrayList<>();
        splitList = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.splitStringToList("");
        Assert.assertEquals(0, splitList.size());
        splitList = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.splitStringToList(SPLIT_STRING);
        Assert.assertEquals(4, splitList.size());
        splitList = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.splitStringToList(SPLIT_STRING_WITH_EMPTY);
        Assert.assertEquals(0, splitList.size());
    }

    @Test
    public void should_equals_when_genRoleNameByRoleId() {
        String roleName = com.soundrecorder.convertservice.util.RoleNameUtil.Companion.genRoleNameByRoleId(3);
        Assert.assertEquals(ROLE_NAME_3, roleName);
    }
}
