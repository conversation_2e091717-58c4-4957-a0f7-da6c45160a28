/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForConvertService.kt
 * * Description : AutoDiForConvertService
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.convertservice.di

import androidx.annotation.Keep
import com.soundrecorder.convertservice.api.ConvertCheckUtilsApi
import com.soundrecorder.convertservice.api.ConvertServiceApi
import com.soundrecorder.convertservice.api.ConvertSupportApi
import com.soundrecorder.convertservice.api.ConvertTaskApi
import com.soundrecorder.convertservice.api.ConvertThreadManageApi
import com.soundrecorder.convertservice.smartname.SmartNameApi
import com.soundrecorder.modulerouter.convertService.ConvertCheckUtilsAction
import com.soundrecorder.modulerouter.convertService.ConvertServiceInterface
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.convertService.ConvertTaskAction
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import org.koin.dsl.module

@Keep
object AutoDiForConvertService {
    val convertServiceModule = module {
        single<ConvertServiceInterface>(createdAtStart = true) {
            ConvertServiceApi
        }
        single<ConvertSupportAction>(createdAtStart = true) {
            ConvertSupportApi
        }
        single<ConvertCheckUtilsAction>(createdAtStart = true) {
            ConvertCheckUtilsApi
        }
        single<ConvertThreadManageAction>(createdAtStart = true) {
            ConvertThreadManageApi
        }
        single<ConvertTaskAction>(createdAtStart = true) {
            ConvertTaskApi
        }
        single<SmartNameAction>(createdAtStart = true) {
            SmartNameApi
        }
    }
}