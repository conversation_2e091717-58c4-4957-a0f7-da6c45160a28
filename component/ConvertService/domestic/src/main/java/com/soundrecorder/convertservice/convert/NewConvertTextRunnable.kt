/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NewConvertTextRunnable
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import com.soundrecorder.convertservice.process.ProcessConvert
import com.soundrecorder.convertservice.process.ProcessSendOCS
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.base.utils.DebugUtil

class NewConvertTextRunnable(val mediaId: Long, val convertAiTitle: Boolean) : Thread(),
    CancelableRunnable, IConvertTextRunnable {

    companion object {
        const val TAG: String = "NewConvertTextRunnable"
    }

    var mConvertUiCallback: IConvertCallback? = null
    private var runnableProgressCallback: IConvertTextRunnableProgress? = null

    @Volatile
    var mConvertRecord: ConvertRecord = ConvertRecord(mediaId)

    private var mProcessConvert: ProcessConvert? = null
    private var mProcessSendOCS: ProcessSendOCS? = null

    override fun unregisterConvertUiCallback() {
        mConvertUiCallback = null
        mProcessConvert?.convertCallback = null
        mProcessSendOCS?.convertCallback = null
    }

    override fun registerConvertUiCallback(convertUiCallback: IConvertCallback?) {
        mConvertUiCallback = convertUiCallback
        mProcessConvert?.convertCallback = convertUiCallback
        mProcessSendOCS?.convertCallback = convertUiCallback
    }

    override fun registerProgressCallback(progressCallback: IConvertTextRunnableProgress?) {
        runnableProgressCallback = progressCallback
    }

    override fun run() {
        DebugUtil.i(TAG, "runnable$mediaId start run")
        doRun()
        postConvertEnd()
    }

    override fun cancelWork() {
        doCancel()
        postCancelConvert()
    }

    /**
     * stop this runable right now
     */
    override fun release() {
        cancelWork()
        unregisterConvertUiCallback()
    }

    /**
     * real start convert process
     */
    private fun doRun() {
        DebugUtil.e(TAG, "doRun: $mediaId")
        mProcessConvert = ProcessConvert(null, mConvertUiCallback, convertAiTitle)
        mProcessSendOCS = ProcessSendOCS(mProcessConvert, mConvertUiCallback, convertAiTitle)
        mProcessSendOCS?.process(mConvertRecord)
    }

    /**
     * real cancel convert process
     */

    private fun doCancel() {
        mProcessConvert?.cancel()
        mProcessSendOCS?.cancel()
    }

    private fun postConvertEnd() {
        mConvertUiCallback?.onConvertEnd(mediaId)
        unregisterConvertUiCallback()
        runnableProgressCallback?.postConvertEnd(mediaId, convertAiTitle)
    }

    private fun postCancelConvert() {
        DebugUtil.i(TAG, "postCancelConvert $mediaId")
        runnableProgressCallback?.postCancelConvert(mediaId, convertAiTitle)
    }

    override fun getCurrentConvertStatus(): ConvertStatus {
        val uploadStatus = mConvertRecord.uploadStatus
        val convertStatus = mConvertRecord.convertStatus
        val status = ConvertStatus(uploadStatus, convertStatus)
        DebugUtil.i(TAG, "status: ${status.uploadStatus},${status.convertStatus}")
        return status
    }

    override fun startConvert() {
        start()
    }
}