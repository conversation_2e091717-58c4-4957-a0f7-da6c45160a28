/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryActionModeCallback
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.callback

import android.graphics.Color
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.widget.TextView
import androidx.core.graphics.alpha
import androidx.core.graphics.blue
import androidx.core.graphics.green
import androidx.core.graphics.red
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.utils.JumpIntentUtil

class SummaryActionModeCallback(private val textView: TextView) : ActionMode.Callback2() {

    companion object {
        private const val TAG = "SummaryActionModeCallback"
        const val SPACE = "□ "  //空格字符  a为占用旗子的占位符  空格处于行尾会被干掉
        private const val DARK_MODE_ALPHA = 255 * 0.25
        private const val LIGHT_MODE_ALPHA = 255 * 0.10
    }

    private val activity = textView.context as? FragmentActivity
    private val lifecycle = activity?.lifecycleScope


    override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
        menu ?: return false
        val copyItem = menu.findItem(android.R.id.copy)
        val shareItem = menu.findItem(android.R.id.shareText)
        val selectAllItem = menu.findItem(android.R.id.selectAll)
        menu.clear()
        copyItem?.let { menu.add(it.groupId, it.itemId, it.order, it.title) }
        shareItem?.let { menu.add(it.groupId, it.itemId, it.order, it.title) }
        selectAllItem?.let {
            menu.add(
                it.groupId,
                it.itemId,
                it.order,
                com.soundrecorder.common.R.string.select_all
            )
        }
        return true
    }

    override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
        return false
    }

    fun getColor(): Int {
        val color = COUIContextUtil.getAttrColor(
            textView.context,
            com.support.appcompat.R.attr.couiColorPrimary,
            0
        )
        val alpha = if (NightModeUtil.isNightMode(textView.context)) {
            (DARK_MODE_ALPHA * color.alpha).toInt()
        } else {
            (LIGHT_MODE_ALPHA * color.alpha).toInt()
        }
        val r = color.red
        val g = color.green
        val b = color.blue
        return Color.argb(alpha, r, g, b)
    }

    override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
        when (item?.itemId) {
            android.R.id.copy -> {
                if (clipboard()) {
                    mode?.finish()
                    return true
                }
            }

            android.R.id.shareText -> {
                if (shareSelectedText()) {
                    mode?.finish()
                    return true
                }
            }
        }
        return false
    }

    private fun clipboard(): Boolean {
        val activity = this.activity ?: return false
        val lifecycle = this.lifecycle ?: return false
        val selStart: Int = textView.selectionStart
        val selEnd: Int = textView.selectionEnd
        val text = textView.text.toString()
        val subString = text.substring(selStart, selEnd).replace(SPACE, "")
        JumpIntentUtil.copyText(activity, lifecycle, subString)
        return true
    }

    private fun shareSelectedText(): Boolean {
        val activity = this.activity ?: return false
        val selStart: Int = textView.selectionStart
        val selEnd: Int = textView.selectionEnd
        val text = textView.text.toString()
        val subString = text.substring(selStart, selEnd).replace(SPACE, "")
        JumpIntentUtil.shareText(activity, subString)
        return true
    }

    override fun onDestroyActionMode(mode: ActionMode?) {
        DebugUtil.d(TAG, "onDestroyActionMode")
        textView.highlightColor = Color.TRANSPARENT
    }
}