/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryCallback
 * Description:
 * Version: 1.0
 * Date: 2025/5/27
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/27      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import com.oplus.unified.summary.sdk.callback.ISummaryCallback

interface IAISummarySDKCallback : ISummaryCallback {

    override fun onCloudState(
        sessionId: String,
        state: Int,
        fileId: String?,
        extras: Map<String, Any>?
    ) {
    }

    override fun onDataAvailable(sessionId: String, jsonResult: String, extras: Map<String, Any>?) {
    }

    override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
    }

    override fun onStop(sessionId: String, extras: Map<String, Any>?) {
    }

    override fun onUploading(sessionId: String, extras: Map<String, Any>?) {
    }
}