/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExportPdf
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.graphics.pdf.PdfDocument
import android.graphics.pdf.PdfDocument.Page
import com.soundrecorder.base.utils.DebugUtil
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

object ExportPdf {
    private const val TAG = "ExportPdf"
    private const val DEFAULT_PAGE_WIDTH = 800
    private const val DEFAULT_PAGE_HEIGHT = 1000
    private const val TITLE_TEXT_SIZE = 22f
    private const val CONTENT_TEXT_SIZE = 14f
    private const val MARGIN_HORIZONTAL = 10f
    private const val MARGIN_VERTICAL = 50f
    private const val TITLE_GAP = 40f
    private const val CONTENT_GAP = 20f

    data class PageParam(var textHeight: Float, var pageNumber: Int)

    @JvmStatic
    @SuppressLint("ResourceType", "LongMethod")
    fun saveToPdf(
        context: Context,
        targetPath: String,
        data: ExportSummaryData
    ): Boolean {
        val pageParam = PageParam(MARGIN_VERTICAL, 1)

        val pdfDocument = PdfDocument()
        val pageInfo =
            PdfDocument.PageInfo.Builder(
                DEFAULT_PAGE_WIDTH,
                DEFAULT_PAGE_HEIGHT,
                pageParam.pageNumber
            ).create()
        var page = pdfDocument.startPage(pageInfo)
        //标题
        page = drawTitle(pdfDocument, page, pageParam, data)
        //内容
        page = drawContent(pdfDocument, page, pageParam, data)
        //图片（卡片需要画成图片）
        page = drawImage(context, pdfDocument, page, pageParam, data)
        //写入
        pdfDocument.finishPage(page)
        //创建缓存目录
        File(targetPath).parentFile?.let {
            if (it.exists().not()) {
                it.mkdirs()
            }
        } ?: run {
            pdfDocument.close()
            return false
        }

        //保存，有同名的要删掉此前的，只保留最新的
        val filePath = File(targetPath)
        if (filePath.exists()) {
            DebugUtil.e(TAG, "already exist, delete old file!")
            filePath.delete()
        }
        try {
            pdfDocument.writeTo(FileOutputStream(filePath))
            scanFile(context, targetPath)
            return true
        } catch (e: IOException) {
            DebugUtil.d(TAG, "e = ${e.message}")
            return false
        } finally {
            pdfDocument.close()
        }
    }

    @JvmStatic
    private fun drawTitle(
        pdfDocument: PdfDocument,
        page: Page,
        pageParam: PageParam,
        data: ExportSummaryData
    ): Page {
        var resultPage = page
        val titlePaint = Paint().apply {
            textSize = TITLE_TEXT_SIZE
            isAntiAlias = true
            typeface = Typeface.create(Typeface.DEFAULT, Typeface.BOLD)
            color = Color.BLACK
        }
        val title = data.title
        val titleWidth = titlePaint.measureText(title)
        //处理过长
        if ((titleWidth + MARGIN_HORIZONTAL * 2) > page.canvas.width) {
            val lines = lineBreak(titlePaint, title, page.canvas.width)
            lines.forEachIndexed { index, s ->
                val childLineWidth = titlePaint.measureText(s)
                val titleHeight = titlePaint.descent() - titlePaint.ascent()
                val titleLeft = (page.canvas.width - childLineWidth) / 2
                page.canvas.drawText(s, titleLeft, pageParam.textHeight, titlePaint)
                pageParam.textHeight += if (index != lines.size - 1) {
                    titleHeight + CONTENT_GAP / 2
                } else {
                    titleHeight
                }
                resultPage = checkNewPage(
                    pdfDocument,
                    DEFAULT_PAGE_WIDTH,
                    DEFAULT_PAGE_HEIGHT,
                    pageParam,
                    page
                )
            }
        } else {
            val titleHeight = titlePaint.descent() - titlePaint.ascent()
            val titleLeft = (page.canvas.width - titleWidth) / 2
            page.canvas.drawText(title, titleLeft, pageParam.textHeight, titlePaint)
            pageParam.textHeight += titleHeight
        }
        return resultPage
    }

    @JvmStatic
    private fun drawContent(
        pdfDocument: PdfDocument,
        page: Page,
        pageParam: PageParam,
        data: ExportSummaryData,
    ): Page {
        var resultPage = page
        val contents = splitContent(data.summary.content).filter { it.isNotEmpty() }
        if (contents.isNotEmpty()) {
            pageParam.textHeight += TITLE_GAP
            resultPage =
                checkNewPage(pdfDocument, DEFAULT_PAGE_WIDTH, DEFAULT_PAGE_HEIGHT, pageParam, page)
        }
        val contentPaint = Paint().apply {
            textSize = CONTENT_TEXT_SIZE
            isAntiAlias = true
            color = Color.BLACK
        }
        contents.forEach { content ->
            val contentWidth = contentPaint.measureText(content)
            if (contentWidth + MARGIN_HORIZONTAL * 2 > resultPage.canvas.width) {
                val lines = lineBreak(contentPaint, content, resultPage.canvas.width)
                lines.forEachIndexed { index, line ->
                    resultPage.canvas.drawText(
                        line,
                        MARGIN_HORIZONTAL,
                        pageParam.textHeight,
                        contentPaint
                    )
                    val contentHeight = contentPaint.descent() - contentPaint.ascent()
                    pageParam.textHeight += if (index != lines.size - 1) {
                        contentHeight + CONTENT_GAP / 2
                    } else {
                        contentHeight + CONTENT_GAP
                    }
                    resultPage = checkNewPage(
                        pdfDocument,
                        DEFAULT_PAGE_WIDTH,
                        DEFAULT_PAGE_HEIGHT,
                        pageParam,
                        resultPage
                    )
                }
            } else {
                resultPage.canvas.drawText(content, MARGIN_HORIZONTAL, pageParam.textHeight, contentPaint)
                val contentHeight = contentPaint.descent() - contentPaint.ascent()
                pageParam.textHeight += contentHeight + CONTENT_GAP
                resultPage = checkNewPage(
                    pdfDocument,
                    DEFAULT_PAGE_WIDTH,
                    DEFAULT_PAGE_HEIGHT,
                    pageParam,
                    resultPage
                )
            }
        }
        return resultPage
    }

    @JvmStatic
    private fun drawImage(
        context: Context,
        pdfDocument: PdfDocument,
        page: Page,
        pageParam: PageParam,
        data: ExportSummaryData
    ): Page {
        var resultPage = page
        val bitmapPaint = Paint().apply {
            isAntiAlias = true
        }
        data.bitmap.forEach {
            //首先先把bitmap解码出来
            val bitmap = decodeBitmap(context, it)
            val left = (page.canvas.width - bitmap.width) / 2f
            val top = pageParam.textHeight
            page.canvas.drawBitmap(bitmap, left, top, bitmapPaint)
            pageParam.textHeight += bitmap.height
            resultPage =
                checkNewPage(pdfDocument, DEFAULT_PAGE_WIDTH, DEFAULT_PAGE_HEIGHT, pageParam, page)
        }
        return resultPage
    }


    @JvmStatic
    private fun checkNewPage(
        pdfDocument: PdfDocument,
        pageWidth: Int,
        pageHeight: Int,
        pageParam: PageParam,
        oldPage: Page
    ): Page {
        if (pageParam.textHeight >= pageHeight) {
            pdfDocument.finishPage(oldPage)
            val pageNumber = ++pageParam.pageNumber
            val newPageInfo =
                PdfDocument.PageInfo.Builder(pageWidth, pageHeight, pageNumber).create()
            val page = pdfDocument.startPage(newPageInfo)
            pageParam.textHeight = MARGIN_VERTICAL
            return page
        }
        return oldPage
    }

    @JvmStatic
    private fun splitContent(text: String): List<String> {
        return text.split("\n")
    }

    @JvmStatic
    private fun lineBreak(paint: Paint, text: String, width: Int): List<String> {
        val lines = mutableListOf<String>()
        val line = StringBuilder()
        text.forEach {
            val newString = line.toString()
            val textWidth = paint.measureText(newString)
            if (textWidth + MARGIN_HORIZONTAL * 2 > width) {
                lines.add(newString)
                line.clear()
                line.append(it)
            } else {
                line.append(it)
            }
        }

        // 添加最后一行
        if (line.isNotEmpty()) {
            lines.add(line.toString())
        }
        return lines
    }

    @JvmStatic
    private fun decodeBitmap(context: Context, path: String): Bitmap {
        var filePath = path
        val bitmapFile = File(path)
        if (bitmapFile.exists().not()) {
            DebugUtil.e(TAG, "decodeBitmap file not exist, use default!")
            filePath = getDefaultFileIfAttachmentLoss(context).path
        }
        val bitmap = BitmapFactory.decodeFile(filePath)
        return bitmap
    }
}