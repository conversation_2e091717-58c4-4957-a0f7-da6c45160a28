/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DurationWarnActivity
 * Description:
 * Version: 1.0
 * Date: 2024/3/8
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/8 1.0 create
 */

package com.soundrecorder.summary.ui

import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.summary.R
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.data.SummaryRecordInfo

class DurationWarnActivity : BaseActivity() {
    companion object {
        // 记录超过2小时待用户确认是否生成摘要的文件信息
        var sRecordInfo: SummaryRecordInfo? = null
    }

    var dialog: AlertDialog? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        showWarnDialog()
    }

    private fun showWarnDialog() {
        val from = sRecordInfo?.from ?: ""
        dialog = COUIAlertDialogBuilder(this).setTitle(resources.getString(com.soundrecorder.common.R.string.summary_warn_record_long))
            .setBlurBackgroundDrawable(true)
            .setPositiveButton(com.soundrecorder.common.R.string.text_generate) { _, _ ->
                sRecordInfo?.let {
                    val record = it.record ?: return@let
                    RecordSummaryManager.startSummaryNoPreCheck(it.from, record, it.convertSentence, it.markList)
                    dispatchResult(from, true, it.record?.id)
                }
                finish()
            }.setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                dispatchResult(from, false, sRecordInfo?.record?.id)
                sRecordInfo = null
                finish()
            }.setOnCancelListener {
                dispatchResult(from, false, sRecordInfo?.record?.id)
                sRecordInfo = null
                finish()
            }.show().apply {
                setCancelable(true)
                setCanceledOnTouchOutside(false)
                ViewUtils.updateWindowLayoutParams(window)
            }
    }

    private fun dispatchResult(from: String, isOk: Boolean, mediaId: Long?) {
        RecordSummaryManager.dispatchDurationWarnResult(isOk, mediaId, from)
    }

    override fun onDestroy() {
        super.onDestroy()
        dialog?.dismiss()
        dialog = null
        if (isFinishing) {
            sRecordInfo = null
        }
    }
}