/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.model

import android.content.Context
import androidx.annotation.Keep
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.JumpIntentUtil
import com.soundrecorder.summary.model.SummaryTheme.Companion.CLASSROOM
import com.soundrecorder.summary.model.SummaryTheme.Companion.INTERVIEW
import com.soundrecorder.summary.model.SummaryTheme.Companion.MEETING
import com.soundrecorder.summary.model.SummaryTheme.Companion.NORMAL
import com.soundrecorder.summary.model.SummaryTheme.Companion.PHONE
import com.soundrecorder.summary.request.database.SummaryCacheEntity
import java.lang.reflect.Type

//无效的录音
const val INVALID_MEDIA = -100

//网络异常
const val NOTWORK_ERROR = -101

//aiunit相关错误
const val LOAD_ERROR = -103

//aiunit相关错误
const val AIUNIT_ERROR = -2

//参数错误；语种不支持
const val INVALID_PARAM = 100001

//鉴权失败
const val AUTH_FAILED = 100002

//触发内容安全
const val RISK_CONTENT = 100003

//language is not support
const val LANGUAGE_NOT_SUPPORT = 100004

//输入长度超限
const val CONTENT_LENGTH_EXCEEDED = 100005

//AI模型服务异常，如google的接口异常，超过并发等;
const val AI_SERVICE_ERROR = 200001

//the llm is stop Generate content
const val AI_SERVICE_STOP = 200002
const val AI_SERVICE_TOKEN_EXCEED = 200003

//插件未初始化或者已被销毁需要重新初始化
const val PLUGIN_INIT_ERROR = 100008

//插件任务执行超时
const val REQUEST_TIMEOUT = 100009

//网络不可达
const val NETWORK_ERROR = 100010

//#文件解析失败
const val FILE_PARSE_FAILED = 100011

//##溯源文档摘要：文档在解析中或者向量化中
const val FILE_NOT_PREPARED = 100012

//录音内容较少，智能命名失败
const val CONTENT_LESS_ERROR = 100013

//server exception
const val SERVER_ERROR = 100014

//## 获取主题失败
const val LLM_GET_THEME_FAILED = 100015

//## 主题类型不支持
const val LLM_THEME_NOT_SUPPORT = 100016

//语音摘要内部错误
const val VOICE_SUMMARY_INNER_ERROR = 300000

//输入错误 # Unsupported language
const val UNSUPPORTED_LANGUAGE = 300001

//llm服务错误
const val LLM_CLIENT_ERROR = 300002

//ner服务内部错误
const val NER_CLIENT_ERROR = 300003

//无法生成标题
const val AI_TITLE_SUPPORT_ERROR = 300004

//内销文本类摘要错误 #摘要服务内部错误
const val LLM_SUMMARY_INNER_ERROR = 400000

const val SMART_EXPCETION = 60000
const val FILE_SIZE_UNSUPPORT_ZERO = 60001
const val FILE_SIZE_MAX_UNSUPPORT = 60002
const val DURATION_MIN_UNSUPPORT_ZERO = 60003
const val DURATION_MAX_UNSUPPORT = 60004
const val FILE_FORMAT_UNSUPPORT = 60005
const val FILE_NOT_EXIST = 60006
const val CONVERT_TEXT_UNSUPPORT = 60007


/**
 * 数据示例
 * 总结：范先生作为中信银行信用卡中心的业务代表，向何魏先生推荐了一笔额外的现金贷款服务。\n核心事件：\n1.范先生表示何魏先生的信用记录良好，因此向他提供了额外的一笔现金贷款。\n2.何魏先生拒绝了这项服务，因为他没有资金需求，并且对中信银行的信用卡体验不佳。\n待办事项：\n1.范先生可能需要记录下何魏先生的反馈，以便后续改进服务或调整营销策略。
 */
@Keep
data class SummaryModel(

    val id: Long,

    val mediaId: Long,

    /**
     * 总结
     */
    val summary: String,

    /**
     * 待办事件
     */
    val agentEvents: List<SummaryAgentEvent>,

    /**
     * 实体，可点击
     */
    val entities: List<SummaryEntity>,

    /**
     * 溯源
     */
    val summaryTrace: List<SummaryTrace>,

    /**
     * 主题
     */
    val theme: SummaryTheme? = null,

    /**
     * 摘要的时间
     */
    val summaryTime: Long,

    val title: String? = null,

    val language: String? = null,

    val extra: HashMap<String, Any>? = null
)

/**
 * 待办事件
 */
@Keep
data class SummaryAgentEvent(
    /**
     * 待办描述
     */
    val agent: String,

    /**
     * 是否完成
     */
    var isDone: Boolean,
)

/**
 * 摘要实体
 * 示例：
 * [
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 3
 * 	},
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 52
 * 	}
 *]
 */
@Keep
data class SummaryEntity(
    val name: String,
    val turnId: Int,
    val indexStart: Int,
    val indexEnd: Int,
    val timestamp: Long,
    val type: String,
    val summaryIndex: Int
) {
    companion object {
        const val SCHEDULE = "时间"
        const val ADDRESS = "地址"
        const val EXPRESS = "express_delivery"
        const val PHONE = "phone_number"
        const val EMAIL = "email"
        const val WEB = "url"
        const val TIME = "Time" //外销时间
        const val EXPORT_ADDRESS = "Address" //外销地址
        const val NO_TIME_SCHEDULE = "年月日" //用于只有日期的实体
        //留下做测试，先不删
        const val NAME = "人名" //用于只有日期的实体
    }

    fun supportEntity(): Boolean {
        val typeIdRight = (type == SCHEDULE
                || type == ADDRESS
                || type == EXPRESS
                || type == EMAIL
                || type == EXPORT_ADDRESS
                || type == NO_TIME_SCHEDULE)
        val contentIsRight = name.isNotEmpty()
        val phoneNumberIsRight =
            kotlin.runCatching { (type == PHONE) && name.toInt() > 0 }.getOrDefault(false)
        val webUrlIsRight = (type == WEB) && JumpIntentUtil.isValidUrl(name)
        return (typeIdRight && contentIsRight) || phoneNumberIsRight || webUrlIsRight
    }
}

@Keep
data class SummaryTrace(
    /**
     * 录音文本开始时间
     */
    val startTime: Long,

    /**
     * 录音文本结束时间
     */
    val endTime: Long,

    /**
     * 匹配的原文
     */
    val chunkText: String,

    /**
     * 溯源的文本
     */
    val traceText: String
)

@Keep
data class SummaryTheme(
    /**
     * 识别的主题
     */
    val style: Int = -1
) {
    companion object {
        const val NORMAL_SIMPLE = -100
        const val NORMAL = 1000
        const val PHONE = 1001
        const val MEETING = 1002
        const val CLASSROOM = 1003
        const val INTERVIEW = 1004
        const val CONFERENCE = 1005
        const val PAPERS = 1006

        @JvmStatic
        fun getTitle(context: Context, style: Int): String {
            return when (style) {
                NORMAL_SIMPLE -> context.getString(com.soundrecorder.common.R.string.summary_style_sample)
                NORMAL -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
                PHONE -> context.getString(com.soundrecorder.common.R.string.summary_style_phone_call)
                MEETING -> context.getString(com.soundrecorder.common.R.string.summary_style_meeting)
                CLASSROOM -> context.getString(com.soundrecorder.common.R.string.summary_style_course)
                INTERVIEW -> context.getString(com.soundrecorder.common.R.string.summary_style_interviews)
                else -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
            }
        }
    }
}

@Keep
data class SummarySupportTheme(
    val code: Int,
    val name: Map<String, String>
) {
    fun isSupportCode(): Boolean {
        return code == NORMAL
                || code == PHONE
                || code == MEETING
                || code == CLASSROOM
                || code == INTERVIEW
    }
}

/**
 * stream
 */
@Keep
data class SummaryStream(
    val stream: String,
    val extra: Map<String, Any>? = null
)

/**
 * error
 */
@Keep
data class SummaryError(
    val errorCode: Int,
    val errorMsgRes: Int,
    val canRetry: Boolean
)

@Keep
data class SummaryCountModel(
    val currentPosition: Int,
    val count: Int,
)

@Keep
data class SummaryStop(
    val reason: Int,
) {
    companion object {
        //正常结束
        const val REASON_FINISH = 1
        //用户停止
        const val REASON_STOP = 2
        //错误结束
        const val REASON_ERROR = 3
        //重试错误结束
        const val REASON_RETRY_ERROR = 4
    }
}

fun SummaryCacheEntity.toSummaryModel(): SummaryModel {
    val summaryContent = this.summaryContent ?: return SummaryModel(
        this.id,
        this.mediaId,
        summary = "",
        emptyList(),
        emptyList(),
        emptyList(),
        SummaryTheme(-1),
        -1
    )
    val summaryAgent = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryAgentEvent>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryAgentEvent>>(this.summaryAgent, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase agent ${this.summaryAgent}")
    }.getOrDefault(emptyList())

    val summaryEntity = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryEntity>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryEntity>>(this.summaryEntity, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase entity ${this.summaryEntity}")
    }.getOrDefault(emptyList())

    val summaryTrace = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryTrace>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryTrace>>(this.summaryTrace, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase trace ${this.summaryTrace}")
    }.getOrDefault(emptyList())

    val theme = SummaryTheme(this.summaryStyle)
    val time = this.timeStamp
    return SummaryModel(
        this.id,
        this.mediaId,
        summaryContent,
        summaryAgent,
        summaryEntity,
        summaryTrace,
        theme,
        time,
        title = this.summaryTitle,
        language = this.summaryLanguage
    )
}