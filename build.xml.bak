<?xml version="1.0" encoding="UTF-8"?>
<project name="NewSoundRecorder" default="help">

    <!-- 指定 OPPO SDK 所在的目录，路径一定要正确 -->
    <property name="sdk.dir" value="/work/ant-workspace/linux-sdk/android-sdk_oppo-ROM-5.0" />

    <!-- 指定提供jar或者so的路径，需要确保该路径存在 -->
    <property name="static.libraries.dir" value="\\172.17.121.34\app_jar_so"/>
    <!-- 存放只参与编译不一起打包的jar库，默认为user-libs，也可以自己指定 -->
    <property name="library.dir" value="" />
    <!--  指定只需要参与编译的jar库，与上面那个library.dir的jar功能类似  -->
    <property name="local.libraries" value="" />
    <!-- 指定参与编译和打包需要的jar库和so库，默认存放在libs目录-->
    <property name="local.static.libraries" value="rom/master/pub:com.coloros.statistics.jar
             rom/stable/V1.0:com.color.commons.jar
             rom/master/pub:com.color.common.sdcard.jar" />

    <!-- true: 编译jar包时，将libs目录下的jar库一起打包
         false: 只参与编译，不打包。-->
    <property name="package.libs.jar" value="true"/>

    <!-- 增加os3.0版本标识-->
    <property name="oppo.os.version" value="3.0" />

    <!-- 这里主要是与APK签名相关，release 会用以下指定签名文件进行签名 ，debug是不会用到以下签名文件进行签名。
         mykey.path是指定签名文件存放的路径;mykey 是这个工程需要的签名文件
                               如果不用提供的key进行签名需要自己指定key.store和key.alias以及对应的密码   -->
    <property name="mykey.path" value="/opt/linux_sdk/keystore" />
    <property name="mykey" value="platform" />
    <property name="key.store" value="${mykey.path}\\${mykey}.keystore" />
    <property name="key.store.password" value="android" />
    <property name="key.alias" value="android${mykey}key" />
    <property name="key.alias.password" value="android" />

    <!-- 指定对应SDK版本号，如android 5.0对应的是 21. -->
    <property name="target" value="android-22" />

    <!-- 指定应用预编译时使用的OPPO SDK版本-->
    <property name="app.prebuild.sdk.version" value="android-22 android-23" />

    <!-- 指定应用编译的资源目录，默认是res -->
    <property name="resource.absolute.oppo.dir" value="res" />

    <!-- support.internal -> true 加载oppo internal api，否则不加载 -->
    <property name="support.internal" value="true" />

    <!-- quick check on sdk.dir -->
    <fail
            message="sdk.dir is missing. Make sure to generate local.properties using 'android update project' or to inject it through an env var"
            unless="sdk.dir"
    />

    <import file="custom_rules.xml" optional="true" />

    <!-- version-tag: 1 -->
    <import file="${sdk.dir}/tools/ant/build.xml" />

	<!-- 指定proguard配置文本 -->
	<!-- <property name="proguard.config" value="proguard.txt" />-->

	<!-- proguard 开关，true->进行proguard混淆 -->
	<!-- <property name="proguard.enabled" value="true" /> -->

</project>
