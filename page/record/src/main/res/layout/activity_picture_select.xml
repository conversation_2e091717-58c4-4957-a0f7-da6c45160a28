<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="SpUsage,UnusedAttribute">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_background_color">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_background_color"
            app:elevation="0dp"
            app:layout_behavior="com.soundrecorder.setting.opensource.SettingRecordBehavior">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp50" />

            <View
                android:id="@+id/divider_line"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_background_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="@dimen/common_margin"
                android:layout_marginRight="@dimen/common_margin"
                android:alpha="0"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false" />
        </com.google.android.material.appbar.AppBarLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingTop="@dimen/dp62">

            <androidx.recyclerview.widget.COUIRecyclerView
                android:id="@+id/rv_pop_pictures"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <View
                android:id="@+id/bottomDivider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:alpha="1"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false"
                android:visibility="gone"
                tools:visibility="visible" />

            <com.coui.appcompat.button.COUIButton
                android:id="@+id/btn_add"
                style="?android:attr/borderlessButtonStyle"
                android:layout_width="@dimen/dp296"
                android:layout_height="@dimen/dp44"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp12"
                android:layout_marginBottom="@dimen/dp24"
                android:background="@drawable/selector_button_picture_select"
                android:forceDarkAllowed="false"
                android:text="@string/photo_mark_recommend_add"
                app:animEnable="true"
                android:textSize="@dimen/sp16"
                app:expandOffset="0dp" />
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>