/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SmallCardDataUtilTest
 * Description:
 * Version: 1.0
 * Date: 2023/10/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/10/31 1.0 create
 */

package com.soundrecorder.record.card.small

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.record.R
import com.soundrecorder.record.shadows.ShadowClearDataUtils
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import io.mockk.every
import io.mockk.mockk
import oplus.multimedia.soundrecorder.card.small.RecorderState
import oplus.multimedia.soundrecorder.card.small.SaveFileState
import oplus.multimedia.soundrecorder.card.small.SmallCardDataUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowClearDataUtils::class]
)
class SmallCardDataUtilTest {
    private var context: Context? = null

    private val recorderViewModelApi = mockk<RecorderServiceInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { recorderViewModelApi }
        })
    }

    @Before
    fun setup() {
        context = ApplicationProvider.getApplicationContext()
        startKoin(koinApp)
    }

    @After
    fun release() {
        context = null
        stopKoin()
    }

    @Test
    fun should_correct_when_getRecordStateValue() {
        every { recorderViewModelApi.getCurrentStatus() } returnsMany listOf(RecorderState.INIT, RecorderState.RECORDING)
        every { recorderViewModelApi.getRecordStatusBeforeSaving() } returns RecorderState.PAUSED

        // not saving state
        Assert.assertEquals(
            RecorderState.INIT,
            SmallCardDataUtil.getRecordStateValue(com.soundrecorder.modulerouter.recorder.SaveFileState.INIT).second
        )
        Assert.assertEquals(
            RecorderState.RECORDING,
            SmallCardDataUtil.getRecordStateValue(com.soundrecorder.modulerouter.recorder.SaveFileState.INIT).second
        )
        // saving state
        Assert.assertEquals(
            RecorderState.PAUSED,
            SmallCardDataUtil.getRecordStateValue(com.soundrecorder.modulerouter.recorder.SaveFileState.SHOW_LOADING_DIALOG).second
        )
    }

    @Test
    fun should_correct_when_getSaveStateValue() {
        Assert.assertEquals(
            SaveFileState.SUCCESS,
            SmallCardDataUtil.getSaveStateValue(true, com.soundrecorder.modulerouter.recorder.SaveFileState.INIT)
        )

        Assert.assertEquals(
            SaveFileState.INIT,
            SmallCardDataUtil.getSaveStateValue(false, com.soundrecorder.modulerouter.recorder.SaveFileState.INIT)
        )

        Assert.assertEquals(
            SaveFileState.INIT,
            SmallCardDataUtil.getSaveStateValue(false, -999)
        )

        Assert.assertEquals(
            SaveFileState.START_LOADING,
            SmallCardDataUtil.getSaveStateValue(
                false,
                com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING
            )
        )

        Assert.assertEquals(
            SaveFileState.SHOW_DIALOG,
            SmallCardDataUtil.getSaveStateValue(
                false,
                com.soundrecorder.modulerouter.recorder.SaveFileState.SHOW_LOADING_DIALOG
            )
        )

        Assert.assertEquals(
            SaveFileState.SUCCESS,
            SmallCardDataUtil.getSaveStateValue(
                false,
                com.soundrecorder.modulerouter.recorder.SaveFileState.SUCCESS
            )
        )

        Assert.assertEquals(
            SaveFileState.ERROR,
            SmallCardDataUtil.getSaveStateValue(false, com.soundrecorder.modulerouter.recorder.SaveFileState.ERROR)
        )
    }

    @Test
    fun should_correct_when_getRecordTimeTextAndTalkDec() {
        val context = context ?: return
        SmallCardDataUtil.smallCardVersionCode = 2
        var result = SmallCardDataUtil.getRecordTimeTextAndTalkDec(context, 0, -1)
        Assert.assertTrue(result.first.contains("."))

        SmallCardDataUtil.smallCardVersionCode = 3
        result = SmallCardDataUtil.getRecordTimeTextAndTalkDec(context, 0, RecorderState.PAUSED)
        Assert.assertFalse(result.first.contains("."))
    }

    @Test
    fun should_correct_when_getRecordButtonData() {
        val context = context ?: return
        SmallCardDataUtil.smallCardVersionCode = -1
        var result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.RECORDING,
            com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING,
            false
        )
        Assert.assertTrue(result.first == R.drawable.breeno_card_record_recording_enable)

        result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.RECORDING,
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT,
            false
        )
        Assert.assertTrue(result.first == R.drawable.breeno_card_record_recording)

        result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.PAUSED,
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT,
            true
        )
        Assert.assertTrue(result.first == R.drawable.ic_breeno_card_record_enable_pause)

        result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.PAUSED,
            com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING,
            false
        )
        Assert.assertTrue(result.first == R.drawable.ic_breeno_card_record_enable_pause)

        result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.PAUSED,
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT,
            false
        )
        Assert.assertTrue(result.first == R.drawable.breeno_card_record_pause)

        result = SmallCardDataUtil.getRecordButtonData(
            context,
            RecorderState.INIT,
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT,
            true
        )
        Assert.assertTrue(result.first == R.drawable.breeno_card_record_init)
    }

    @Test
    fun should_correct_when_getMarkButtonData() {
        every { recorderViewModelApi.isMarkEnabledFull() } returnsMany listOf(true, false)

        var result = SmallCardDataUtil.getMarkButtonData()
        Assert.assertTrue(result.first)

        result = SmallCardDataUtil.getMarkButtonData()
        Assert.assertFalse(result.first)
    }

    @Test
    fun should_correct_when_getSaveButtonData() {
        var result = SmallCardDataUtil.getSaveButtonData(com.soundrecorder.modulerouter.recorder.SaveFileState.INIT)
        Assert.assertTrue(result.first)

        result =
            SmallCardDataUtil.getSaveButtonData(com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING)
        Assert.assertFalse(result.first)
    }

    @Test
    fun should_correct_when_getSaveLoadingText() {
        val context = context ?: return
        // versionCode<3
        SmallCardDataUtil.smallCardVersionCode = 2
        var result = SmallCardDataUtil.getSaveLoadingText(
            context,
            com.soundrecorder.modulerouter.recorder.SaveFileState.INIT
        )
        Assert.assertTrue(result.isBlank())
        // versionCode<3
        result = SmallCardDataUtil.getSaveLoadingText(
            context,
            com.soundrecorder.modulerouter.recorder.SaveFileState.SUCCESS
        )
        Assert.assertTrue(result.isNotBlank())
        SmallCardDataUtil.smallCardVersionCode = 3
        // versionCode=3
        result = SmallCardDataUtil.getSaveLoadingText(
            context,
            com.soundrecorder.modulerouter.recorder.SaveFileState.SUCCESS
        )
        Assert.assertTrue(result.isNotBlank())
    }

    @Test
    fun should_correct_when_getSaveSuccessViewData() {
        val context = context ?: return
        var result = SmallCardDataUtil.getSaveSuccessViewData(context, SaveFileState.INIT, "")
        Assert.assertTrue(result.first.isBlank())

        // versionCode<3
        SmallCardDataUtil.smallCardVersionCode = 2
        result = SmallCardDataUtil.getSaveSuccessViewData(context, SaveFileState.SUCCESS, "11")
        Assert.assertTrue(result.first.isNotBlank())
        // versionCode=3
        SmallCardDataUtil.smallCardVersionCode = 3
        result = SmallCardDataUtil.getSaveSuccessViewData(context, SaveFileState.SUCCESS, "11")
        Assert.assertFalse(result.first.isBlank())
    }


    @Test
    fun should_correct_when_getLastOneAmp() {
        // versionCode=3
        SmallCardDataUtil.smallCardVersionCode = 3
        Assert.assertEquals(0, SmallCardDataUtil.getLastOneAmp())

        // versionCode<3
        SmallCardDataUtil.smallCardVersionCode = 2
        every { recorderViewModelApi.isAlreadyRecording() } returnsMany listOf(true, false)
        every { recorderViewModelApi.getAmplitudeList() } returns mutableListOf(1)
        // recording state
        Assert.assertEquals(1, SmallCardDataUtil.getLastOneAmp())

        // not recording
        Assert.assertEquals(0, SmallCardDataUtil.getLastOneAmp())
    }

    @Test
    fun should_correct_when_isSmallVersion3OrLater() {
        SmallCardDataUtil.smallCardVersionCode = -1
        Assert.assertFalse(SmallCardDataUtil.isSmallVersion3OrLater())

        SmallCardDataUtil.smallCardVersionCode = 3
        Assert.assertTrue(SmallCardDataUtil.isSmallVersion3OrLater())
    }

    @Test
    fun should_correct_when_getLastAmpListAndMarkList() {
        SmallCardDataUtil.smallCardVersionCode = -1
        Assert.assertTrue(SmallCardDataUtil.getLastAmpListAndMarkList().first <= 0)

        SmallCardDataUtil.smallCardVersionCode = 3
        every { recorderViewModelApi.getAmplitudeList() } returnsMany listOf(listOf(1), getMoreAmpList())
        val markDataBean = MarkDataBean(1)
        every { recorderViewModelApi.getMarkData() } returnsMany listOf(
            listOf(),
            listOf(
                markDataBean,
                markDataBean,
                markDataBean,
                markDataBean,
                markDataBean,
                markDataBean,
                markDataBean
            )
        )
        Assert.assertEquals(1, SmallCardDataUtil.getLastAmpListAndMarkList().first)
        val result = SmallCardDataUtil.getLastAmpListAndMarkList()
        Assert.assertEquals(5, result.third.size)
        Assert.assertEquals(50, result.second.size)
    }

    private fun getMoreAmpList(): List<Int> {
        return mutableListOf<Int>().apply {
            for (i in 0..51) {
                add(i)
            }
        }
    }
}