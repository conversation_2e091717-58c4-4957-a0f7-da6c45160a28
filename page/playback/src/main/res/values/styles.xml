<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!--播放页面底部工具栏TextView基本样式-->
    <style name="textView_playBack_bottomTool" parent="textView_playBack_bottomTool_base">
        <item name="android:minWidth">@dimen/botton_min_width</item>
    </style>

    <style name="textView_playBack_bottomTool_base">
        <item name="android:maxLines">@integer/botton_text_line_limit</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textSize">@dimen/botton_text_size</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:drawablePadding">@dimen/dp6</item>
        <item name="android:textColor">@color/coui_color_secondary_neutral</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="textView_playBack_bottomTool_force_dark" parent="textView_playBack_bottomTool_base">
        <item name="android:textColor">@color/coui_color_secondary_neutral</item>
    </style>

    <style name="textView_playBack_bottomTool_minWidth_force_dark" parent="textView_playBack_bottomTool">
        <item name="android:textColor">@color/coui_color_secondary_neutral</item>
    </style>

    <style name="detail_title" parent="android:TextAppearance">
        <item name="android:textSize">@dimen/sp16</item>
        <item name="android:textColor">@color/percent_85_black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp50</item>
    </style>

    <style name="detail_item_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp14</item>
        <item name="android:textColor">@color/coui_color_label_secondary</item>
        <item name="android:layout_marginTop">@dimen/dp10</item>
        <item name="android:layout_marginStart">@dimen/dp24</item>
        <item name="android:layout_marginEnd">@dimen/dp24</item>
    </style>

    <style name="detail_item_content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp16</item>
        <item name="android:lineSpacingExtra">0dp</item>
        <item name="android:textFontWeight">500</item>
        <item name="fontFamily">sans-serif-medium</item>
        <item name="android:textColor">@color/coui_color_label_primary</item>
        <item name="android:layout_marginTop">@dimen/dp4</item>
        <item name="android:layout_marginBottom">@dimen/dp10</item>
        <item name="android:layout_marginStart">@dimen/dp24</item>
        <item name="android:layout_marginEnd">@dimen/dp24</item>
    </style>

    <style name="convert_text_time_appearance_focused">
        <item name="android:fontWeight">500</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">@dimen/sp12</item>
        <item name="android:lineSpacingMultiplier">@dimen/coui_spacing_multiplier_body_s</item>
        <item name="android:textColor">?attr/couiColorPrimary</item>
    </style>

    <style name="convert_text_time_appearance_not_focused">
        <item name="android:fontWeight">400</item>
        <item name="android:textSize">@dimen/sp12</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:lineSpacingMultiplier">@dimen/coui_spacing_multiplier_body_xs</item>
        <item name="android:textColor">?attr/couiColorLabelSecondary</item>
    </style>

</resources>