<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_background_color">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.1dp"
            android:background="@color/percent_15_black"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/rv_convert_content"
            layout="@layout/fragment_convert_content"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/ll_bottom_area"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintWidth_default="percent"
            app:layout_constraintWidth_percent="@dimen/screen_width_percent_parentchild"
            />

        <View
            android:id="@+id/background_mask"
            style="@style/BackgroundMaskStyle"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.soundrecorder.playback.newconvert.search.view.ConvertSearchBottomArea
            android:id="@+id/ll_bottom_area"
            android:layout_width="match_parent"
            android:layout_height="@dimen/play_convert_search_bottom_area_height"
            android:background="@color/convert_search_layout_bg"
            android:padding="@dimen/play_convert_search_bottom_area_padding"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>