<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/dp16" >

    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
            android:id="@+id/animator_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:background="@drawable/background_convert_speaker"
            app:max_padding_end="@dimen/dp14">

            <LinearLayout
                android:id="@+id/ll_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp8"
                    android:src="@drawable/ic_circle1" />

                <TextView
                    android:id="@+id/tv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                    android:paddingStart="@dimen/dp8"
                    android:paddingTop="@dimen/dp6"
                    android:paddingEnd="@dimen/dp10"
                    android:paddingBottom="@dimen/dp6"
                    android:textFontWeight="500"
                    android:textColor="@color/percent_85_black"
                    android:textSize="@dimen/sp10"
                    android:fontFamily="sans-serif-medium"
                    tools:text="@string/convert_speaker" />

            </LinearLayout>
        </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

        <TextView
            android:id="@+id/start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:lines="1"
            android:paddingTop="@dimen/dp4"
            android:paddingBottom="@dimen/dp4"
            android:contentDescription=""
            android:textAppearance="@style/convert_text_time_appearance_not_focused"
            android:fontFeatureSettings="tnum" />
    </LinearLayout>


    <com.soundrecorder.playback.newconvert.view.TextImageMixLayout
        android:id="@+id/item_text_image_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:text_background_color="?attr/couiColorPrimary"
        >
    </com.soundrecorder.playback.newconvert.view.TextImageMixLayout>



</LinearLayout>