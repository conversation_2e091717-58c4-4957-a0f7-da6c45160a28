<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/sv_converting"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:paddingStart="@dimen/responsive_ui_margin_large"
    android:paddingEnd="@dimen/responsive_ui_margin_large"
    android:scrollbars="none">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_converting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.55" />

        <com.soundrecorder.common.widget.OSImageView
            android:id="@+id/loadingView"
            android:layout_width="@dimen/os_image_def_width"
            android:layout_height="@dimen/os_image_def_height"
            android:importantForAccessibility="no"
            app:anim_end_frame="240"
            app:anim_frame_duration="240"
            app:anim_frame_rate="60"
            app:anim_json_repeatCount="-1"
            app:anim_raw_json="@raw/ic_converting_content"
            app:anim_repeatMode="restart"
            app:anim_start_frame="36"
            app:layout_constraintBottom_toTopOf="@id/guide_converting"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/ll_converting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/guide_converting">

            <TextView
                android:id="@+id/convert_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp15"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/sp4"
                android:text="@string/transfer_text_progress"
                android:textColor="@color/coui_color_primary_neutral"
                style="@style/couiTextAppearanceHeadline4" />

            <TextView
                android:id="@+id/convert_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/sp4"
                android:text="@string/transferring_content_v2"
                android:textColor="@color/coui_color_secondary_neutral"
                style="@style/couiTextAppearanceBody" />

            <TextView
                android:id="@+id/convert_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:fontFamily="sans-serif-medium"
                android:lineSpacingExtra="@dimen/sp4"
                android:paddingStart="@dimen/dp12"
                android:paddingTop="@dimen/dp4"
                android:paddingEnd="@dimen/dp12"
                android:paddingBottom="@dimen/dp4"
                android:text="@string/convert_text_cancel"
                android:textColor="?attr/couiColorPrimaryText"
                android:textSize="@dimen/dp14" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>