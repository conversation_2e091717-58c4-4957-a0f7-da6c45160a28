/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackPlayerController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback

import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.player.WavePlayerController

class PlaybackPlayerController(playerCallback: PlayerHelperBasicCallback?) :
    WavePlayerController(playerCallback) {
    override val TAG: String = "PlaybackPlayerController"

    override fun onPlayError(extra: Int) {
        val isAACError = dealAACErrorSeekToDuration(extra)
        if (!isAACError) {
            releasePlay()
        }
        super.onPlayError(extra)
    }

    override fun doContinuePlay() {
        super.doContinuePlay()
        BuryingPoint.addRecordPlayState(RecorderUserAction.VALUE_RECORD_PLAY_PLAYBACK)
    }

    override fun doPausePlay() {
        super.doPausePlay()
        BuryingPoint.addRecordPlayState(RecorderUserAction.VALUE_RECORD_PLAY_PAUSE_PLAYBACK)
    }
}