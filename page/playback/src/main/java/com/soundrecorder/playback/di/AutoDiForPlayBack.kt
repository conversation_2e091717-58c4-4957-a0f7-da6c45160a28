/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForPlayBack.kt
 * * Description : AutoDiForPlayBack
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.playback.di

import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.playback.PlaybackApi
import org.koin.dsl.module

object AutoDiForPlayBack {
    val playBackModule = module {
        single<PlayBackInterface>(createdAtStart = true) {
            PlaybackApi
        }
    }
}