/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.view.View
import android.widget.ImageView
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.shape.ShapeAppearanceModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.common.databean.ConvertContentItem

class ShapableImageViewSetupHelper {
    companion object {
        const val TAG = "BackGroundTextViewSetupHelper"
    }

    interface OnShapableImageViewClickListener {
        fun onImageViewClick(view: ImageView, convertContentItem: ConvertContentItem?, currentDataBean: MarkDataBean)
    }

    fun setUpImageView(
        context: Context,
        currentItem: ConvertContentItem.ImageMetaData?,
        convertContentItem: ConvertContentItem?,
        imageView: ShapeableImageView?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        lastItemType: Int,
        onClickListener: OnShapableImageViewClickListener?,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    ): Pair<View, ImageLoadData>? {
        DebugUtil.i(TAG, "checkAndAddImageView isLastOne $isLastOne")
        if (currentItem == null || convertContentItem == null || imageView == null) {
            DebugUtil.i(TAG, "checkAndAddImageView currentItem or convertContentItem null or imageView null, return")
            return null
        }
        var currentPictureMarkDataBean: MarkDataBean = currentItem.imageItem ?: return null

        ImageTextItemLayoutParamUtil.setupLayoutParamForImageItem(
            context,
            currentItem,
            isFirstOne,
            isLastOne,
            lastItemType,
            drawAttr,
            imageView
        )

        var maxtWithAndRatio = ImageWithHeightCaculateUtil.getImageViewMaxtWithAndRatio(context)
        var imageViewConfig = ImageWithHeightCaculateUtil.caculateImageViewWithAndHeight(
            currentPictureMarkDataBean,
            maxtWithAndRatio
        )
        if (imageViewConfig.needFitCenter) {
            imageView.scaleType = ImageView.ScaleType.CENTER_CROP
        }
        //Coil加载时Size选项
        var imageLoadData = ImageLoadData(
            FileUtils.getAppFile(currentPictureMarkDataBean.pictureFilePath, false),
            imageViewConfig.imageViewWidth / TextImageMixLayoutHelper.SAMPLE_SCALE,
            imageViewConfig.imageViewHeight / TextImageMixLayoutHelper.SAMPLE_SCALE
        )
        imageView.shapeAppearanceModel = ShapeAppearanceModel.Builder()
            .setAllCornerSizes(context.resources.getDimension(com.support.appcompat.R.dimen.coui_round_corner_m))
            .build()

        loadImageView(imageView, imageLoadData)
        //设置点击事件
        imageView.setOnClickListener {
            onClickListener?.onImageViewClick(
                imageView,
                convertContentItem,
                currentPictureMarkDataBean
            )
        }
        return Pair(imageView, imageLoadData)
    }

    fun loadImageView(imageView: ShapeableImageView, imageLoadData: ImageLoadData) {
        DebugUtil.i(TAG, "loadImageView imageLoadData $imageLoadData")
        imageView.into(imageLoadData)
    }
}