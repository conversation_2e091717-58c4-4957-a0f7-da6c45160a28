/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameManagerImpl
 * * Description: SmartNameManagerImpl
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.smartname

import android.app.Activity
import androidx.fragment.app.FragmentActivity
import com.soundrecorder.base.BaseApplication.getAppContext
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyCallback
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.smartname.IUnifiedSummaryCallBack
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils

class SmartNameManagerImpl : ISmartNameManager {

    companion object {
        private const val TAG = "SmartNameManagerImpl"
    }

    private var mSelectMediaIdList: MutableList<Long>? = null
    private var mUnifiedSummaryManager: IUnifiedSummaryCallBack? = null
    private var isOpenSwitch: Boolean = false
    private val privacyPolicyAction by lazy {
        Injector.injectFactory<PrivacyPolicyInterface>()
    }

    /**
     * 智能命名入口
     * selectedMediaIdList: 选中的录音
     * convetAiTitle: 是否生成智能标题
     */
    override fun convertStartSmartNameClickHandle(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        pageFrom: Int?,
        isOpenSwitch: Boolean
    ) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        this.isOpenSwitch = isOpenSwitch
        DebugUtil.d(TAG, "convertStartSmartNameClickHandle, selectedMediaIdList:${selectedMediaIdList.size}, isOpenSwitch:$isOpenSwitch")
        this.mSelectMediaIdList = selectedMediaIdList
        if (!BaseUtil.isEXP() && !PermissionUtils.hasFuncTypePermission(FUNC_TYPE_SMART_SHORTHAND)) {
            showStatementWithFirstUseConvert(activity)
        } else {
            startOrResumeConvertSmartName(activity, mSelectMediaIdList, isOpenSwitch)
        }
    }

    /**
     * 初次使用转文本，需要显示声明弹窗
     */
    private fun showStatementWithFirstUseConvert(activity: Activity?) {
        val fragmentActivity = activity as? FragmentActivity ?: return
        val functionPrivacy = privacyPolicyAction?.newFunctionPrivacyDelegate(FUNC_TYPE_SMART_SHORTHAND)
        functionPrivacy?.showFunctionPrivacyDialog(
            fragmentActivity,
            object : IFunctionPrivacyCallback {
                override fun onPrivacyAgreed() {
                    startOrResumeConvertSmartName(activity, mSelectMediaIdList, isOpenSwitch)
                }

                override fun onPrivacyRejected() {
                    DebugUtil.d(TAG, "showStatementWithFirstUseConvert onPrivacyRejected")
                }
            })
    }

    /**
     * 检查智能命名插件是否下载
     */
    override fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)?, isOpenSwitch: Boolean) {
        if (activity == null) {
            return
        }
        if (mUnifiedSummaryManager == null) {
            DebugUtil.d(TAG, "showSmartNameDialog, newUnifiedSummaryManager")
            mUnifiedSummaryManager = Injector.injectFactory<SmartNameAction>()?.newUnifiedSummaryManager()
        }
        mUnifiedSummaryManager?.showAiUnitPluginsDialog(activity, NamePluginDownloadCallback(callback), isOpenSwitch = isOpenSwitch)
    }

    /**
     * 开始智能命名
     */
    override fun startOrResumeConvertSmartName(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        isOpenSwitch: Boolean
    ) {
        if (activity == null || selectedMediaIdList.isNullOrEmpty()) {
            DebugUtil.d(TAG, "startOrResumeConvertSmartName, activity or selectedMediaIdList is null")
            return
        }
        if (NetworkUtils.isNetworkInvalid(getAppContext())) {
            ToastManager.showShortToast(getAppContext(), com.soundrecorder.common.R.string.no_network_intelligent_name_failure)
            return
        }
        checkPluginsDownload(activity, { download ->
            if (download) {
                DebugUtil.d(TAG, "checkPluginsDownload, thread:${Thread.currentThread()}")
                SmartNameManger.getInstance().startOrResumeConvertSmartName(selectedMediaIdList)
            }
        }, isOpenSwitch)
    }

    override fun doClickPermissionConvertOK(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        pageFrom: Int?
    ) {
        DebugUtil.d(TAG, "doClickPermissionConvertOK, isOpenSwitch:$isOpenSwitch")
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        PermissionUtils.setConvertGrantedStatus(getAppContext())
        PermissionUtils.setNetWorkGrantedStatus(getAppContext(), true)
        CloudConfigUtils.updateConvertConfig()
        isOpenSwitch = false
    }

    override fun release() {
        DebugUtil.d(TAG, "release")
        mSelectMediaIdList?.clear()
        mUnifiedSummaryManager?.releaseAllDialog()
        mUnifiedSummaryManager = null
    }

    override fun releaseAll() {
        release()
        SmartNameManger.getInstance().release()
    }

    private class NamePluginDownloadCallback(private val callback: ((Boolean) -> Unit)?) : IPluginDownloadCallback {
        override fun onDownLoadResult(result: Boolean) {
            DebugUtil.d(TAG, "onDownLoadResult result: $result")
            callback?.invoke(result)
        }
    }
}