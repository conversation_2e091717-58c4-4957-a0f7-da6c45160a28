/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayBackEmptyFragment
 * Description:
 * Version: 1.0
 * Date: 2022/12/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/12/6 1.0 create
 */

package com.soundrecorder.playback.empty

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentEmptyPlaybackBinding

/**
 * 未选中录音 缺省图页面
 */
class PlayBackEmptyFragment : Fragment() {
    private val mLogTag = "PlayBackEmptyFragment"
    private lateinit var binding: FragmentEmptyPlaybackBinding

    private val mLayoutChangeListener: WindowLayoutChangeListener = object : WindowLayoutChangeListener() {
        override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
            context?.let {
                val width = it.px2dp(rect.width()).toInt()
                val height = it.px2dp(rect.height()).toInt()
                binding.ivEmptyPlayback.setScaleByEmptySize(width, height, mLogTag)
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rooView = inflater.inflate(R.layout.fragment_empty_playback, container, false)
        binding = FragmentEmptyPlaybackBinding.bind(rooView)
        return rooView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        DebugUtil.d(mLogTag, "onViewCreated ")
        binding.ivEmptyPlayback.initImageResource()
        binding.svEmptyPlayback.addOnLayoutChangeListener(mLayoutChangeListener)
    }

    override fun onDestroyView() {
        DebugUtil.d(mLogTag, "onDestroyView ")
        super.onDestroyView()
        binding.svEmptyPlayback.removeOnLayoutChangeListener(mLayoutChangeListener)
    }
}