/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.playback.cloudconfig.CloudConfigUtils
import com.soundrecorder.playback.empty.PlayBackEmptyFragment
import com.soundrecorder.playback.newconvert.ConvertUtils
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.smartname.SmartNameManagerImpl
import com.soundrecorder.wavemark.uti.CallNameParseHelper
import oplus.multimedia.soundrecorder.playback.mute.MuteCacheManager
import oplus.multimedia.soundrecorder.playback.mute.detector.MuteDataDetectorWorker

object PlaybackApi : PlayBackInterface {

    private const val TAG = "PlaybackApi"

    override fun createPlayBackIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClassName(context.packageName, PlaybackActivity::class.java.name)
        return jumpIntent
    }

    override fun isPlaybackActivity(context: Context): Boolean = context is PlaybackActivity

    override fun cancelAllConvertTask() {
        ConvertUtils.cancelAllConvertTask()
    }

    override fun stopConvertService(context: Context) {
        ConvertUtils.stopConvertService(context)
    }

    override fun clearMuteCache(filePath: String) {
        MuteCacheManager.delete(filePath)
    }

    override fun getPlaybackActivityClass(): Class<*> {
        return PlaybackActivity::class.java
    }

    override fun startMuteDetectIfNecessary(fullPath: String?) {
        MuteDataDetectorWorker.startMuteDetectIfNecessary(fullPath)
    }

    override fun newPlaybackFragment(showLoading: Boolean): Fragment {
        return PlaybackContainerFragment().apply {
            arguments = bundleOf(PlaybackContainerFragment.ARG_KEY_SHOW_LOADING to showLoading)
        }
    }

    override fun newPlaybackEmptyFragment(): Fragment {
        return PlayBackEmptyFragment()
    }

    override fun onPermissionGranted(fragment: Fragment?) {
        (fragment as? PlaybackContainerFragment)?.onPermissionGranted()
    }

    override fun onSaveInstanceState(fragment: Fragment?, outState: Bundle) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onSaveInstanceState(outState)
    }

    override fun onRestoreInstanceState(fragment: Fragment?, saveState: Bundle) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onRestoreInstanceState(saveState)
    }

    override fun onNewIntent(fragment: Fragment?, intent: Intent?) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.onNewIntent(intent)
    }

    override fun setRequestCodeX(fragment: Fragment?, code: Int) {
        (fragment as? PlaybackContainerFragment)?.getIPictureMarkDelegate()?.setRequestCodeX(code)
    }

    override fun onPrivacyPolicySuccess(fragment: Fragment?, type: Int) {
        (fragment as? PlaybackContainerFragment)?.onPrivacyPolicySuccess(type)
    }

    override fun pausePlay(fragment: Fragment?, clearNotification: Boolean) {
        (fragment as? PlaybackContainerFragment)?.pausePlay(clearNotification)
    }

    override fun updateConvertConfig() {
        CloudConfigUtils.updateConvertConfig()
    }

    override fun isSupportWpsExport(): Boolean {
        return CloudConfigUtils.isSupportWpsExport
    }

    override fun readConvertContent(
        appContext: Context?,
        filename: String?,
        serverPlanCode: Int?
    ): java.util.ArrayList<*>? {
        kotlin.runCatching {
            return ConvertToUtils.readConvertContent(appContext, filename, serverPlanCode)
        }.onFailure {
            DebugUtil.e(PlaybackConvertViewModel.TAG, "readConvertContent error.$it", it)
        }
        return null
    }

    override fun readOShareConvertContent(filePath: String?, serverPlanCode: Int?): java.util.ArrayList<*>? {
        kotlin.runCatching {
            return ConvertToUtils.readOShareConvertContent(filePath, serverPlanCode)
        }.onFailure {
            DebugUtil.e(PlaybackConvertViewModel.TAG, "readConvertContent error.$it", it)
        }
        return null
    }

    override fun getSmartNameManager(): ISmartNameManager {
        return SmartNameManagerImpl()
    }

    override fun parseCallName(mContext: Context, path: String?, mediaId: Long, mimeType: String): String? {
        var name: String? = null
        when (mimeType) {
            RecordConstant.MIMETYPE_ACC,
            RecordConstant.MIMETYPE_ACC_ADTS -> name = path?.let { CallNameParseHelper.parseCallName(it) }
            RecordConstant.MIMETYPE_MP3 -> {
                // 通过mimeType判断是否为mp3，拦截 非mp3文件
                name = CallNameParseHelper.parseCallName(mContext, mediaId)
            }
        }
        return name
    }

    override fun releaseMp3File() {
        CallNameParseHelper.releaseMp3()
    }
 }