/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.os.Bundle
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.StatusBarUtil.setStatusBarTransparentAndBlackFont
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.exportconvert.summary.ShareWithSummaryFragment

class ShareWithTxtActivity : BaseActivity() {
    companion object {
        const val TAG = "ShareWithTxtActivityTAG"
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "ShareTxt"
        private const val SHOW_SUMMARY = 0
        const val SHARE_WITH_SUMMARY_FRAGMENT = "ShareWithSummaryFragment"
        const val SHARE_WITH_TXT_FRAGMENT = "ShareWithTxtFragment"
    }

    private var mViewModel: ShareWithTxtViewModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_share_with_txt)
        val mediaRecordId = intent.extras?.getLong("mediaRecordId")
        DebugUtil.e(TAG, "mediaRecordId >> $mediaRecordId")
        mViewModel = ViewModelProvider(this)[ShareWithTxtViewModel::class.java]
        initiateWindowInsets()
        attachFragments(savedInstanceState)
    }

    private fun initiateWindowInsets() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                    override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                        super.onApplyInsets(v, insets)
                        DebugUtil.i(TAG, "onApplyInsets")
                        TaskBarUtil.setNavigationColorOnSupportTaskBar(
                            navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                            activity = this@ShareWithTxtActivity,
                            defaultNoTaskBarColor = navigationBarColor()
                        )
                    }
                }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
        setStatusBarTransparentAndBlackFont(this, navigationBarColor())
    }

    override fun navigationBarColor(): Int {
        return R.color.share_txt_navigation_color
    }

    private fun attachFragments(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            // do not create Fragment again when activity recreated with configuration changed
            return
        }
        val shareType =  intent.extras?.getInt("shareType")
        DebugUtil.d(TAG, "shareType >> $shareType")
        val bundle = initBundle()
        val supportFragmentManager = supportFragmentManager
        if (shareType == SHOW_SUMMARY) {
            supportFragmentManager.beginTransaction()
                .add(
                    R.id.fragment_container_view,
                    ShareWithSummaryFragment::class.java,
                    bundle,
                    SHARE_WITH_SUMMARY_FRAGMENT
                )
                .setReorderingAllowed(true)
                .commit()
        } else {
            supportFragmentManager.beginTransaction()
                .add(
                    R.id.fragment_container_view,
                    ShareWithTxtFragment::class.java,
                    bundle,
                    SHARE_WITH_TXT_FRAGMENT
                )
                .setReorderingAllowed(true)
                .commit()
        }
    }


    private fun initBundle(): Bundle {
        var mediaRecordId: Long = 0
        var canShowSpeaker = false
        var isShowSpeaker = false
        var createTime: Long = -1
        var playFileName = ""
        var playFilePath = ""
        var shareType = 0
        var fileType = ""
        var summaryFilePath = ""

        intent?.extras?.let {
            mediaRecordId = it.getLong("mediaRecordId")
            canShowSpeaker = it.getBoolean("canShowSpeaker")
            isShowSpeaker = it.getBoolean("isShowSpeaker")
            createTime = it.getLong("createTime")
            playFileName = it.getString("playFileName", "")
            playFilePath = it.getString("playFilePath", "")
            shareType = it.getInt("shareType", 0)
            fileType = it.getString("fileType", "")
            summaryFilePath = it.getString("summaryFilePath", "")
        }
        val bundle = Bundle()
        bundle.putLong("mediaRecordId", mediaRecordId)
        bundle.putBoolean("canShowSpeaker", canShowSpeaker)
        bundle.putBoolean("isShowSpeaker", isShowSpeaker)
        bundle.putLong("createTime", createTime)
        bundle.putString("playFileName", playFileName)
        bundle.putString("playFilePath", playFilePath)
        bundle.putInt("shareType", shareType)
        bundle.putString("fileType", fileType)
        bundle.putString("summaryFilePath", summaryFilePath)
        return bundle
    }
}