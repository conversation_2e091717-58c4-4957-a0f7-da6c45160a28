/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackAnimatedCircleButtonTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/5 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class PlaybackAnimatedCircleButtonTest {
    var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_correct_when_init() {
        val context = context ?: return
        val view = PlaybackAnimatedCircleButton(context)
        Assert.assertNull(Whitebox.getInternalState(view, "mDrawablePlaySubWindow"))
    }

    @Test
    fun should_correct_when_switchPlayState() {
        val context = context ?: return
        val nameDrawable = "mDrawablePlaySubWindow"
        val view = PlaybackAnimatedCircleButton(context)
        view.switchPlayState()
        Assert.assertNull(Whitebox.getInternalState(view, nameDrawable))

        view.windowType = WindowType.LARGE
        view.switchPlayState()
        Assert.assertNotNull(Whitebox.getInternalState(view, nameDrawable))

        Whitebox.invokeMethod<Unit>(view, "onDetachedFromWindow")
        Assert.assertNull(Whitebox.getInternalState(view, nameDrawable))
    }

    @Test
    fun should_correct_when_switchPauseState() {
        val context = context ?: return
        val nameDrawable = "mDrawablePauseSubWindow"
        val view = PlaybackAnimatedCircleButton(context)
        view.switchPauseState()
        Assert.assertNull(Whitebox.getInternalState(view, nameDrawable))

        view.windowType = WindowType.LARGE
        view.switchPauseState()
        Assert.assertNotNull(Whitebox.getInternalState(view, nameDrawable))

        Whitebox.invokeMethod<Unit>(view, "onDetachedFromWindow")
        Assert.assertNull(Whitebox.getInternalState(view, nameDrawable))
    }
}