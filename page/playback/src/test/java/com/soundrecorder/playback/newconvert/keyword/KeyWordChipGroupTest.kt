/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.keyword

import android.app.Activity
import android.os.Build
import androidx.core.view.isVisible
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.button.COUILoadingButton
import com.coui.appcompat.chip.COUIChip
import com.google.android.material.chip.ChipGroup
import com.soundrecorder.playback.PlaybackActivity
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog


@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class, ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class KeyWordChipGroupTest {

    private var context: Activity? = null
    private var group: KeyWordChipGroup? = null

    @Before
    fun setUp() {
        context = Robolectric.buildActivity(PlaybackActivity::class.java).get()
        group = KeyWordChipGroup(context!!)
    }

    @After
    fun tearDown() {
        context = null
        group = null
    }

    private fun getChipGroup(group: KeyWordChipGroup): ChipGroup {
        val chipGroupField = Whitebox.getField(KeyWordChipGroup::class.java, "chipGroup")
        chipGroupField.isAccessible = true
        return chipGroupField.get(group) as ChipGroup
    }

    private fun getLoadingButton(group: KeyWordChipGroup): COUILoadingButton {
        val loadingButtonField = Whitebox.getField(KeyWordChipGroup::class.java, "loadingBtn")
        loadingButtonField.isAccessible = true
        return loadingButtonField.get(group) as COUILoadingButton
    }

    private fun createChipList(count: Int): List<String> {
        val list = mutableListOf<String>()
        for (i in 0 until count) {
            list.add("测试$i")
        }
        return list
    }

    @Test
    fun should_not_null_when_initView() {
        Whitebox.invokeMethod<Void>(group, "initView")
        val chipGroup = getChipGroup(group!!)

        Assert.assertNotNull(chipGroup)
    }

    /**
     * 测试有数据时显示chipGroup,无数据时隐藏
     */
    @Test
    fun should_show_chip_when_setKeyWords() {
        var list = createChipList(0)
        var state = KeyWordChipGroup.DEFAULT_STATE
        group?.let {
            it.setKeyWords(list, state)
            var chipGroup = getChipGroup(it)
            Assert.assertFalse(chipGroup.isVisible)

            list = createChipList(2)
            it.setKeyWords(list, state)
            Assert.assertTrue(chipGroup.isVisible)
        }
    }

    @Test
    fun should_notnull_when_setKeyWordChipClickListener() {
        group?.setKeyWordChipClickListener(null)

        val listenerField = Whitebox.getField(KeyWordChipGroup::class.java, "chipClickListener")
        listenerField.isAccessible = true
        var listener = listenerField.get(group)

        Assert.assertNull(listener)

        group?.setKeyWordChipClickListener(object : KeyWordChipClickListener {
            override fun extractKeyWord(): Boolean {
                return true
            }

            override fun onClickKeyWord(chip: COUIChip, keyWord: String) {
            }
        })

        listener = listenerField.get(group)
        Assert.assertNotNull(listener)
    }

    @Test
    fun should_show_chip_when_showKeyWords() {
        group?.let {
            var list = createChipList(0)
            Whitebox.invokeMethod<Void>(it, "showKeyWords", list)
            val chipGroup = getChipGroup(it)

            Assert.assertTrue(chipGroup.isVisible)
            Assert.assertEquals(0, chipGroup.childCount)

            list = createChipList(4)
            Whitebox.invokeMethod<Void>(it, "showKeyWords", list)
            Assert.assertTrue(chipGroup.isVisible)
            Assert.assertEquals(4, chipGroup.childCount)
        }
    }

    @Test
    fun should_show_loading_when_showExtractButton() {

        group?.let {
            var state = KeyWordChipGroup.DEFAULT_STATE
            Whitebox.invokeMethod<Void>(it, "showExtractButton", state)
            val loadingBtn = getLoadingButton(it)

            Assert.assertTrue(loadingBtn.isVisible)
            Assert.assertEquals(KeyWordChipGroup.DEFAULT_STATE, loadingBtn.buttonState)

            state = KeyWordChipGroup.LOADING_STATE
            Whitebox.invokeMethod<Void>(it, "showExtractButton", state)
            Assert.assertEquals(KeyWordChipGroup.LOADING_STATE, loadingBtn.buttonState)
        }
    }

    @Test
    fun test_createKeyWordChip() {
        group?.let {
            val chip = Whitebox.invokeMethod<COUIChip>(it, "createKeyWordChip")
            Assert.assertNotNull(chip)
        }
    }

    @Test
    fun should_when_showLoadingView() {
        group?.let {
            Whitebox.invokeMethod<Void>(it, "showLoadingView", true)
            val chipGroup = getChipGroup(it)
            val loadingBtn = getLoadingButton(it)

            Assert.assertFalse(chipGroup.isVisible)
            Assert.assertTrue(loadingBtn.isVisible)

            Whitebox.invokeMethod<Void>(it, "showLoadingView", false)

            Assert.assertTrue(chipGroup.isVisible)
            Assert.assertFalse(loadingBtn.isVisible)
        }
    }

    @Test
    fun should_loadingBtn_not_null_when_ensureLoadingButton() {
        group?.let {
            Whitebox.invokeMethod<Void>(it, "ensureLoadingButton")
            val loadingBtn = getLoadingButton(it)

            Assert.assertNotNull(loadingBtn)
        }
    }

    @Test
    fun test_showLoadingState() {
        group?.let {
            Whitebox.invokeMethod<Void>(it, "ensureLoadingButton")
            val loadingBtn = getLoadingButton(it)

            var state = KeyWordChipGroup.DEFAULT_STATE
            Whitebox.invokeMethod<Void>(it, "showLoadingState", state)
            Assert.assertEquals(KeyWordChipGroup.DEFAULT_STATE, loadingBtn.buttonState)

            state = KeyWordChipGroup.LOADING_STATE
            Whitebox.invokeMethod<Void>(it, "showLoadingState", state)
            Assert.assertEquals(KeyWordChipGroup.LOADING_STATE, loadingBtn.buttonState)
        }
    }

    @Test
    fun should_return_int_when_getRealHeight() {
        var list = createChipList(0)
        val state = KeyWordChipGroup.DEFAULT_STATE
        group?.let {
            it.setKeyWords(list, state)
            var height = it.getRealHeight() //测试显示提取按钮
            Assert.assertTrue(height > 0)

            list = createChipList(8)
            it.setKeyWords(list, state)
            height = it.getRealHeight() // 测试显示关键词列表，单测时获取高度为0

            Assert.assertTrue(height == 0)
        }
    }
}