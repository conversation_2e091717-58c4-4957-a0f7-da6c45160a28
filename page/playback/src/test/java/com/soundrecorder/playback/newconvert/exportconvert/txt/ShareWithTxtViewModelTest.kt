/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.os.Build
import android.os.Bundle
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.InjectMocks
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config


@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class ShareWithTxtViewModelTest {
    private val mActivityController: ActivityController<ShareWithTxtActivity> =
        Robolectric.buildActivity(ShareWithTxtActivity::class.java)
    private var mActivity: ShareWithTxtActivity? = null

    @InjectMocks
    private val saveTxtToLocalCallback: SaveToLocalCallback = object : SaveToLocalCallback {
        override fun onShowSaveFileWaitingDialog() {
            println("onShowSaveFileWaitingDialog")
        }

        override fun onGetFileName(fileName: String, fileAbsPath: String) {
            println("onGetFileName")
        }

        override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
            println("onSaveSuccess")
        }

        override fun onSaveFailed(message: String) {
            println("onSaveFailed")
        }
    }

    @InjectMocks
    private val shareTxtCallback = object : IShareListener {

        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            println("onShowWaitingDialog...")
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            println("onSaveSuccess")
            println("分享成功，type >> $type")
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            println("分享失败，message >> $message")
        }
    }

    @Before
    fun setUp() {
    }

    @Test
    fun setRepositoryTest() {
        val mock = Mockito.mock(ShareWithTxtViewModel::class.java)
        mock.setRepository(null)
        mock.setRepository(ShareWithTxtRepository())
    }

    @Test
    fun initArgFromBundleTest() {
        var bundle: Bundle? = null
        mActivity = mActivityController.create().get()
        mActivity?.application?.let {
            val mock = ShareWithTxtViewModel(it)
            mock.initArgFromBundle(bundle)
            Assert.assertEquals(null, Whitebox.getInternalState(mock, "mOriginalFileName"))

            bundle = Bundle()
            mock.initArgFromBundle(bundle)
            Assert.assertEquals(null, Whitebox.getInternalState(mock, "mOriginalFileName"))

            Whitebox.setInternalState(mock, "mOriginalFileName", "")
            mock.initArgFromBundle(bundle)
            Assert.assertEquals("", Whitebox.getInternalState(mock, "mOriginalFileName"))

            bundle?.putLong("mediaRecordId", 12024L)
            bundle?.putLong("createTime", System.currentTimeMillis())
            bundle?.putBoolean("canShowSpeaker", false)
            bundle?.putBoolean("isShowSpeaker", false)
            bundle?.putString("playFileName", "playFileName.mp3")
            Whitebox.setInternalState(mock, "mOriginalFileName", "")
            mock.initArgFromBundle(bundle)
            Assert.assertNotNull(bundle)
            Assert.assertEquals("playFileName", Whitebox.getInternalState(mock, "mOriginalFileName"))

            bundle = Mockito.mock(Bundle::class.java)
            Whitebox.setInternalState(mock, "mOriginalFileName", "")
            Mockito.`when`(bundle?.getLong("mediaRecordId")).thenThrow(NullPointerException())
            mock.initArgFromBundle(bundle)
            Assert.assertEquals("", Whitebox.getInternalState(mock, "mOriginalFileName"))
        }
    }

    @Test
    fun saveResultChoiceTest() {
        mActivity = mActivityController.create().get()
        mActivity?.application?.let {
            val mock = ShareWithTxtViewModel(it)
            Assert.assertEquals(true, mock.getLastShowLine())
            Assert.assertEquals(true, mock.getLastShowDate())
            Assert.assertEquals(true, mock.getLastShowSpeaker())

            mock.saveResultChoice()
            Assert.assertEquals(true, mock.getLastShowLine())
            Assert.assertEquals(true, mock.getLastShowDate())
            Assert.assertEquals(true, mock.getLastShowSpeaker())

            mock.mShowLineLiveData.value = false
            mock.mShowDateLiveData.value = false
            mock.mShowSpeakerLiveData.value = false
            mock.saveResultChoice()
            Assert.assertEquals(false, mock.getLastShowLine())
            Assert.assertEquals(false, mock.getLastShowDate())
            Assert.assertEquals(false, mock.getLastShowSpeaker())
        }
    }

    @Test
    @Ignore
    fun restoreChoiceTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(true, viewModel?.mShowLineLiveData?.value)
        Assert.assertEquals(true, viewModel?.mShowDateLiveData?.value)
        Assert.assertEquals(true, viewModel?.mShowSpeakerLiveData?.value)
        Assert.assertEquals(true, viewModel?.getLastShowLine())
        Assert.assertEquals(true, viewModel?.getLastShowDate())
        Assert.assertEquals(true, viewModel?.getLastShowSpeaker())

        Whitebox.setInternalState(viewModel, "mLastShowLine", false)
        Whitebox.setInternalState(viewModel, "mLastShowDate", false)
        Whitebox.setInternalState(viewModel, "mLastShowSpeaker", false)
        viewModel?.restoreChoice()
        Assert.assertEquals(false, viewModel?.mShowLineLiveData?.value)
        Assert.assertEquals(false, viewModel?.mShowDateLiveData?.value)
        Assert.assertEquals(false, viewModel?.mShowSpeakerLiveData?.value)
    }

    @Test
    fun getTxtFileContentTest() {
        val lineSeparator = System.lineSeparator()
        mActivity = mActivityController.create().get()
        mActivity?.application?.let {
            val viewModel = initViewModelData(ShareWithTxtViewModel(it))

            var expectedString = "title$lineSeparator" +
                    lineSeparator +
                    "date$lineSeparator" +
                    "subject$lineSeparator" +
                    "roles$lineSeparator" +
                    lineSeparator +
                    "roleName1  00:00$lineSeparator" +
                    "textContent$lineSeparator$lineSeparator"

            var method = PowerMockito.method(ShareWithTxtViewModel::class.java, "getTxtFileContent")
            Assert.assertEquals(expectedString, method.invoke(viewModel))


            viewModel.mShowLineLiveData.value = false
            expectedString = "title$lineSeparator" +
                    lineSeparator +
                    "date$lineSeparator" +
                    "subject$lineSeparator" +
                    "${lineSeparator}textContent"
            method = PowerMockito.method(ShareWithTxtViewModel::class.java, "getTxtFileContent")
            Assert.assertEquals(expectedString, method.invoke(viewModel))

            viewModel.mShowLineLiveData.value = true
            //只显示时间和分段
            viewModel.mShowSpeakerLiveData.value = false
            expectedString = "title$lineSeparator" +
                    lineSeparator +
                    "date$lineSeparator" +
                    "subject$lineSeparator" +
                    lineSeparator +
                    "00:00$lineSeparator" +
                    "textContent$lineSeparator$lineSeparator"
            method = PowerMockito.method(ShareWithTxtViewModel::class.java, "getTxtFileContent")
            Assert.assertEquals(expectedString, method.invoke(viewModel))

            //只显示讲话人和分段
            viewModel.mShowDateLiveData.value = false
            viewModel.mShowSpeakerLiveData.value = true
            expectedString = "title$lineSeparator" +
                    lineSeparator +
                    "date$lineSeparator" +
                    "subject$lineSeparator" +
                    "roles$lineSeparator" +
                    lineSeparator +
                    "roleName1$lineSeparator" +
                    "textContent$lineSeparator$lineSeparator"
            method = PowerMockito.method(ShareWithTxtViewModel::class.java, "getTxtFileContent")
            Assert.assertEquals(expectedString, method.invoke(viewModel))

            viewModel.mShowDateLiveData.value = true
            viewModel.mItemsListLiveData.value = mutableListOf()
            expectedString = "title$lineSeparator" +
                    lineSeparator +
                    "date$lineSeparator" +
                    "subject$lineSeparator" +
                    "roles$lineSeparator" +
                    lineSeparator
            method = PowerMockito.method(ShareWithTxtViewModel::class.java, "getTxtFileContent")
            Assert.assertEquals(expectedString, method.invoke(viewModel))
        }
    }

    @Test
    fun getLastShowLineTest() {
        val mock = getViewModel()
        Assert.assertEquals(true, mock?.getLastShowLine())
        Whitebox.setInternalState(mock, "mLastShowLine", false)
        Assert.assertEquals(false, mock?.getLastShowLine())
    }

    @Test
    fun getLastShowDateTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(true, viewModel?.getLastShowDate())
        Whitebox.setInternalState(viewModel, "mLastShowDate", false)
        Assert.assertEquals(false, viewModel?.getLastShowDate())
    }

    @Test
    fun getLastShowSpeakerTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(true, viewModel?.getLastShowSpeaker())
        Whitebox.setInternalState(viewModel, "mLastShowSpeaker", false)
        Assert.assertEquals(false, viewModel?.getLastShowSpeaker())
    }

    @Test
    fun getCanShowSpeakerRoleTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(false, viewModel?.getCanShowSpeakerRole())
        viewModel?.setCanShowSpeakerRole(true)
        Assert.assertEquals(true, viewModel?.getCanShowSpeakerRole())
    }

    @Test
    fun setCanShowSpeakerRoleTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(false, viewModel?.getCanShowSpeakerRole())
        viewModel?.setCanShowSpeakerRole(true)
        Assert.assertEquals(true, viewModel?.getCanShowSpeakerRole())
    }

    @Test
    fun setMediaRecorderIdTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(-1L, Whitebox.getInternalState(viewModel, "mMediaRecordId"))
        viewModel?.setMediaRecorderId(1000L)
        Assert.assertEquals(1000L, Whitebox.getInternalState(viewModel, "mMediaRecordId"))
    }

    @Test
    fun setCreateTimeTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(-1L, Whitebox.getInternalState(viewModel, "mCreateTime"))
        viewModel?.setCreateTime(1000L)
        Assert.assertEquals(1000L, Whitebox.getInternalState(viewModel, "mCreateTime"))
    }

    @Test
    fun setOnSaveTxtToLocalResultCallbackTest() {
        val viewModel = getViewModel()
        Assert.assertNull(Whitebox.getInternalState(viewModel, "saveTxtToLocalCallback"))
        viewModel?.setOnSaveTxtToLocalResultCallback(saveTxtToLocalCallback)
        Assert.assertNotNull(Whitebox.getInternalState(viewModel, "saveTxtToLocalCallback"))
    }

    @Test
    fun setOnShareTxtCallbackResultCallbackTest() {
        val viewModel = getViewModel()
        Assert.assertNull(Whitebox.getInternalState(viewModel, "shareTxtCallback"))
        viewModel?.setOnShareTxtCallbackResultCallback(shareTxtCallback)
        Assert.assertNotNull(Whitebox.getInternalState(viewModel, "shareTxtCallback"))
    }

    @Test
    fun getConvertFileSizeTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(0L, viewModel?.getConvertFileSize())
        Whitebox.setInternalState(viewModel, "mConvertFileSize", 1000L)
        Assert.assertEquals(1000L, viewModel?.getConvertFileSize())
    }

    @Test
    fun getNeedRestoreDialogTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(false, viewModel?.getNeedRestoreDialog())
        viewModel?.setNeedRestoreDialog(true)
        Assert.assertEquals(true, viewModel?.getNeedRestoreDialog())
    }

    @Test
    fun setNeedRestoreDialogTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(false, viewModel?.getNeedRestoreDialog())
        viewModel?.setNeedRestoreDialog(true)
        Assert.assertEquals(true, viewModel?.getNeedRestoreDialog())
    }

    @Test
    fun setAlreadyShowSnackBarTest() {
        val viewModel = getViewModel()
        Assert.assertEquals(false, viewModel?.getNeedShowSnackBar())

        Whitebox.setInternalState(viewModel, "mNeedShowSnackBar", true)
        Assert.assertEquals(true, viewModel?.getNeedShowSnackBar())

        viewModel?.setAlreadyShowSnackBar()
        Assert.assertEquals(false, viewModel?.getNeedShowSnackBar())
    }

    @Test
    fun getSaveCallBackFileNameTest() {
        val viewModel = getViewModel()
        Assert.assertEquals("", viewModel?.getSaveCallBackFileName())
        Whitebox.setInternalState(viewModel, "mSaveCallBackFileName", "fileName")
        Assert.assertEquals("fileName", viewModel?.getSaveCallBackFileName())
    }

    private fun getViewModel(): ShareWithTxtViewModel? {
        var viewModel: ShareWithTxtViewModel? = null
        mActivity = mActivityController.create().get()
        mActivity?.application?.let {
            viewModel = ShareWithTxtViewModel(it)
        }
        return viewModel
    }

    private fun initViewModelData(viewModel: ShareWithTxtViewModel): ShareWithTxtViewModel {
        viewModel.mTitleLiveData.value = "title"
        viewModel.mDateLiveData.value = "date"
        viewModel.mSubjectLiveData.value = "subject"
        viewModel.mRolesStringLiveData.value = "roles"

        viewModel.mShowLineLiveData.value = true
        viewModel.mShowDateLiveData.value = true
        viewModel.mShowSpeakerLiveData.value = true
        viewModel.mContentStringLiveData.value = "textContent"

        viewModel.setCanShowSpeakerRole(true)

        val mutableListOf = mutableListOf<ConvertContentItem>()
        val convertContentItem = ConvertContentItem()
        convertContentItem.roleName = "roleName1"
        convertContentItem.textContent = "textContent"
        mutableListOf.add(convertContentItem)
        viewModel.mItemsListLiveData.value = mutableListOf
        return viewModel
    }
}