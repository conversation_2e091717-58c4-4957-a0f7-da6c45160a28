/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemViewTest
 * Description:
 * Version: 1.0
 * Date: 2024/5/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/5/20 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class PlayWaveItemViewTest {
    var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_correct_when_drawDottedLineWhenNoData() {
        val realContext = context ?: return
        val view = PlayWaveItemView(realContext)
        Assert.assertTrue(Whitebox.invokeMethod(view, "drawDottedLineWhenNoData"))
    }
}