/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditWaveRecyclerViewTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.wave

import android.content.Context
import android.os.Build
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.editrecord.EditRecordActivity
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption
import com.soundrecorder.editrecord.views.wave.EditWaveItemView
import com.soundrecorder.editrecord.views.wave.EditWaveItemView.Companion.NONE
import com.soundrecorder.editrecord.views.wave.EditWaveRecyclerView
import com.soundrecorder.editrecord.views.wave.HandlerMoveListener
import com.soundrecorder.editrecord.views.wave.interfaze.ISubscriber
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLooper

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class EditWaveRecyclerViewTest {

    private var mContext: Context? = null
    private var mController: ActivityController<EditRecordActivity>? = null

    @Before
    fun setUp() {
        mContext = BaseApplication.getAppContext()
        mController = Robolectric.buildActivity(EditRecordActivity::class.java)
    }

    @After
    fun tearDown() {
        mController = null
        mContext = null
    }

    @Test
    fun should_return_true_when_onInterceptTouchEvent() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            recyclerView.setIsCanScrollTimeRuler(false)
            val event =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            val result = recyclerView.onInterceptTouchEvent(event)
            Assert.assertTrue(result)

            recyclerView.setIsCanScrollTimeRuler(true)
            recyclerView.onInterceptTouchEvent(event)
            Assert.assertEquals(200, Whitebox.getInternalState(recyclerView, "mTouchDownX"))

            recyclerView.register(object : ISubscriber {
                override fun notifyStartTime() {
                }

                override fun notifyEndTime() {
                }

                override fun notifyTimeChangeByX(x: Float) {
                }

                override fun isTouchHandler(x: Float): Boolean {
                    return true
                }

                override fun notifyUp() {
                }
            })
            recyclerView.onInterceptTouchEvent(event)
            Assert.assertFalse(Whitebox.getInternalState(recyclerView, "mCanScrollHorizontally"))
        }
    }

    @Test
    fun should_returnFalse_when_onTouchEvent() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            var motionEvent =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_DOWN,
                    200f,
                    200f,
                    0
                )
            Whitebox.setInternalState(recyclerView, "mMarkViewHeight", 500)
            Whitebox.setInternalState(recyclerView, "mParent", LinearLayout(it))

            //模拟mCanScrollHorizontally = true, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(true)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertEquals(200, Whitebox.getInternalState(recyclerView, "mTouchDownX"))

            //mCanScrollHorizontally = false, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(false)
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertTrue(recyclerView.onTouchEvent(motionEvent))

            motionEvent =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_MOVE,
                    200f,
                    200f,
                    0
                )
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertTrue(recyclerView.onTouchEvent(motionEvent))

            //mCanScrollHorizontally = false, ACTION_DOWN
            recyclerView.setIsCanScrollTimeRuler(false)
            motionEvent =
                MotionEvent.obtain(
                    System.currentTimeMillis(),
                    200,
                    MotionEvent.ACTION_UP,
                    200f,
                    200f,
                    0
                )
            recyclerView.onTouchEvent(motionEvent)
            Assert.assertTrue(
                Whitebox.getInternalState<Boolean>(
                    recyclerView,
                    "mCanScrollHorizontally"
                )
            )
        }
    }

    @Test
    fun should_notNull_when_register() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            Assert.assertNull(Whitebox.getInternalState(recyclerView, "mSubscribers"))
            recyclerView.register(object :
                ISubscriber {
                override fun notifyStartTime() {
                    //do nothing
                }

                override fun notifyEndTime() {
                    //do nothing
                }

                override fun notifyTimeChangeByX(x: Float) {
                    //do nothing
                }

                override fun isTouchHandler(x: Float): Boolean {
                    return false
                }

                override fun notifyUp() {
                    //do nothing
                }
            })

            Assert.assertNotNull(Whitebox.getInternalState(recyclerView, "mSubscribers"))
        }
    }

    @Test
    fun should_equals_when_notifySubscribersUp() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val subscribers = mutableListOf<ISubscriber>()
            Whitebox.setInternalState(recyclerView, "mSubscribers", subscribers)
            Whitebox.invokeMethod<Any>(recyclerView, "notifySubscribersUp")
            Assert.assertEquals(NONE, recyclerView.hitStatus)
        }
    }

    @Test
    fun should_false_when_isTouchHandler() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val result = Whitebox.invokeMethod<Boolean>(recyclerView, "isTouchHandler", 100f)
            Assert.assertFalse(result)
        }
    }

    @Test
    fun should_equals_when_createNewItemView() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val view = recyclerView.createNewItemView(it, LinearLayout(it))
            Assert.assertTrue(view is EditWaveItemView)
        }
    }

    @Test
    fun should_equals_when_fixItemCount() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val totalCount = 2
            val fixItemCount = recyclerView.fixItemCount(totalCount)
            Assert.assertEquals(totalCount, fixItemCount)
        }
    }

    @Test
    fun should_equals_when_onBindItemView() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val amplitude = mutableListOf(1, 2, 3, 4)
            Whitebox.setInternalState(recyclerView, "mAmplitudeValue", amplitude)
            val rulerView = EditWaveItemView(it)
            recyclerView.onBindItemView(rulerView, 1)
            Assert.assertEquals(amplitude, rulerView.amplitudes)
        }
    }

    @Test
    fun should_equals_when_setCutStartTime() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val time = 1000L
            recyclerView.setCutStartTime(time)
            Assert.assertEquals(time, recyclerView.startRecord)
        }
    }

    @Test
    fun should_equals_when_setCutEndTime() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val time = 2000L
            recyclerView.setCutEndTime(time)
            Assert.assertEquals(time, recyclerView.endRecord)
        }
    }

    @Test
    fun should_equals_when_removeAllPointTime() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            recyclerView.removeAllPointTime()
            Assert.assertNull(recyclerView.endRecord)
        }
    }

    @Test
    fun should_equals_when_resetStatus() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            recyclerView.resetStatus()
            Assert.assertNull(recyclerView.endRecord)
        }
    }

    @Test
    fun should_equals_when_setLastPlayTime() {
        mController?.get()?.let {
            val recyclerView = EditWaveRecyclerView(it, null)
            val time = 3000L
            recyclerView.setLastPlayTime(time)
            Assert.assertEquals(time, recyclerView.getLastTime())
        }
    }

    @Test
    fun should_null_when_onDetachedFromWindow() {
        val recyclerView = EditWaveRecyclerView(mContext, null)
        recyclerView.onTouchDownOnWaveBar = {}
        recyclerView.onTouchUpOnWaveBar = {}
        recyclerView.handlerMoveListener = object : HandlerMoveListener {
            override fun onStartMove(time: Long) {
            }

            override fun onEndMove(time: Long) {
            }
        }

        Whitebox.invokeMethod<Unit>(recyclerView, "onDetachedFromWindow")
        Assert.assertNull(recyclerView.onTouchDownOnWaveBar)
        Assert.assertNull(recyclerView.onTouchUpOnWaveBar)
        Assert.assertNull(recyclerView.handlerMoveListener)
    }

    @Test
    fun should_when_notifySubscribers() {
        val recyclerView = EditWaveRecyclerView(mContext, null).apply {
            setCutStartTime(1000)
            setCutEndTime(3000)
            totalTime = 5000
            register(object : ISubscriber {
                override fun notifyStartTime() {
                }

                override fun notifyEndTime() {
                }

                override fun notifyTimeChangeByX(x: Float) {
                }

                override fun isTouchHandler(x: Float): Boolean {
                    return false
                }

                override fun notifyUp() {
                }
            })
        }

        // left line run left
        recyclerView.hitStatus = 2
        Whitebox.invokeMethod<Unit>(recyclerView, "notifySubscribers", 0F)
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchLeft"))
        ShadowLooper.runUiThreadTasksIncludingDelayedTasks()

        // left line run right
        Whitebox.invokeMethod<Unit>(recyclerView, "notifySubscribers", 500F)
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchRight"))

        // right line run left
        Whitebox.setInternalState(recyclerView, "isRunningOnTouchLeft", false)
        recyclerView.hitStatus = 1
        Whitebox.invokeMethod<Unit>(recyclerView, "notifySubscribers", 0F)
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchLeft"))

        // right line run right
        Whitebox.setInternalState(recyclerView, "isRunningOnTouchRight", false)
        Whitebox.invokeMethod<Unit>(recyclerView, "notifySubscribers", 500F)
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchRight"))
    }

    @Test
    fun should_when_removeRunLeftOnClipBar() {
        val recyclerView = EditWaveRecyclerView(mContext, null)
        Whitebox.invokeMethod<Unit>(recyclerView, "runLeftOnClipBar")
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchLeft"))

        Whitebox.invokeMethod<Unit>(recyclerView, "removeRunLeftOnClipBar")
        Assert.assertFalse(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchLeft"))
    }

    @Test
    fun should_when_removeRunRightOnClipBar() {
        val recyclerView = EditWaveRecyclerView(mContext, null)
        Whitebox.invokeMethod<Unit>(recyclerView, "runRightClipBar")
        Assert.assertTrue(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchRight"))

        Whitebox.invokeMethod<Unit>(recyclerView, "removeRunRightOnClipBar")
        Assert.assertFalse(Whitebox.getInternalState<Boolean>(recyclerView, "isRunningOnTouchRight"))
    }
}