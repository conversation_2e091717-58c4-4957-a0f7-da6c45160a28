/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditPlayerControllerTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui;

import android.net.Uri;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.player.status.PlayStatus;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;


@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class EditPlayerControllerTest {

    @Test
    public void should_returnNotNull_when_setLastPlayTime() {
        EditPlayerController playerController = new EditPlayerController(null);
        playerController.setLastPlayTime(2000);
        Assert.assertEquals(2000, playerController.getLastPlayProgressTime());
        playerController.setLastPlayTime(0);
        Assert.assertFalse(Whitebox.getInternalState(playerController, "mIsFirstSet"));
    }

    @Test
    public void should_returnNotNull_when_prepareToPlay() {
        EditPlayerController playerController = new EditPlayerController(null);
        playerController.doInit();
        Uri playUri = Uri.parse("abcd");
        playerController.prepareToPlay(playUri);
        Assert.assertEquals(playUri, playerController.getPlayUri());
    }

    @Test
    public void should_equals_when_onPlayError() {
        EditPlayerController playerController = new EditPlayerController(null);
        playerController.onPlayError(-1);
        Assert.assertEquals(0L, (long) playerController.getCurrentTimeMillis().getValue());
    }

    @Test
    public void should_equals_when_onActionComplete() {
        EditPlayerController playerController = new EditPlayerController(null);
        playerController.getCurrentTimeMillis().setValue(1000L);
        playerController.onActionComplete(PlayStatus.ACTION_PREPARE);
        Assert.assertEquals(1000L, (long) playerController.getLastPlayStartTime().getValue());

        playerController.onActionComplete(PlayStatus.ACTION_CONTINUE);
        Assert.assertEquals(1000L, (long) playerController.getLastPlayStartTime().getValue());
    }

    @Test
    public void should_equals_when_seekTime() {
        EditPlayerController playController = new EditPlayerController(null);
        playController.doSeekTime(1000, true);
        Assert.assertEquals(1000L, (long) playController.getLastPlayStartTime().getValue());
    }

    @Test
    public void should_equals_when_seekRelease() {
        EditPlayerController playController = new EditPlayerController(null);
        playController.seekRelease();
        Assert.assertEquals(PlayStatus.PLAYER_STATE_HALTON, (int) playController.getPlayerState().getValue());
    }
}
