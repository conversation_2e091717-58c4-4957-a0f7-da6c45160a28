/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ZoomWindowChangeObserver
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:IllegalCatch
package com.soundrecorder.editrecord.views.zoomwindow;

import com.oplus.zoomwindow.IOplusZoomWindowObserver;
import com.oplus.zoomwindow.OplusZoomWindowInfo;
import com.oplus.zoomwindow.OplusZoomWindowManager;
import com.soundrecorder.base.utils.DebugUtil;

public class ZoomWindowChangeObserver extends IOplusZoomWindowObserver.Stub {
    private static final String TAG = "ZoomWindowChangeObserver";
    private ZoomWindowChange mZoomWindowChange = null;

    @Override
    public void onZoomWindowShow(OplusZoomWindowInfo oplusZoomWindowInfo) {
        DebugUtil.d(TAG, "onZoomWindowShow");
        if (mZoomWindowChange != null) {
            mZoomWindowChange.onZoomWindowShow();
        }
    }

    @Override
    public void onZoomWindowHide(OplusZoomWindowInfo oplusZoomWindowInfo) {
        DebugUtil.d(TAG, "onZoomWindowHide");
        if (mZoomWindowChange != null) {
            mZoomWindowChange.onZoomWindowHide();
        }
    }

    @Override
    public void onZoomWindowDied(String s) {
        DebugUtil.d(TAG, "onZoomWindowDied");
    }

    @Override
    public void onInputMethodChanged(boolean b) {
        DebugUtil.d(TAG, "onInputMethodChanged");
    }

    public void setZoomWindowChange(ZoomWindowChange windowChange) {
        mZoomWindowChange = windowChange;
    }

    public void registerZoomWindowObserver() {
        try {
            OplusZoomWindowManager manager = OplusZoomWindowManager.getInstance();
            manager.registerZoomWindowObserver(this);
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "registerZoomWindowObserver with an exception", ignored);
        }
    }

    public void unregisterZoomWindowObserver() {
        try {
            OplusZoomWindowManager manager = OplusZoomWindowManager.getInstance();
            manager.unregisterZoomWindowObserver(this);
        } catch (Throwable ignored) {
            DebugUtil.e(TAG, "unregisterZoomWindowObserver with an exception", ignored);
        }
    }

    public interface ZoomWindowChange {
        void onZoomWindowShow();

        void onZoomWindowHide();
    }
}
