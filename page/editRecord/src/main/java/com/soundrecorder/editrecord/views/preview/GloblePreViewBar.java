/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: GloblePreViewBar
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.preview;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.Build;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.DensityUtil;
import com.soundrecorder.editrecord.R;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class GloblePreViewBar extends FrameLayout {

    public static final int NATURE_UNIT_TIME = 70;
    /**
     * 波形刻度数量
     */
    public static final float NATURE_COUNT = 188f;
    /**
     * 波形间隔数量
     */
    public static final float NATURE_SPACE_COUNT = NATURE_COUNT - 1;
    /**
     * 188 +187 = 375
     */
    public static final float TOTAL_COUNT = NATURE_COUNT + NATURE_SPACE_COUNT;
    /**
     * 188 * 70 = 13160
     * the view width could signify time,when mScaleValue equal 1
     */
    public static final long NATURE_TIME = (long) (NATURE_COUNT * NATURE_UNIT_TIME);

    protected static final int GAP_LINE_PARAMS = 4;
    protected static final float CUT_LINE_WIDTH = 1.5f;
    private static final float HALF_VALUE = 0.5f;
    private static final String TAG = "GloblePreViewBar";
    private static final int DRAW_POINT_FRAME_TIME = 18;
    private static final int DRAW_POINT_FRAME_THRESHOLD = 300;
    private static final int PRECISION = 9;
    private static final int CURRENT_LINE_PADDING = 10;
    private static final int POINT_MARGIN = 2;
    private static final int AMPLITUDE_HEIGHT = 40;
    private static final int DRAG_BAR_HEIGHT = 44;

    private final AmplitudeView mAmplitudeView;

    protected List<Integer> mAmplitudes;
    protected Paint mGapLinePaint;

    private Paint mBgPaint;
    private Paint mPointPaint;
    private Paint mAmplitudePaint;

    private long mPointTime;

    private List<PointMoveListener> mListeners = new ArrayList<>();
    private DragBar mDragBar;
    private boolean mIsTouch;
    private float mWavePadding;
    private long mTotalTime;
    private long mPreTime = -1;
    private boolean mReverseLayout = false;
    private float mPointRealX = 0;
    /*中线是否跟随把手拖动*/
    private boolean mMiddleLineFollowDragBar;

    public GloblePreViewBar(Context context) {
        this(context, null);
    }

    public GloblePreViewBar(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, 0);
    }

    public GloblePreViewBar(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                boolean isForceDarkAllowed = isForceDarkAllowed();
                DebugUtil.i(TAG, "isForceDarkAllowed: " + isForceDarkAllowed);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "isForceDarkAllowed()", e);
        }
        initParams(context, attributeSet);
        mAmplitudeView = new AmplitudeView(context);
        addAmplitudeView();
        initDragBar(context);
        setWillNotDraw(false);
    }

    private void initDragBar(Context context) {
        mDragBar = new DragBar(context);
        mDragBar.setCutTimeListener(new DragBar.CutTimeListener() {

            @Override
            public void onTouchDownOnBar(int direction, long time) {
                for (PointMoveListener listener : mListeners) {
                    listener.onTouchDownOnBar(direction, time);
                }
            }

            @Override
            public void onMoveOnDragBar(int direction, long time) {
                for (PointMoveListener listener : mListeners) {
                    listener.onMoveOnDragBar(direction, time);
                }
                if (mMiddleLineFollowDragBar) {
                    correctPointTime(time);
                }
            }

            @Override
            public void onTouchUpOnBar(int direction, long time) {
                for (PointMoveListener listener : mListeners) {
                    listener.onTouchUpOnBar(direction, time);
                }
            }
        });
    }

    private void onMoveOnRedBar() {
        for (PointMoveListener listener : mListeners) {
            listener.onMoveOnMiddleBar(mPointTime);
        }
    }

    private void addAmplitudeView() {
        mAmplitudeView.setPaint(mAmplitudePaint);
        mAmplitudeView.setmWavePadding(mWavePadding);
        int height = (int) DensityUtil.dp2px(getContext(), AMPLITUDE_HEIGHT);
        LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, height);
        layoutParams.leftMargin = getAmpMargin();
        layoutParams.rightMargin = getAmpMargin();
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mAmplitudeView, layoutParams);
        mAmplitudeView.setListener();
    }

    private int getAmpMargin() {
        return (int) (Math.ceil(getContext().getResources().getDimension(R.dimen.edit_left_drag_bar_width)));
    }

    private float getRoundRadius() {
        return getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp9);
    }

    public void setReverseLayout(boolean reverseLayout) {
        if (reverseLayout == mReverseLayout) {
            return;
        }
        mReverseLayout = reverseLayout;
        requestLayout();
    }

    public boolean getReverseLayout() {
        return mReverseLayout;
    }


    public void setListenter(PointMoveListener listener) {
        mListeners.add(listener);
    }

    public void setMiddleLineFollowDragBar(boolean middleLineFollowDragBar) {
        this.mMiddleLineFollowDragBar = middleLineFollowDragBar;
    }

    private void initParams(Context context, AttributeSet attr) {
        TypedArray array = context.obtainStyledAttributes(attr, R.styleable.GloblePreViewBar);
        int pointColor = array.getColor(R.styleable.GloblePreViewBar_point_color, Color.BLACK);
        int amplitudeColor = array.getColor(R.styleable.GloblePreViewBar_amplitude_color, context.getColor(R.color.edit_wave_normal_color));
        int gapLineColor = array.getColor(R.styleable.GloblePreViewBar_gap_line_color, context.getColor(R.color.edit_wave_normal_color));
        array.recycle();

        mPointPaint = new Paint();
        mPointPaint.setColor(pointColor);
        mPointPaint.setAntiAlias(true);
        mPointPaint.setDither(true);

        mAmplitudePaint = new Paint();
        mAmplitudePaint.setColor(amplitudeColor);
        mAmplitudePaint.setAntiAlias(true);
        mAmplitudePaint.setDither(true);

        mGapLinePaint = new Paint();
        mGapLinePaint.setColor(gapLineColor);
        mGapLinePaint.setAntiAlias(true);
        mGapLinePaint.setDither(true);

        mBgPaint = new Paint();
        mBgPaint.setColor(getResources().getColor(R.color.edit_bg_color, context.getTheme()));
        mBgPaint.setAntiAlias(true);
        mBgPaint.setDither(true);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawBackground(canvas);
        if (mAmplitudes != null && mAmplitudes.size() > 0) {
            drawPoint(canvas);//ok
        }
    }

    private void drawBackground(Canvas canvas) {
        float roundRadius = getRoundRadius();
        canvas.drawRoundRect(getPaddingStart(), getPaddingTop(),
                getWidth() - getPaddingEnd(),
                getHeight() - getPaddingBottom(),
                roundRadius, roundRadius, mBgPaint);
        canvas.save();
    }

    /**
     * 绘制当前进度条
     */
    private void drawPoint(Canvas canvas) {
        if ((mAmplitudes == null) || mAmplitudes.isEmpty()) {
            mPointTime = 0;
        }
        float pointX = 0;
        if (isLessNature() && (mTotalTime != 0)) {
            pointX = addFixWidth()
                    + mPointTime / (float) mTotalTime * (getAvailableWidth() - 2 * addFixWidth()) + getPaddingStart() + getAmpMargin();
        } else {
            if (mTotalTime != 0) {
                pointX = mPointTime / (float) mTotalTime * getAvailableWidth() + getPaddingStart() + getAmpMargin();
            }
        }
        mPointRealX = pointX - getHalfPointWidth();
        float v2 = pointX + getHalfPointWidth();
        if (mReverseLayout) {
            mPointRealX = getWidth() - mPointRealX;
            v2 = getWidth() - v2;
        }
        float margin = DensityUtil.dp2px(getContext(), POINT_MARGIN);
        canvas.drawRect(mPointRealX, margin, v2, getHeight() - margin, mPointPaint);
        canvas.save();
    }

    public float getStartYByHeight(float amplitude) {
        float height = mAmplitudeView.getHeight();
        if (amplitude > height) {
            return 0;
        } else {
            return (height / WaveViewUtil.NUM_TWO
                    - (((amplitude / WaveViewUtil.NUM_TWO) == 0) ? WaveViewUtil.NUM_TWO : (amplitude / WaveViewUtil.NUM_TWO)));
        }
    }

    public float getEndYByHeight(float amplitude) {
        float height = mAmplitudeView.getHeight();
        if (amplitude > height) {
            return height;
        } else {
            return (height / WaveViewUtil.NUM_TWO
                    + (((amplitude / WaveViewUtil.NUM_TWO) == 0) ? WaveViewUtil.NUM_TWO : (amplitude / WaveViewUtil.NUM_TWO)));
        }
    }

    private boolean isLessNature() {
        if (mAmplitudes != null) {
            return mAmplitudes.size() < NATURE_COUNT;
        } else {
            return true;
        }
    }

    @Override
    protected void onMeasure(int width, int height) {
        super.onMeasure(width, height);
    }

    @Override
    public boolean onTouchEvent(MotionEvent motionEvent) {
        mPreTime = -1;
        switch (motionEvent.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                float x = motionEvent.getX();
                float y = motionEvent.getY();
                float padding = DensityUtil.dp2px(getContext(), CURRENT_LINE_PADDING);
                RectF rectF = new RectF(mPointRealX - padding, 0, mPointRealX + getPointWidth() + padding, getHeight());
                boolean contains = rectF.contains(x, y);
                if (contains) {
                    for (PointMoveListener listener : mListeners) {
                        listener.onTouchDownMiddleBar();
                    }
                }
                return contains;
            }
            case MotionEvent.ACTION_MOVE: {
                mIsTouch = true;
                long timeByX = getTimeByX(motionEvent.getX(), false);
                correctPointTime(timeByX);
                onMoveOnRedBar();
                postInvalidateOnAnimation();

            }
            return true;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL: {
                mIsTouch = false;
                long timeByX = getTimeByX(motionEvent.getX(), false);
                correctPointTime(timeByX);
                for (PointMoveListener listener : mListeners) {
                    listener.onTouchUpMiddleBar(mPointTime);
                }
                postInvalidateOnAnimation();
            }
            return true;
            default:
                return super.onTouchEvent(motionEvent);
        }
    }

    private void correctPointTime(long timeByX) {
        // 矫正时间：0-总时长之间
        if (timeByX < 0) {
            mPointTime = 0;
        } else {
            mPointTime = Math.min(timeByX, mTotalTime);
        }
        // 业务逻辑：不小于选择起点时间
        long startTime = mDragBar.getStartTime();
        if (mPointTime < startTime) {
            mPointTime = startTime;
        }
        // 业务逻辑：不大于选择结束时间
        long endTime = mDragBar.getEndTime();
        if (mPointTime > endTime) {
            mPointTime = endTime;
        }
    }

    /**
     * 根据触摸点坐标，计算触摸点的时间
     *
     * @param x 触摸点坐标
     * @return 触摸点的时间
     */
    public long getTimeByX(float x, boolean isChild) {
        //rtl
        float xValue = x;
        if (mReverseLayout && !isChild) {
            xValue = getWidth() - xValue;
        }

        //以预览条起点为基准，手指触摸点在预览条上的长度
        float preViewBarLength = 0;
        if (isChild) {
            preViewBarLength = xValue - getAmpMargin() - addFixWidth();
        } else {
            preViewBarLength = xValue - getPaddingStart() - getAmpMargin() - addFixWidth();
        }
        DebugUtil.i(TAG, "x=" + xValue + " addFixWidth="
                + addFixWidth() + " getPaddingStart=" + getPaddingStart() + " getAmpMargin=" + getAmpMargin());

        int totalAmpWidth = getTotalAmpWidth();
        if (totalAmpWidth == 0) {
            DebugUtil.i(TAG, "getTimeByX, totalAmpWidth is 0 return");
            return 0;
        }
        if (Float.isNaN(preViewBarLength)) {
            DebugUtil.i(TAG, "getTimeByX, preViewBarLength is NAN return");
        }
        BigDecimal bigPercent = new BigDecimal(preViewBarLength).divide(new BigDecimal(totalAmpWidth), PRECISION, RoundingMode.HALF_UP);
        return new BigDecimal(mTotalTime).multiply(bigPercent).longValue();
    }

    public float findChildXByTime(long mPointTime) {
        int totalAmpWidth = getTotalAmpWidth();
        if (totalAmpWidth == 0) {
            DebugUtil.i(TAG, "findChildXByTime, totalAmpWidth is 0 return");
            return 0;
        }
        BigDecimal inputTime = new BigDecimal(mPointTime);
        if (mTotalTime == 0) {
            DebugUtil.i(TAG, "findChildXByTime, mTotalTime is 0 return");
            return 0;
        }
        return inputTime.divide(new BigDecimal(mTotalTime), PRECISION, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(totalAmpWidth))
                .add(new BigDecimal(addFixWidth() + getAmpMargin()))
                .floatValue();
    }

    public void setPointTime(long currentTime, boolean isRightDrag) {
        if (mIsTouch) {
            return;
        }
        if (!isRightDrag) {
            mPreTime = -1;
        }
        if (mPreTime >= currentTime) {
            return;
        }
        /**
         *  When playing a recording with a playback time less than 1 minute,the global preview bar jitters.
         *  Increase the number of times the global preview bar is drawn to make it move smoothly.
         */
        if ((mTotalTime >= Constants.TIME_ONE_MINUTE) || (currentTime - mPointTime > DRAW_POINT_FRAME_THRESHOLD)) {
            mPointTime = currentTime;
            mPreTime = mPointTime;
            postInvalidateOnAnimation();
        } else {
            mPreTime = currentTime;
            refreshPoint(DRAW_POINT_FRAME_TIME);
        }
    }

    private void refreshPoint(final long delayTime) {
        if (mPreTime == -1) {
            return;
        }
        this.mPointTime += delayTime;
        if ((mPointTime != 0) && (mPointTime < mPreTime)) {
            postDelayed(() -> refreshPoint(delayTime), delayTime);
        } else {
            mPointTime = mPreTime;
        }
        postInvalidateOnAnimation();
    }

    public void resetPreTime() {
        mPreTime = -1;
    }

    public void setCutStartTime(long mCutStartTime) {
        DebugUtil.i(TAG, "setCutStartTime mCutStartTime=" + mCutStartTime);
        mDragBar.invalidateByStart(mCutStartTime);
        postInvalidateOnAnimation();
    }

    public void setCutEndTime(long mCutEndTime) {
        DebugUtil.i(TAG, "setCutEndTime mCutEndTime=" + mCutEndTime);
        mDragBar.invalidateByEnd(mCutEndTime);
        postInvalidateOnAnimation();
    }

    public interface PointMoveListener {
        /**
         * ACTION_DOWN 左右把手
         *
         * @param time
         */
        void onTouchDownOnBar(int direction, long time);

        /**
         * ACTION_MOVE 左边/右边 把手
         *
         * @param direction 左侧/右侧把手
         * @param time
         */
        void onMoveOnDragBar(int direction, long time);

        /**
         * ACTION_UP/ACTION_CANCEL
         *
         * @param time
         */
        void onTouchUpOnBar(int direction, long time);

        /**
         * ACTION_DOWN 红线
         */
        void onTouchDownMiddleBar();

        /**
         * ACTION_MOVE 中间红线
         *
         * @param time
         */
        void onMoveOnMiddleBar(long time);

        /**
         * ACTION_UP/ACTION_CANCEL
         *
         * @param time
         */
        void onTouchUpMiddleBar(long time);
    }

    public void resetStatus() {
        if (mAmplitudes != null) {
            mAmplitudes.clear();
        }
    }

    public void setSelectTime(long currentTime) {
        mPointTime = currentTime;
        mPreTime = currentTime;
        postInvalidateOnAnimation();
    }

    public void setTotalTime(long mTotalTime) {
        this.mTotalTime = mTotalTime;
        mDragBar.setMDuration(mTotalTime);
    }

    public long getTotalTime() {
        return mTotalTime;
    }

    public void setAmplitudes(List<Integer> amplitudes) {
        this.mAmplitudes = amplitudes;
        mAmplitudeView.postInvalidate();
        if (mAmplitudes != null && !mAmplitudes.isEmpty()) {
            addDragBar();
        }
    }

    public List<Integer> getAmplitudes() {
        return mAmplitudes;
    }

    private void addDragBar() {
        int height = (int) DensityUtil.dp2px(getContext(), DRAG_BAR_HEIGHT);
        LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, height);
        if (mDragBar.getParent() != null) {
            ((ViewGroup) mDragBar.getParent()).removeView(mDragBar);
        }
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mDragBar, layoutParams);
    }

    private float getPointWidth() {
        return getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp4);
    }

    private int getHalfPointWidth() {
        return (int) (Math.ceil(getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp4) * HALF_VALUE));
    }

    public boolean isLessOneScreen() {
        return isLessNature() || getTotalTime() < NATURE_TIME;
    }

    /**
     * 每个波形刻度的宽度
     */
    public float getAmplitudeWidth() {
        int width = getAvailableWidth();
        BigDecimal viewWidth = new BigDecimal(width);
        BigDecimal sizeBig = new BigDecimal(TOTAL_COUNT);
        return viewWidth.divide(sizeBig, PRECISION, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 波形总宽度
     */
    public int getTotalAmpWidth() {
        if (mAmplitudes == null || mAmplitudes.isEmpty()) {
            return 0;
        }
        int totalAmpWidth = 0;
        if (isLessOneScreen()) {
            totalAmpWidth = (int) ((mAmplitudes.size() * 2 - 1) * getAmplitudeWidth());
            DebugUtil.i(TAG, "mAmplitudes.size=" + mAmplitudes.size());
        } else {
            totalAmpWidth = getAvailableWidth();
        }
        DebugUtil.i(TAG, "previewBarWidth:" + totalAmpWidth);
        return totalAmpWidth;
    }

    public int getAvailableWidth() {
        return getWidth() - getPaddingStart() - getPaddingEnd() - getAmpMargin() * 2;
    }

    /**
     * 预览条起始位置距左边的距离
     *
     * @return 若 mAmplitudes.size() > NATURE_COUNT，则距左边距离为 0
     */
    public long addFixWidth() {
        if (mAmplitudes == null) {
            return 0;
        }
        return (getAvailableWidth() - getTotalAmpWidth()) / WaveViewUtil.NUM_TWO;
    }

    public float getDragBarStart() {
        return mDragBar.getDragBarStart();
    }

    public float getDragBarEnd() {
        return mDragBar.getDragBarEnd();
    }
}
