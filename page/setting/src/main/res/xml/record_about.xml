<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/app_name_main">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.aboutpage.COUIAppInfoPreference
        android:key="pref_app_info"
        android:selectable="false"
        app:appIcon="@drawable/ic_launcher_recorder"
        app:appName="@string/app_name_main"
        app:couiShowDivider="false"
        app:copyFinishText="@string/copied"
        app:copyText="@string/text_copy" />

    <com.coui.appcompat.preference.COUIPreferenceCategory app:isFirstCategory="true">
        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_record_policy"
            android:persistent="false"
            android:title="@string/runtime_permission_illustration_privacy" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="pref_record_license"
            android:persistent="false"
            android:title="@string/record_license" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="large_model_info"
            android:persistent="false"
            android:title="@string/setting_big_model_info" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>