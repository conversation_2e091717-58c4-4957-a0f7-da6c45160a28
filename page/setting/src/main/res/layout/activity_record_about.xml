<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="false">

    <FrameLayout
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <TextView
        android:id="@+id/tv_icp_info"
        style="@style/couiTextAppearanceDescription"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:layout_marginHorizontal="@dimen/dp16"
        android:background="@drawable/icp_text_ripple_bg"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginBottom="@dimen/setting_icp_info_margin_bottom"
        android:drawableRight="@drawable/ic_icp_info_arrow"
        android:gravity="center"
        android:text="@string/icp_info"
        android:textColor="?attr/couiColorHintNeutral"
        android:visibility="gone" />
</LinearLayout>