/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardScaleTransition
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.miniapp.transition

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.ViewGroup
import androidx.transition.Transition
import androidx.transition.TransitionValues

internal class AppCardAlphaTransition(private vararg val ids: Int) : Transition() {
    companion object {
        private const val PROP_NAME_CHANGE_ALPHA = "com.soundrecorder.dragonfly.card:change:alpha"
    }

    override fun captureEndValues(transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_ALPHA] = view.alpha
        }
    }

    override fun captureStartValues(transitionValues: TransitionValues) {
        val view = transitionValues.view ?: return
        if (view.id in ids) {
            transitionValues.values[PROP_NAME_CHANGE_ALPHA] = view.alpha
        }
    }

    override fun createAnimator(sceneRoot: ViewGroup, startValues: TransitionValues?, endValues: TransitionValues?): Animator? {
        if (startValues == null || endValues == null || endValues.view == null) {
            return null
        }
        val view = endValues.view
        val startAlpha = startValues.values[PROP_NAME_CHANGE_ALPHA] as Float
        val endAlpha = endValues.values[PROP_NAME_CHANGE_ALPHA] as Float
        if (startAlpha != endAlpha) {
            val animator = ValueAnimator.ofFloat(startAlpha, endAlpha)
            animator.addUpdateListener { animation ->
                val value = animation.animatedValue as Float
                view.alpha = value
            }
            return animator
        }
        return null
    }

    override fun getTransitionProperties() = arrayOf(PROP_NAME_CHANGE_ALPHA)
}