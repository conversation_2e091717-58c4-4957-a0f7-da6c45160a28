/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecordContinueUtil
 * Description:
 * Version: 1.0
 * Date: 2023/8/24
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/8/24 1.0 create
 */

package com.soundrecorder.miniapp

import android.app.Activity
import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.AppCardUtils.addContinueRequestPermissionFlag
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.utils.Injector

object MiniRecordContinueUtil {
    private const val LOG_TAG = "MiniRecordContinueUtil"
    const val INTENT_ACTION_MINIAPP_DEFAULT_CONTINUE = "com.soundrecorder.MINI_APP_CONTINUE_LAUNCHER"

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    /**
     * 无权限接续内屏授予权限
     * 目前用于外销需要运行时权限时使用
     */
    @JvmStatic
    fun continueToRecorderActivityByContinueFlag(context: Context) {
        context.startActivity(Intent(context, recordApi?.getRecorderActivityClass()).apply {
            setPackage(context.packageName)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            addContinueRequestPermissionFlag(context.getString(com.soundrecorder.common.R.string.continue_set_permssion_to_main_screen))
        })
    }


    /**
     * 【通过系统暂态层接续页面，展开手机接续】
     * 接续到内屏隐私政策页面，点击返回回到首页权限弹窗
     * @param context
     * @param nextAction 跳转下一个页面action
     * @see MiniAppConstant NEXT_ACTION_RECORDER_PRIVACY or  NEXT_ACTION_BOOT_PRIVACY or NEXT_ACTION_APP_SETTING
     * @param privacyDialogType 用户须知弹窗type，一致性
     */
    @JvmStatic
    fun continueGotoPrivacyPageByContinueFlag(context: Activity, nextAction: Int) {
        DebugUtil.i(LOG_TAG, "continueGotoPrivacyPageByContinueFlag")
        ActivityTaskUtils.clearAllTask() // 清栈
        val browseFileClass = browseFileApi?.getBrowseFileClass() ?: return
        val intent = Intent(context, browseFileClass)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Intent.ACTION_MAIN // 接续到内屏应用主页的，在接续的跳转中需新增action、category配置
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.addContinueRequestPermissionFlag(context.getString(com.soundrecorder.common.R.string.view_full_info_policy))
        intent.putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, nextAction)
        context.startActivity(intent)
    }

    /**
     * 【通过系统暂态层接续页面，展开手机接续】
     * 接续到内屏录音-应用详情页面
     * @param context
     * @param backPageClass 应用详情返回显示的页面
     */
    @JvmStatic
    fun continueToRecordSettingDetailByContinueFlag(context: Activity, backPageClass: Class<*>, permissions: ArrayList<String>?) {
        DebugUtil.i(LOG_TAG, "continueToRecordSettingDetailByContinueFlag")
        ActivityTaskUtils.clearAllTask()
        val intent = Intent(context, backPageClass)
        intent.putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, MiniAppConstant.NEXT_ACTION_APP_SETTING)
        permissions?.let {
            val bundle = Bundle()
            bundle.putStringArrayList(MiniAppConstant.MINI_PERMISSION_KEY, permissions)
            intent.putExtras(bundle)
        }
        if (backPageClass == browseFileApi?.getBrowseFileClass()) {
            intent.action = Intent.ACTION_MAIN // 接续到内屏应用主页的，在接续的跳转中需新增action、category配置
            intent.addCategory(Intent.CATEGORY_LAUNCHER)
        }
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.addContinueRequestPermissionFlag(context.getString(com.soundrecorder.common.R.string.continue_set_permssion_to_main_screen))
        context.startActivity(intent)
    }

    /**
     * 当前属于录制状态
     * 接续到内屏录制页面，且跳转到录音设置
     */
    @JvmStatic
    fun continueToSettingByContinueFlagOnRecordingState(context: Activity, permissions: ArrayList<String>?) {
        DebugUtil.i(LOG_TAG, "continueToSettingByContinueFlagOnRecordingState")
        val targetClass = recordApi?.getRecorderActivityClass() ?: return
        val intent = Intent(context, targetClass)
        permissions?.let {
            val bundle = Bundle()
            bundle.putStringArrayList(MiniAppConstant.MINI_PERMISSION_KEY, permissions)
            intent.putExtras(bundle)
        }
        intent.putExtra(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, MiniAppConstant.NEXT_ACTION_APP_SETTING)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.addContinueRequestPermissionFlag(context.getString(com.soundrecorder.common.R.string.continue_set_permssion_to_main_screen))
        context.startActivity(intent)
    }

    /**
     * 【展开手机自动接续】
     * 接续到内屏首页，显示用户须知弹窗
     * @param context
     * @param showingPrivacyDialogType 用户须知弹窗type
     * @see com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
     * @param showNotificationSnackBar 是否显示底部通知权限引导snackBar true:显示  false：不显示
     * @param canShowAllFileDialogWhenResume onResume 是否弹所有文件管理权限弹窗(nextAction+无所有文件管理权限)
     */
    @JvmStatic
    fun continueBrowseFileByExpand(
        context: Activity,
        showNotificationSnackBar: Boolean = false,
        canShowAllFileDialogWhenResume: Boolean = true
    ) {
        DebugUtil.i(LOG_TAG, "continueBrowseFileByExpand,showNotificationSnackBar=$showNotificationSnackBar")
        val targetClass = browseFileApi?.getBrowseFileClass() ?: return
        val intent = Intent(context, targetClass)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        intent.action = Intent.ACTION_MAIN // 接续到内屏应用主页的，在接续的跳转中需新增action、category配置
        intent.addCategory(Intent.CATEGORY_LAUNCHER)
        intent.putExtra(MiniAppConstant.EXTRA_NAME_CHECK_SHOW_NOTIFICATION_SNACK_BAR, showNotificationSnackBar)
        // 显示snackBar，进入录制页面，onResume就不要显示所有文件管理权限弹窗
        intent.putExtra(MiniAppConstant.EXTRA_NAME_SHOW_ALL_FILE_PERMISSION_RESUME, canShowAllFileDialogWhenResume)
        context.startActivity(intent, ActivityOptions.makeBasic().apply { launchDisplayId = DisplayUtils.mainId }.toBundle())
    }


    /**
     * 【展开手机自动接续】
     * 接续到内屏录制页面
     * @param context
     * @param clearTask
     * @param showNotificationSnackBar 是否显示底部通知权限引导snackBar true:显示  false：不显示
     * @param canShowAllFileDialogWhenResume onResume 是否弹所有文件管理权限弹窗(nextAction+无所有文件管理权限)
     */
    @JvmStatic
    fun continueRecorderActivityByExpand(
        context: Activity,
        clearTask: Boolean = true,
        showNotificationSnackBar: Boolean = false,
        canShowAllFileDialogWhenResume: Boolean = true
    ) {
        DebugUtil.i(LOG_TAG, "continueRecorderActivityByExpand,clearTask=$clearTask,showNotificationSnackBar=$showNotificationSnackBar")
        val targetClass = recordApi?.getRecorderActivityClass() ?: return
        val intent = Intent(context, targetClass)
        intent.putExtra(MiniAppConstant.EXTRA_NAME_CHECK_SHOW_NOTIFICATION_SNACK_BAR, showNotificationSnackBar)
        // 显示snackBar，进入录制页面，onResume就不要显示所有文件管理权限弹窗
        intent.putExtra(MiniAppConstant.EXTRA_NAME_SHOW_ALL_FILE_PERMISSION_RESUME, canShowAllFileDialogWhenResume)
        if (clearTask) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        } else {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
        context.startActivity(intent, ActivityOptions.makeBasic().apply { launchDisplayId = DisplayUtils.mainId }.toBundle())
    }

    /**
     * 【展开手机自动接续】
     * 录制默认状态接续到TransparentActivity，具体逻辑TransparentActivity中
     * 1.内屏页面再快捷播放、播放、裁切页面： 把录音拉到前台即可
     * 2.否则，到录音首页列表
     */
    @JvmStatic
    fun continueByExpandWhenDefaultState(context: Activity) {
        DebugUtil.i(LOG_TAG, "continueByExpandWhenDefaultState")
        val intent = Intent(INTENT_ACTION_MINIAPP_DEFAULT_CONTINUE)
        intent.setPackage(context.packageName)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent, ActivityOptions.makeBasic().apply { launchDisplayId = DisplayUtils.mainId }.toBundle())
    }
}