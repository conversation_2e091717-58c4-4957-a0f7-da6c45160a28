/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderAppCardWidgetProvider
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.soundrecorder.common.card

import android.content.Context
import android.os.Bundle
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.oplus.cardwidget.util.getCardType
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.AppCardInterface
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_DRAGON_FLY
import com.soundrecorder.modulerouter.utils.Injector

class DragonFlyAppCardWidgetProvider : AppCardWidgetProvider() {

    companion object {
        const val CARD_TAG = "DragonFlyAppCardWidgetProvider"
    }

    private val appCardApi by lazy {
        Injector.injectFactory<AppCardInterface>()
    }

    override fun onCardsObserve(context: Context, widgetCodes: List<String>) {
        super.onCardsObserve(context, widgetCodes)
        DebugUtil.i(CARD_TAG, "onCardsObserve,widgetCodes = ${widgetCodes.toTypedArray()}")
        widgetCodes.forEach {
            appCardApi?.addWidgetCodes(it)
        }
    }

    override fun getCardLayoutName(widgetCode: String): String {
        DebugUtil.i(CARD_TAG, "getCardLayoutName,widgetCode = $widgetCode")
        val cardType = widgetCode.getCardType()
        if (cardType == CARD_TYPE_FOR_DRAGON_FLY) {
            return "dragon_fly_recorder_card.json"
        }
        return ""
    }


    override fun onResume(context: Context, widgetCode: String) {}

    override fun subscribed(context: Context, widgetCode: String) {
        super.subscribed(context, widgetCode)
        appCardApi?.addWidgetCodesOnResume(widgetCode)
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        super.unSubscribed(context, widgetCode)
        appCardApi?.removeWidgetCodeOnPause(widgetCode)
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        if (!checkCallPermission()) return null
        val widgetCode = extras?.getString("widgetCode") ?: ""
        DebugUtil.i(CARD_TAG, "method = $method, widgetCode = $widgetCode")
        val bundle = appCardApi?.callFromDragonFlyCard(method, widgetCode)
        if (bundle != null) {
            return bundle
        }
        return super.call(method, arg, extras)
    }
}