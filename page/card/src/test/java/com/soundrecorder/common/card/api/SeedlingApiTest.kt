/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.api

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.card.RecordSeedlingCardWidgetProvider
import com.soundrecorder.common.card.shadows.ShadowFeatureOption
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_FLUID_CARD
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_OLD
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_SEEDLING_CARD
import org.json.JSONObject
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SeedlingApiTest {

    private var context: Context? = null
    private var mockedStaticSdkApi: MockedStatic<SeedlingSdkApi>? = null

    @Before
    fun init() {
        context = ApplicationProvider.getApplicationContext()
        mockedStaticSdkApi = Mockito.mockStatic(SeedlingSdkApi::class.java)
    }

    @After
    fun release() {
        SeedlingApi.release()
        mockedStaticSdkApi?.close()
        mockedStaticSdkApi = null
        context = null
    }

    @Test
    fun should_notNull_when_init() {
        Assert.assertNull(SeedlingApi.mSeedingCardProcess)
        SeedlingApi.init()
        Assert.assertNotNull(SeedlingApi.mSeedingCardProcess)
    }

    @Test
    fun should_success_when_getStatusBarSupportTypeDirectly() {
        Mockito.`when`(SeedlingSdkApi.isSupportFluidCloud(any(Context::class.java)))
            .thenReturn(true, false, false)
        SeedlingApi.getStatusBarSupportTypeDirectly(context!!) {
            Assert.assertEquals(STATUS_BAR_SUPPORT_FLUID_CARD, it)
        }

        Mockito.`when`(SeedlingSdkApi.isSupportSystemSendIntent(any(Context::class.java)))
            .thenReturn(true, false)
        SeedlingApi.getStatusBarSupportTypeDirectly(context!!) {
            Assert.assertEquals(STATUS_BAR_SUPPORT_SEEDLING_CARD, it)
        }

        SeedlingApi.getStatusBarSupportTypeDirectly(context!!) {
            Assert.assertEquals(STATUS_BAR_SUPPORT_OLD, it)
        }
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    fun should_invoke_when_initSupportFluidCardCallback() {
        val callback: (Boolean?, Boolean) -> Unit = { _, _ ->
            //do nothing
        }
        SeedlingApi.initSupportFluidCardCallback(context!!, callback)
        mockedStaticSdkApi?.verify({
            SeedlingSdkApi.initSupportFluidCardCallback(context!!, callback)
        }, times(1))
    }

    @Test
    fun should_invoke_when_isSupportFluidCloud() {
        SeedlingApi.isSupportFluidCloud(context!!)
        mockedStaticSdkApi?.verify(
            { SeedlingSdkApi.isSupportFluidCloud(any(Context::class.java)) },
            times(1)
        )
    }

    @Test
    fun should_invoke_when_isSupportSystemSendIntent() {
        SeedlingApi.isSupportSystemSendIntent(context!!)
        Mockito.`when`(SeedlingSdkApi.isSupportSystemSendIntent(any(Context::class.java)))
            .thenReturn(true)
        mockedStaticSdkApi?.verify(
            { SeedlingSdkApi.isSupportSystemSendIntent(any(Context::class.java)) },
            times(1)
        )
    }

    @Test
    @Ignore
    fun should_invoke_when_refreshSeedlingData() {
        SeedlingApi.refreshSeedlingData(JSONObject())
        Mockito.mockStatic(RecordSeedlingCardWidgetProvider::class.java).use {
            it.verify(
                { RecordSeedlingCardWidgetProvider.refreshSeedlingData(jsonData = any(JSONObject::class.java)) },
                times(1)
            )
        }
    }

    @Test
    fun should_invoke_when_registerResultCallBack() {
        SeedlingApi.registerResultCallBack()
        mockedStaticSdkApi?.verify(
            { SeedlingSdkApi.registerResultCallBack() },
            times(1)
        )
    }

    @Test
    fun should_null_when_release() {
        SeedlingApi.release()
        Assert.assertNull(SeedlingApi.mSeedingCardProcess)
    }
}