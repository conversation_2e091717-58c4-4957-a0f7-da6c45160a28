/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: BrowsePanelController
 * Description:
 * Version: 1.0
 * Date: 2023/1/13
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/1/13 1.0 create
 */

package com.soundrecorder.browsefile.parentchild

import android.content.Context
import android.os.Build
import android.os.Looper
import android.view.View
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.isInvisible
import androidx.fragment.app.Fragment
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_0
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_1
import com.soundrecorder.browsefile.parentchild.BrowseAnimUtil.Companion.GUIDE_PERCENT_NEGATIVE_0_3
import com.soundrecorder.browsefile.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowMultiFileObserver
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowOplusCompactUtil
import com.soundrecorder.browsefile.shadows.ShadowStorageManager
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.Shadows.shadowOf
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class,
        ShadowMultiFileObserver::class, ShadowOplusCompactUtil::class,
        ShadowStorageManager::class, ShadowCOUIVersionUtil::class])
class BrowsePanelControllerTest {
    private val funNameUpdateWindowTypeChanged = "updateLayoutWhenWindowTypeChanged"
    private val funNameOnPlayRecordChanged = "onPlayRecordChanged"
    private val internalStateAnimUtils = "mAnimUtils"
    private val mediaId = 10L
    private val mediaIdSecond = 20L
    private var activityController: ActivityController<BrowseFile>? = null
    private var activity: BrowseFile? = null
    private var fragment: BrowseFragment? = null
    private var context: Context? = null
    private var viewModel: BrowseFileActivityViewModel? = null
    private var controller: BrowsePanelController? = null

    private val playback = mockk<PlayBackInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { playback }
        })
    }

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activityController = Robolectric.buildActivity(BrowseFile::class.java)
        activity = activityController?.create()?.resume()?.get()
        fragment = activity?.supportFragmentManager?.fragments?.get(0) as BrowseFragment
        viewModel = Whitebox.getInternalState(activity, "mBrowseFileActivityViewModel")
        controller = Whitebox.getInternalState<BrowsePanelController>(activity, "mBrowsePanelController")
        startKoin(koinApp)
        every { playback.newPlaybackEmptyFragment() } returns Fragment()
    }

    @After
    fun tearDown() {
        stopKoin()
        fragment = null
        activity = null
        activityController = null
        context = null
        viewModel = null
    }

    @Test
    fun should_notNull_when_init() {
        Assert.assertNotNull(controller)
        Assert.assertNotNull(controller?.getBrowseFragment())

        val dividerLine = Whitebox.getInternalState<View>(controller, "parentDividerLine")
        Assert.assertNotNull(dividerLine)

        Assert.assertTrue(viewModel?.windowType?.hasObservers() ?: false)
        Assert.assertTrue(viewModel?.mCurrentPlayRecordData?.hasObservers() ?: false)
        Assert.assertTrue(viewModel?.allRecordCount?.hasObservers() ?: false)
    }

    @Test
    fun should_when_noPlayData_updateLayoutWhenWindowTypeChanged() {
        val dividerLine = Whitebox.getInternalState<View>(controller, "parentDividerLine")
        val parentStartLine = Whitebox.getInternalState<Guideline>(controller, "parentStartLine")
        val parentEndLine = Whitebox.getInternalState<Guideline>(controller, "parentEndLine")
        val childStartLine = Whitebox.getInternalState<Guideline>(controller, "childStartLine")

        shadowOf(Looper.getMainLooper()).idle()
        viewModel?.windowType?.value = WindowType.SMALL
        Whitebox.invokeMethod<Void>(controller, funNameUpdateWindowTypeChanged, WindowType.SMALL)

        Assert.assertTrue(dividerLine.isInvisible)
        Assert.assertTrue(parentStartLine.guidePercent() == GUIDE_PERCENT_0)
        Assert.assertTrue(parentEndLine.guidePercent() == GUIDE_PERCENT_1)

        viewModel?.windowType?.value = WindowType.MIDDLE
        Whitebox.invokeMethod<Void>(controller, funNameUpdateWindowTypeChanged, WindowType.MIDDLE)
        Assert.assertFalse(dividerLine.isInvisible)
        Assert.assertTrue(parentStartLine.guidePercent() == GUIDE_PERCENT_0)
        Assert.assertTrue(parentEndLine.guidePercent() == childStartLine.guidePercent())

        viewModel?.windowType?.value = WindowType.SMALL
        Whitebox.invokeMethod<Void>(controller, funNameUpdateWindowTypeChanged, WindowType.SMALL)
    }

    @Test
    fun should_when_hasPlayData_updateLayoutWhenWindowTypeChanged() {
        viewModel?.mCurrentPlayRecordData?.value = StartPlayModel()
        Whitebox.invokeMethod<Void>(controller, funNameUpdateWindowTypeChanged, WindowType.SMALL)
        val parentStartLine = Whitebox.getInternalState<Guideline>(controller, "parentStartLine")
        val parentEndLine = Whitebox.getInternalState<Guideline>(controller, "parentEndLine")
        val childStartLine = Whitebox.getInternalState<Guideline>(controller, "childStartLine")

        Assert.assertTrue(parentStartLine.guidePercent() == GUIDE_PERCENT_NEGATIVE_0_3)
        Assert.assertTrue(parentEndLine.guidePercent() == BrowseAnimUtil.GUIDE_PERCENT_0_7)
        Assert.assertTrue(childStartLine.guidePercent() == GUIDE_PERCENT_0)
    }

    @Test
    fun should_when_smallWindow_onPlayRecordChanged() {
        Assert.assertNull(controller?.getPlaybackContainerFragment())
        Assert.assertNull(controller?.getPlaybackEmptyFragment())
        shadowOf(Looper.getMainLooper()).idle()

        viewModel?.allRecordCount?.value = 1
        viewModel?.windowType?.value = WindowType.SMALL // data from null to value
        val data = StartPlayModel(mediaId)
        viewModel?.mCurrentPlayRecordData?.value = data
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data)

        Assert.assertNotNull(Whitebox.getInternalState(controller, internalStateAnimUtils))
//        Assert.assertNotNull(controller?.getPlaybackContainerFragment())
        Assert.assertTrue(viewModel?.lastRecordId == mediaId)

        controller?.onDestroy()
        Assert.assertNull(Whitebox.getInternalState(controller, internalStateAnimUtils))

        // data value changed same
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data) // data value changed differ
        val data2 = StartPlayModel(mediaIdSecond)
        viewModel?.mCurrentPlayRecordData?.value = data2
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data2)

        // data change to null
        viewModel?.clearPlayRecordData()
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, null as? StartPlayModel?)
        Assert.assertNull(controller?.getPlaybackContainerFragment())
        Assert.assertNull(controller?.getPlaybackEmptyFragment())
        Assert.assertTrue(viewModel?.lastRecordId == -1L)
    }

    @Test
    fun should_when_middleWindow_onPlayRecordChanged() {
        shadowOf(Looper.getMainLooper()).idle()
        viewModel?.windowType?.value = WindowType.MIDDLE
        viewModel?.allRecordCount?.value = 1
        viewModel?.clearPlayRecordData()
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, null as? StartPlayModel?)
        Assert.assertNull(controller?.getPlaybackContainerFragment())
//        Assert.assertNotNull(controller?.getPlaybackEmptyFragment())

        // data from null to value
        val data = StartPlayModel(mediaId)
        viewModel?.mCurrentPlayRecordData?.value = data
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data)
        controller?.pausePlayDetail(true)

        Assert.assertNull(Whitebox.getInternalState(controller, internalStateAnimUtils))
//        Assert.assertNotNull(controller?.getPlaybackContainerFragment())
        Assert.assertTrue(viewModel?.lastRecordId == mediaId)

        // data value changed same
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data) // data value changed differ
        val data2 = StartPlayModel(mediaIdSecond)
        viewModel?.mCurrentPlayRecordData?.value = data2
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, data2)

        // data change to null
        viewModel?.clearPlayRecordData()
        Whitebox.invokeMethod<Void>(controller, funNameOnPlayRecordChanged, null as? StartPlayModel?)
        Assert.assertNull(controller?.getPlaybackContainerFragment())
//        Assert.assertNotNull(controller?.getPlaybackEmptyFragment())
        Assert.assertTrue(viewModel?.lastRecordId == -1L)
    }
}