/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/4/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.os.Build;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.seekbar.COUISeekBar;
import com.soundrecorder.base.ext.ExtKt;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ItemBrowsePlayInfoLayoutTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_equals_max_when_initSeekBar() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        COUISeekBar seekBar = Whitebox.getInternalState(layout, "seekBar");
        Assert.assertEquals(seekBar.getMax(), ItemBrowsePlayInfoLayout.PROGRESS_MAX);
    }

    @Test
    public void should_equals_when_updatePlayProgress() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        long duration = 6000;
        layout.setDuration(duration);
        long curTime = 1000;
        layout.updatePlayProgress(curTime);
        TextView playProgress = Whitebox.getInternalState(layout, "playProgress");
        TextView playDuration = Whitebox.getInternalState(layout, "playTotalDuration");
        Assert.assertEquals(playProgress.getText().toString(), ExtKt.currentInMsFormatTimeExclusive(curTime, duration));
        Assert.assertEquals(playDuration.getText().toString(), ExtKt.durationInMsFormatTimeExclusive(duration, true));
    }

    @Test
    public void should_return_true_when_isNotCompleteVisible() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.setVisibility(View.GONE);
        Assert.assertTrue(layout.isNotCompleteVisible());
    }

    @Test
    public void should_return_not_zero_when_startOrContinueEnterAnim() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        layout.startOrContinueEnterAnim(null, new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                Assert.assertTrue(layout.getHeight() != 0);
            }
        });
    }

    @Test
    public void should_return_zero_when_startOrContinueExitAnim() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        layout.startOrContinueExitAnim(null, new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                Assert.assertEquals(layout.getHeight(), 0);
            }
        });
    }

    @Test
    public void should_visible_when_setExpandState() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        layout.setExpandState();
        Assert.assertEquals(layout.getVisibility(), View.VISIBLE);
    }

    @Test
    public void should_gone_when_setShrinkState() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        layout.setShrinkState();
        Assert.assertEquals(layout.getVisibility(), View.GONE);
    }

    @Test
    public void should_return_null_when_cancelAnimation() {
        ItemBrowsePlayInfoLayout layout = new ItemBrowsePlayInfoLayout(mContext);
        layout.cancelAnimation();
        Animator enterAnimator = Whitebox.getInternalState(layout, "enterAnim");
        Assert.assertNull(enterAnimator);
    }
}
