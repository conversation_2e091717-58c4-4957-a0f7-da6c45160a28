package com.soundrecorder.browsefile.search.item

import android.content.Context
import android.os.Build
import android.text.SpannableStringBuilder
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox.invokeMethod
import org.robolectric.annotation.Config
import java.util.*

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class ItemSearchViewHolderTest {
    private var mContext: Context? = null
    var titleColorIndex: ArrayList<Int> = ArrayList<Int>()

    @Before
    fun setUp() {
        titleColorIndex.add(com.soundrecorder.common.R.color.black_color)
        titleColorIndex.add(com.soundrecorder.common.R.color.white_color)
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        mContext = null
    }

    @Test
    @Ignore
    fun should_notNull_when_getRecordTitle() {
        val itemSearchView = Mockito.mock(ItemSearchViewHolder::class.java)
        val itemSearchViewModel = Mockito.mock(ItemSearchViewModel::class.java)
        Mockito.doReturn(1).`when`(itemSearchViewModel).taskId
        Mockito.doReturn("1111").`when`(itemSearchViewModel).title
        Mockito.doReturn("1111").`when`(itemSearchViewModel).displayName
        Mockito.doReturn(true).`when`(itemSearchViewModel).allContains
        Mockito.doReturn(titleColorIndex).`when`(itemSearchViewModel).titleColorIndex
        invokeMethod<Any>(itemSearchView, "setViewModel", itemSearchViewModel)
        Assert.assertNotNull(invokeMethod<SpannableStringBuilder>(itemSearchView, "getRecordTitle", itemSearchViewModel))
    }

    @Test
    @Ignore
    fun should_notNull_when_getConvertText() {
        val itemSearchView = Mockito.mock(ItemSearchViewHolder::class.java)
        Assert.assertNotNull(invokeMethod<SpannableStringBuilder>(itemSearchView, "getConvertText", "2223", titleColorIndex))
    }
}