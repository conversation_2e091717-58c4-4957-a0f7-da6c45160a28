package com.soundrecorder.browsefile.search.load.center.filechange

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class CenterFileChangeDataFactoryTest {
    private var mMockDataFactory: CenterFileChangeDataFactory? = null

    @Before
    fun setUp() {
        mMockDataFactory = CenterFileChangeDataFactory()
    }

    @Test
    fun should_returnNull_when_queryUpdatedIdData() {
        val result = mMockDataFactory?.queryUpdatedIdData(arrayListOf())
        assert(result == null)
    }

    @Test
    fun should_returnNull_CursorIsNull_when_genLocalDataList() {
        val result = mMockDataFactory?.genLocalDataList(null)
        assert(result != null)
    }

    @Test
    fun should_returnNotNull_when_queryInsertBeanByIds() {
        val rowIds: MutableList<Long> = ArrayList()
        rowIds.add(1L)
        rowIds.add(2L)
        val searchInsertBeans: List<SearchInsertBean>? = mMockDataFactory?.queryInsertBeanByIds(rowIds, true)
        Assert.assertNotNull(searchInsertBeans)
    }
}