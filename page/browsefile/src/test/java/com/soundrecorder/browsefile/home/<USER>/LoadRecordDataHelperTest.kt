/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LoadRecordDataHelperTest
 * Description:
 * Version: 1.0
 * Date: 2024/1/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/1/15 1.0 create
 */

package com.soundrecorder.browsefile.home.load

import android.database.Cursor
import android.os.Build
import android.provider.MediaStore
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.UploadRecord
import com.soundrecorder.common.utils.ConvertDbUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusUsbEnvironment::class]
)
class LoadRecordDataHelperTest {

    @Test
    fun should_correct_when_addCompleteFlag() {
        val cursor = Mockito.mock(Cursor::class.java)
        Mockito.`when`(cursor.moveToFirst()).thenReturn(true)
        Mockito.`when`(cursor.columnNames).thenReturn(arrayOf(MediaStore.Audio.Media.RELATIVE_PATH))
        val result = LoadRecordDataHelper.addCompleteFlag(cursor, 0)
        Assert.assertNotNull(result)
    }

    @Test
    fun should_correct_when_parseStringColumn() {
        val listCount = BrowseListCount()
        var result = Whitebox.invokeMethod<Boolean>(
            LoadRecordDataHelper,
            "parseStringColumn",
            "",
            "",
            0,
            listCount
        )
        Assert.assertFalse(result)

        val relativeColumnName = MediaStore.Audio.Media.RELATIVE_PATH
        result = Whitebox.invokeMethod(
            LoadRecordDataHelper,
            "parseStringColumn",
            RecordModeConstant.RELATIVE_PATH_STANDARD,
            relativeColumnName,
            RecordModeConstant.BUCKET_VALUE_STANDARD,
            listCount
        )
        Assert.assertTrue(result)

        result = Whitebox.invokeMethod(
            LoadRecordDataHelper,
            "parseStringColumn",
            RecordModeConstant.RELATIVE_PATH_MEETING,
            relativeColumnName,
            RecordModeConstant.BUCKET_VALUE_CONFERENCE,
            listCount
        )
        Assert.assertTrue(result)

        result = Whitebox.invokeMethod(
            LoadRecordDataHelper,
            "parseStringColumn",
            RecordModeConstant.RELATIVE_PATH_INTERVIEW,
            relativeColumnName,
            RecordModeConstant.BUCKET_VALUE_INTERVIEW,
            listCount
        )
        Assert.assertTrue(result)

        result = Whitebox.invokeMethod(
            LoadRecordDataHelper,
            "parseStringColumn",
            RecordModeConstant.RELATIVE_PATH_CALL,
            relativeColumnName,
            RecordModeConstant.BUCKET_VALUE_CALL,
            listCount
        )
        Assert.assertTrue(result)

        result = Whitebox.invokeMethod(
            LoadRecordDataHelper,
            "parseStringColumn",
            "",
            relativeColumnName,
            RecordModeConstant.BUCKET_VALUE_ALL,
            listCount
        )
        Assert.assertTrue(result)
    }

    @Test
    fun should_correct_when_getConvertDbMap() {
        val record = ConvertRecord(1)
        val uploadRecordList1: MutableList<UploadRecord> = arrayListOf()
        uploadRecordList1.add(UploadRecord(0, "", 0, 0, "etag_1", "", 1))
        val record1 = ConvertRecord(-1)

        val staticConvertDb = Mockito.mockStatic(ConvertDbUtil::class.java)
        staticConvertDb.`when`<List<ConvertRecord>> { ConvertDbUtil.selectAll() }.thenReturn(
            mutableListOf(record, record1)
        )

        val result = LoadRecordDataHelper.getConvertDbMap()
        Assert.assertTrue(result.isNotEmpty())

        staticConvertDb.close()
    }
}