package com.soundrecorder.browsefile.home.dialog.navigation;

import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.robolectric.Shadows.shadowOf;

import android.app.Dialog;
import android.content.Intent;
import android.os.Build;
import android.view.MenuItem;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.bottomnavigation.COUINavigationView;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.R;
import com.soundrecorder.browsefile.shadows.ShadowAppFeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowCOUIMaxHeightScrollView;
import com.soundrecorder.browsefile.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.databean.Record;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowAppFeatureUtil.class,
        ShadowCOUIVersionUtil.class, ShadowCOUIMaxHeightScrollView.class})
public class NavigationViewManagerTest {
    private BrowseFile mActivity;
    private BrowseFile mSpyActivity;
    private static final String METHOD_MESSAGE = "getMessage";

    @Before
    public void setUp() {
        mActivity = Robolectric.buildActivity(BrowseFile.class).get();
        mSpyActivity = spy(mActivity);
    }

    @Test
    public void should_startActivity_when_menuItemSelectedSend() {
        COUINavigationView couiNavigationView = new COUINavigationView(mSpyActivity);
        NavigationViewManager navigationViewManager = new NavigationViewManager(mSpyActivity, couiNavigationView, false);
        MenuItem mockMenuItem = Mockito.mock(MenuItem.class);
        Mockito.when(mockMenuItem.getItemId()).thenReturn(R.id.item_send);
        navigationViewManager.click(mockMenuItem, null, null, null);
        Intent intent = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        Assert.assertNull(intent);
    }

    @Test
    public void should_popDialog_when_showProgress() throws Exception {
        COUINavigationView couiNavigationView = new COUINavigationView(mSpyActivity);
        NavigationViewManager navigationViewManager = new NavigationViewManager(mSpyActivity, couiNavigationView, false);
        Whitebox.invokeMethod(navigationViewManager, "showProgress");
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);

        navigationViewManager.release();
    }

    @Test
    public void should_popDialog_when_showDeleteDialog() throws Exception {
        COUINavigationView couiNavigationView = new COUINavigationView(mSpyActivity);
        NavigationViewManager navigationViewManager = new NavigationViewManager(mSpyActivity, couiNavigationView, false);
        Whitebox.invokeMethod(navigationViewManager, "showDeleteDialog", true);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);

        navigationViewManager.release();
        Dialog deleteDialog = Whitebox.getInternalState(navigationViewManager, "mDeleteDialog");
        Assert.assertNull(deleteDialog);
    }

    @Test
    public void should_popDialog_when_showRenameDialog() throws Exception {
        COUINavigationView couiNavigationView = new COUINavigationView(mSpyActivity);
        NavigationViewManager navigationViewManager = new NavigationViewManager(mSpyActivity, couiNavigationView, false);
        Record record = Mockito.mock(Record.class);
        when(record.getDisplayName()).thenReturn("111");
        Whitebox.invokeMethod(navigationViewManager, "showRenameDialog", record);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(dialog);

        navigationViewManager.release();
        Dialog renameDialog = Whitebox.getInternalState(navigationViewManager, "mRenameDialog");
        Assert.assertNull(renameDialog);
    }

    @Test
    public void should_when_getTitle() throws Exception {
        COUINavigationView couiNavigationView = new COUINavigationView(mSpyActivity);
        NavigationViewManager navigationViewManager = new NavigationViewManager(mSpyActivity, couiNavigationView, false);
        String titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 0, true, true);
        Assert.assertEquals(mSpyActivity.getString(com.soundrecorder.common.R.string.all_record_will_be_deleted_from_device_or_cloud), titleStr);
        titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 0, false, true);
        Assert.assertEquals(mSpyActivity.getString(com.soundrecorder.common.R.string.all_record_will_be_deleted_from_device), titleStr);
        titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 1, false, false);
        Assert.assertEquals(mSpyActivity.getString(com.soundrecorder.common.R.string.the_record_will_be_deleted_from_device), titleStr);
        titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 1, true, false);
        Assert.assertEquals(mSpyActivity.getString(com.soundrecorder.common.R.string.the_record_will_be_deleted_from_device_or_cloud), titleStr);
        titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 2, true, false);
        Assert.assertEquals(mSpyActivity.getResources().getQuantityString(com.soundrecorder.common.R.plurals.count_record_will_be_deleted_from_device_or_cloud, 2, 2), titleStr);
        titleStr = Whitebox.invokeMethod(navigationViewManager, METHOD_MESSAGE, 2, false, false);
        Assert.assertEquals(mSpyActivity.getResources().getQuantityString(com.soundrecorder.common.R.plurals.count_record_will_be_deleted_from_device, 2, 2), titleStr);
    }

    @After
    public void tearDown() {
        mSpyActivity = null;
        mActivity = null;
    }
}
