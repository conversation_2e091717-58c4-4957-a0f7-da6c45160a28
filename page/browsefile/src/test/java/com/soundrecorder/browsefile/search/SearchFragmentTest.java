package com.soundrecorder.browsefile.search;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import android.os.Build;

import androidx.fragment.app.Fragment;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;

import java.util.List;

import com.soundrecorder.browsefile.R;
import com.soundrecorder.browsefile.search.item.head.SearchHeaderAdapter;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowMultiFileObserver;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class,
                ShadowFeatureOption.class, ShadowMultiFileObserver.class})
public class SearchFragmentTest {

    private ActivityController<BrowseFile> mController;
    private BrowseFile mActivity;
    private SearchFragment mFragment;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(BrowseFile.class);
        mActivity = mController.create().get();
        mActivity.getSupportFragmentManager().beginTransaction().replace(R.id.fl_left_container, new SearchFragment()).commit();
        mController.start().resume();
        mFragment = (SearchFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
    }

    @After
    public void tearDown() {
        mFragment = null;
        mActivity = null;
        mController = null;
    }

    @Test
    @Ignore
    public void should_init_when_onViewCreated() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_left_container), null);
        mFragment.onViewCreated(null);
        assertNotNull(Whitebox.getInternalState(mFragment, "mItemAdapter"));
        assertNotNull(Whitebox.getInternalState(mFragment, "searchAnim"));
    }

    @Test
    @Ignore
    public void should_response_when_onClick() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_left_container), null);
        mFragment.onViewCreated(null);
        mActivity.findViewById(R.id.background_mask_container).performClick();
        List<Fragment> fragments = mActivity.getSupportFragmentManager().getFragments();
        assertEquals(0, fragments.size());
    }

    @Test
    @Ignore
    public void should_correct_when_onQueryTextChange_different_inputs() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_left_container), null);
        mFragment.onViewCreated(null);
        SearchHeaderAdapter mHeaderAdapter = (SearchHeaderAdapter) Whitebox.getInternalState(mFragment, "mHeaderAdapter");

        boolean result = mFragment.onQueryTextChange("");
        assertFalse(result);
        assertEquals(0, mHeaderAdapter.getTotalCount());
        assertTrue(Whitebox.getInternalState(mFragment, "isQueryClear"));

        mHeaderAdapter.setTotalCount(100);
        result = mFragment.onQueryTextChange("newText");
        assertFalse(result);
        assertFalse(Whitebox.getInternalState(mFragment, "isQueryClear"));
        assertNotEquals(0, mHeaderAdapter.getTotalCount());
    }



}
