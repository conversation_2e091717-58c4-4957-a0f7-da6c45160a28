/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordItemDragDelegateTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/21
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/21 1.0 create
 */

package com.soundrecorder.browsefile.home

import android.content.Context
import android.os.Build
import android.view.DragEvent
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.databean.Record
import java.util.concurrent.ConcurrentHashMap
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.spy
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecordItemDragDelegateTest {
    private var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        context = null
    }

    @Test
    fun should_correct_when_setViewAlpha() {
        val view = spy(View::class.java)
        RecordItemDragDelegate.setViewAlpha(view)
    }

    @Test
    fun should_correct_when_tryStartDrag() {
        val context = context ?: return
        val view = View(context)
        val taskId = 100
        val delegate = RecordItemDragDelegate(context, taskId)
        // liveEditMode is not true
        delegate.tryStartDrag(view, true)

        // liveSelectedMap is null
        ItemBrowseRecordViewModel.liveEditMode[taskId] = MutableLiveData(true)
        delegate.tryStartDrag(view, false)

        val map = ConcurrentHashMap<Long, Record>().apply {
            put(1, Record())
        }
        ItemBrowseRecordViewModel.liveSelectedMap[taskId] = MutableLiveData<ConcurrentHashMap<Long, Record>>(map)
        delegate.tryStartDrag(view, false)
        val dragEvent = Mockito.mock(DragEvent::class.java)
        Mockito.`when`(dragEvent.action).thenReturn(DragEvent.ACTION_DRAG_ENDED)
        Mockito.`when`(dragEvent.result).thenReturn(false, true)
        view.dispatchDragEvent(dragEvent)
        view.dispatchDragEvent(dragEvent)
    }

    @Test
    fun should_correct_when_setReceiveDragListener() {
        val context = context ?: return
        val view = View(context)
        val delegate = RecordItemDragDelegate(context, 1)
        delegate.setReceiveDragListener(null)
        delegate.setReceiveDragListener(view)

        val dragEvent = Mockito.mock(DragEvent::class.java)
        Mockito.`when`(dragEvent.action).thenReturn(DragEvent.ACTION_DROP)
        Mockito.`when`(dragEvent.localState).thenReturn("")
        view.dispatchDragEvent(dragEvent)
        Assert.assertNull(Whitebox.getInternalState(delegate, "recordDropHash"))

        Mockito.`when`(dragEvent.localState).thenReturn("drag_from_recorder")
        view.dispatchDragEvent(dragEvent)
        Assert.assertNotNull(Whitebox.getInternalState(delegate, "recordDropHash"))
    }

    @Test
    fun should_correct_when_checkIsDraggingState() {
        val taskId = 99
        val delegate = RecordItemDragDelegate(context, taskId)
        Assert.assertFalse(delegate.checkIsDraggingWhenRefreshData())

        ItemBrowseRecordViewModel.liveDragMode[taskId] = MutableLiveData(true)
        Assert.assertTrue(delegate.checkIsDraggingWhenRefreshData())
    }
}