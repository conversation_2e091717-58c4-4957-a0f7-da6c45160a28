/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SegmentSeekBar
 * Description:
 * Version: 1.0
 * Date: 2025/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/18 1.0 create
 */

package com.soundrecorder.browsefile.home.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.common.widget.seekbar.COUISeekBarOS15

open class ItemBrowsePlayCustomSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : COUISeekBarOS15(context, attrs, defStyleAttr, defStyleRes) {
    companion object {
        const val DEFAULT_BORDER_SIZE = 1f
    }
    private val logTag = "ItemBrowsePlayCustomSeekBar"
    private var thumbBordersPaint: Paint? = null
    private var thumbBordersSize: Float? = DEFAULT_BORDER_SIZE
    private var thumbBorderColor: Int? = null

    init {
        thumbBordersPaint = Paint().apply {
            isAntiAlias = true
            style = Paint.Style.STROKE
        }
        val typedArray = context.obtainStyledAttributes(
            attrs,
            com.soundrecorder.common.R.styleable.ItemBrowsePlayCustomSeekBar,
            defStyleAttr,
            0
        )
        typedArray.let {
            thumbBordersSize = it.getDimension(com.soundrecorder.common.R.styleable.ItemBrowsePlayCustomSeekBar_thumbBordersSize, DEFAULT_BORDER_SIZE)
            thumbBorderColor = it.getColor(
                com.soundrecorder.common.R.styleable.ItemBrowsePlayCustomSeekBar_thumbBordersColor,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            )
            typedArray.recycle()
        }
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawThumbBorder(canvas)
    }


    private fun drawThumbBorder(canvas: Canvas) {
        if (this.mShowThumb) {
            thumbBordersPaint?.let {
                it.strokeWidth = this.thumbBordersSize ?: DEFAULT_BORDER_SIZE
                it.color = thumbBorderColor ?: COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
                val centerY = this.seekBarCenterY
                val thumbInnerAreaLeft = this.mThumbPosition - this.mCurThumbRadius
                val thumbInnerAreaRight = this.mThumbPosition + this.mCurThumbRadius

                canvas.drawRoundRect(
                    thumbInnerAreaLeft, centerY.toFloat() - this.mCurThumbRadius,
                    thumbInnerAreaRight, centerY.toFloat() + this.mCurThumbRadius,
                    this.mCurThumbRadius,
                    this.mCurThumbRadius,
                    it
                )
            }
        }
    }
}