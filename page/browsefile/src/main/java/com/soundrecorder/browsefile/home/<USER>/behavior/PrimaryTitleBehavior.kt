package com.soundrecorder.browsefile.home.view.behavior

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.TextView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.doOnDetach
import androidx.core.view.isVisible
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.toolbar.COUIToolbar
import com.facebook.rebound.SimpleSpringListener
import com.facebook.rebound.Spring
import com.facebook.rebound.SpringSystem
import com.google.android.material.appbar.AppBarLayout
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.browsefile.home.view.SubTitleLayout
import com.soundrecorder.common.utils.DensityHelper
import com.soundrecorder.common.utils.ViewUtils
import kotlin.math.roundToInt

class PrimaryTitleBehavior(private val context: Context, attrs: AttributeSet? = null) : CoordinatorLayout.Behavior<AppBarLayout>(context, attrs) {

    var isEditMode = false
    var isSearch = false
    var isDoTopMarginAnim = false
    var scrollDistance = 1
    var mAppBarLayoutHeight = 0
    var fragmentIsHidden = false // 所在fragment的hidden状态
    private var scrollStartY = 0
    private var mSubTitleInitHeight = 0
    private var mScrollView: COUIRecyclerView? = null
    private var mDividerLine: View? = null
    private var mAppBarLayout: AppBarLayout? = null
    private var mToolbar: COUIToolbar? = null
    private var mContent: LinearLayout? = null
    private var mPrimaryTitle: TextView? = null
    private var mSubtitle: SubTitleLayout? = null
    private var mMainTitle: View? = null

    private var mPrimaryTitleTextSize = 0F
    private var mPrimaryTitleTextSizeRange = 0F
    private var mPrimaryTitleMinTextSize = 0F
    private var mPrimaryTitleHeight = 0
    private var mPrimaryTitleMinHeight = 0
    private var mPrimaryTitleWidthDiff = 0
    private var mPrimaryTitleInitWidth = 0

    private var mSubTitleMarginBottom = 0
    private var mSubTitleMarginBottomRange = 0
    private var mSubTitleHeight = 0
    private var mSubTitleScrollMarginBottom = 0

    private var mDividerLineMargin = 0
    private var mDividerAlphaChangeOffset = 0
    private var mTitleColor = 0
    private var mTitleContentMarginTop = 0f
    private var mEditModeMargin = 0

    // spring
    private val mSpringSystem = SpringSystem.create()
    private val mSpring: Spring = mSpringSystem.createSpring()
    private var mReboundListener: ReboundListener? = null

    // Location for spring scrolling
    private var mTempLocationY = 0

    private var systemBarInsetsTop = 0

    //手机横屏下会用到这个值
    private var isCanScroll = true

    private var mRatio = 0F
    private var locationYInWindowWithOffset = 0
    // 底部录制面板、面板上方分割线
    private var bottomDivider: View? = null
    private var bottomRecordPanel: View? = null

    private var isFirstLoadData = true

    inner class ReboundListener : SimpleSpringListener() {
        override fun onSpringUpdate(spring: Spring) {
            if (mTempLocationY != mSpring.endValue.toInt()) {
                mScrollView?.scrollBy(0, (spring.currentValue - mTempLocationY).toInt())
            } else {
                mSpring.setAtRest()
            }
            mTempLocationY = mSpring.currentValue.toInt()
        }
    }

    companion object {
        private const val TAG = "PrimaryTitleBehavior"
        const val ALPHA_OPAQUE = 255
        const val PERCENT_0 = 0f
        const val PERCENT_50 = 0.5f
        const val PERCENT_100 = 1f
        const val FONT_VARIATION_DEFAULT = 500
        const val FONT_VARIATION_RANGE = 400
        const val FONT_VARIATION_SCALE = 50
        const val TRANSITION_RATIO = -1F
        const val EXPAND_RATIO = 0F

        // mAppBarLayout 下面
        const val LOCATION_STATE_BELOW = 1

        //mAppBarLayout 上面(滑出屏幕状态)
        const val LOCATION_STATE_ABOVE = 2

        //处于两者之间
        const val LOCATION_STATE_BOTH = 3
    }

    init {
        val res = context.resources
        // this param from support demo
        mPrimaryTitleMinTextSize = res.getDimension(R.dimen.toolbar_title_final_text_size)
        mPrimaryTitleMinHeight = res.getDimensionPixelOffset(R.dimen.toolbar_title_final_height)
        mPrimaryTitleWidthDiff = res.getDimensionPixelOffset(R.dimen.toolbar_title_width_diff)
        mSubTitleMarginBottom = res.getDimensionPixelOffset(R.dimen.sub_title_margin_bottom)
        mSubTitleScrollMarginBottom = res.getDimensionPixelOffset(R.dimen.sub_title_scroll_margin_bottom)
        mSubTitleMarginBottomRange = mSubTitleMarginBottom - mSubTitleScrollMarginBottom
        mTitleColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimaryNeutral, 0)
        mTitleContentMarginTop = res.getDimension(R.dimen.title_content_margin_top)
        mEditModeMargin = res.getDimensionPixelOffset(R.dimen.toolbar_title_edit_mode_margin)

        mDividerLineMargin = res.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.common_margin)
        mDividerAlphaChangeOffset = res.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp10)
    }

    override fun onStartNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        child: AppBarLayout,
        directTargetChild: View,
        target: View,
        axes: Int,
        type: Int
    ): Boolean {

        if (target !is COUIRecyclerView) {
            return false
        }
        ensureBehaviorView(coordinatorLayout, child, target)
        return false
    }

    fun ensureBehaviorView(coordinatorLayout: CoordinatorLayout, child: AppBarLayout, target: View?) {
        initView(coordinatorLayout, child, target)
    }

    private fun initView(coordinatorLayout: CoordinatorLayout, child: AppBarLayout, target: View?) {
        if (mScrollView == null) {
            mScrollView = target as? COUIRecyclerView
            mAppBarLayout = child
            mToolbar = mAppBarLayout?.findViewById(R.id.browse_toolbar)
            mContent = mAppBarLayout?.findViewById(R.id.content)
            mPrimaryTitle = mAppBarLayout?.findViewById(R.id.toolbar_title)
            mMainTitle = mAppBarLayout?.findViewById(R.id.main_title)
            mSubtitle = mAppBarLayout?.findViewById(R.id.toolbar_subtitle)
            mDividerLine = mAppBarLayout?.findViewById(R.id.divider_line)
            bottomDivider = coordinatorLayout.findViewById(R.id.divider_line_record_panel)
            bottomRecordPanel = coordinatorLayout.findViewById(R.id.gradientBackground)
            addListener()
        }
        initParam()
    }

    private fun checkSubTitleHeightChange() {
        val currentSubTitleHeight = mSubtitle?.measuredHeight ?: 0
        if (mSubTitleHeight != currentSubTitleHeight) {
            val offset = currentSubTitleHeight - mSubTitleHeight
            mSubTitleHeight = currentSubTitleHeight
            mAppBarLayoutHeight += offset
            scrollStartY += offset
            scrollDistance += offset
        }
    }

    private fun initParam() {
        if ((mAppBarLayoutHeight != 0) && (mPrimaryTitleHeight != 0) && (mSubTitleHeight != 0)) {
            return
        }
        mAppBarLayoutHeight = mAppBarLayout?.measuredHeight ?: 0
        mPrimaryTitleHeight = mPrimaryTitle?.measuredHeight ?: 0
        mSubTitleHeight = mSubtitle?.measuredHeight ?: 0
        scrollStartY = mAppBarLayoutHeight + systemBarInsetsTop
        scrollDistance = mContent?.measuredHeight ?: 1
        mPrimaryTitleTextSize = mPrimaryTitle?.textSize ?: 0F
        mPrimaryTitleTextSizeRange = mPrimaryTitleTextSize - mPrimaryTitleMinTextSize
        mSubTitleInitHeight = mSubTitleHeight
        DebugUtil.i(
            TAG, """
            mAppBarLayoutHeight = $mAppBarLayoutHeight
            mPrimaryTitleHeight = $mPrimaryTitleHeight
            mSubTitleHeight = $mSubTitleHeight
            mScrollStartY = $scrollStartY
            scrollDistance = $scrollDistance
            mPrimaryTitleTextSize = $mPrimaryTitleTextSize
            mPrimaryTitleTextSizeRange = $mPrimaryTitleTextSizeRange
            systemBarInsetsTop = $systemBarInsetsTop
        """.trimMargin()
        )
    }

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            if (isSearch && isDoTopMarginAnim) return
            if ((newState == RecyclerView.SCROLL_STATE_IDLE)) {
                val diff = getDiff()
                if (diff in 0..scrollDistance) {
                    val ratio: Float = diff.toFloat() / scrollDistance
                    val dy = if (ratio > PERCENT_50) {
                        scrollDistance - diff
                    } else {
                        -diff
                    }
                    DebugUtil.i(TAG, "SCROLL_STATE_IDLE diff > $diff scrollDistance > $scrollDistance dy > $dy ")
                    mSpring.apply {
                        currentValue = 0.0
                        endValue = dy.toDouble()
                    }
                }
            }
        }
    }

    private val drawListener = ViewTreeObserver.OnDrawListener {
        if (isSearch && isDoTopMarginAnim) return@OnDrawListener
        // fragment hide不执行，计算的diff是错误的，会导致可见时标题栏明显从上到下滚动闪烁效果
        if (fragmentIsHidden) return@OnDrawListener
        if (isCanScroll) {
            onListScroll(false)
        }
    }

    private fun addListener() {
        mScrollView?.let { recyclerView ->
            recyclerView.viewTreeObserver.addOnDrawListener(drawListener)
            recyclerView.addOnScrollListener(scrollListener)

            recyclerView.doOnDetach {
                recyclerView.viewTreeObserver.removeOnDrawListener(drawListener)
                recyclerView.removeOnScrollListener(scrollListener)
            }
        }
        if (mReboundListener == null) {
            mReboundListener = ReboundListener()
        }
        mSpring.addListener(mReboundListener)
    }

    fun release() {
        mSpring.removeAllListeners()
        mReboundListener = null
        mScrollView = null
        mDividerLine = null
        mAppBarLayout = null
        mToolbar = null
        mContent = null
        mPrimaryTitle = null
        mSubtitle = null
        mMainTitle = null
    }

    fun onListScroll(forceUpdate: Boolean = true) {
        if ((mAppBarLayoutHeight == 0) || (mPrimaryTitleHeight == 0) || (mSubTitleHeight == 0)) {
            return
        }
        if (fragmentIsHidden) {
            DebugUtil.d(TAG, "onListScroll return by fragment is hidden")
            return
        }
        checkSubTitleHeightChange()
        updateView(forceUpdate)
    }

    private fun getLocationYInWindow(child: View?): Int {
        val location = IntArray(2)
        if (child == null) {
            if (hasDataEmpty()) {
                location[1] = scrollStartY
            } else {
                //浮窗下，第一次有数据时，child==null，按展开计算
                if (isFirstLoadData && isFlexibleWindow(context as? Activity)) {
                    location[1] = scrollStartY
                    isFirstLoadData = false
                } else {
                    location[1] = 0
                }
            }
        } else {
            child.getLocationInWindow(location)
            location[1] = location[1] + child.height
        }
        return location[1]
    }

    private fun hasDataEmpty() = ((mScrollView?.adapter as? BrowseAdapter)?.getRealItemCount()) == 0

    private fun updateView(forceUpdate: Boolean = false) {
        val diff = getDiff(forceUpdate)
        if (scrollDistance == 0) {
            scrollDistance = mContent?.measuredHeight ?: 1
        }
        var ratio: Float = diff.toFloat() / scrollDistance
        ratio = when {
            ratio > PERCENT_100 -> PERCENT_100
            ratio < PERCENT_0 -> PERCENT_0
            else -> ratio
        }
        if (!forceUpdate && (mRatio == ratio)) {
            return
        }
        if (ratio == EXPAND_RATIO && getLocationYInWindowWithOffset() == scrollStartY) {
            mRatio = TRANSITION_RATIO
            return
        }
        //DebugUtil.i(TAG, "updateView  forceUpdate=$forceUpdate,diff==$diff,scrollDistance == $scrollDistance, ratio=$ratio")
        if (!ratio.isNaN()) {
            mRatio = ratio
            mScrollView?.post {
                if (getCanScroll()) {
                    updateView(ratio)
                }
            }
        } else {
            DebugUtil.e(TAG, "ratio nan $ratio")
        }
    }

    fun getDiff(logEnable: Boolean = true): Int {
        val offset = mSubTitleHeight - mSubTitleInitHeight
        val y: Int = getLocationYInWindow(getChild()) + offset
        locationYInWindowWithOffset = y
        if (logEnable) {
            DebugUtil.i(TAG, "getDiff  scrollStartY==$scrollStartY, y==$y, offset==$offset")
        }
        return scrollStartY - y
    }

    private fun getLocationYInWindowWithOffset(): Int {
        return locationYInWindowWithOffset
    }

    fun getDiffWithSearch(): Int {
        var y = getLocationYInWindow(getChildWithSearch())
        y -= systemBarInsetsTop
        return scrollStartY - y
    }

    fun updateSystemBarInsetsTop(top: Int) {
        systemBarInsetsTop = top
        scrollStartY = mAppBarLayoutHeight + systemBarInsetsTop
        DebugUtil.i(TAG, "updateSystemBarInsetsTop top=$top, scrollStartY=$scrollStartY")
    }

    private fun updateView(ratio: Float) {
        updateToolBarTitle(ratio)
        updatePrimaryTitle(ratio)
        updateSubTitle(ratio)
        updateTitleContent(ratio)
        updateDividerLine(ratio)
    }

    private fun updateToolBarTitle(ratio: Float) {
        val titleTextColor = if (ratio == PERCENT_100) {
            if (isEditMode) {
                mToolbar?.setTitleTextAppearance(context, com.support.toolbar.R.style.textAppearanceSecondTitle)
            } else {
                mToolbar?.setTitleTextAppearance(context, R.style.Browse_Title_Semibold)
            }
            Color.argb(ALPHA_OPAQUE, Color.red(mTitleColor), Color.green(mTitleColor), Color.blue(mTitleColor))
        } else {
            Color.argb(0, 0, 0, 0)
        }
        mToolbar?.setTitleTextColor(titleTextColor)
    }

    private fun updatePrimaryTitle(ratio: Float) {
        mPrimaryTitle?.apply {
            //ready new param
            val weight = FONT_VARIATION_DEFAULT + ((ratio * FONT_VARIATION_RANGE).toInt() / FONT_VARIATION_SCALE) * FONT_VARIATION_SCALE

            val textSize = (mPrimaryTitleMinTextSize + (1 - ratio) * mPrimaryTitleTextSizeRange).roundToInt()
            val newAlpha = if (ratio == PERCENT_100) PERCENT_0 else PERCENT_100
            val param = mPrimaryTitle?.layoutParams as? LinearLayout.LayoutParams
            val newHeight = (mPrimaryTitleHeight - (mPrimaryTitleHeight - mPrimaryTitleMinHeight) * ratio).toInt()
            mPrimaryTitleInitWidth = DensityHelper.getDefaultConfigDimension(R.dimen.title_max_width_value)
            val paddingStart = ViewUtils.dp2px(NumberConstant.NUM_F16_0).toInt()
            mEditModeMargin = ((mToolbar?.measuredWidth ?: 0) - (mPrimaryTitle?.measuredWidth ?: 0)) / NumberConstant.NUM_2 - paddingStart
            val newMarginStart = if (isEditMode) (mEditModeMargin * ratio).toInt() else 0

            if (paint?.textSize?.toInt() != textSize) {
                paint.textSize = textSize.toFloat()
                //由于textSize和marginStart均取整赋值，字体大小改变时，再一起改动marginStart，否则会出现title在水平方向上的抖动感
                param?.apply {
                    height = newHeight
                    //width = newWidth
                    marginStart = newMarginStart
                }
            }

            alpha = newAlpha
            layoutParams = param
            maxWidth = (mPrimaryTitleInitWidth - mPrimaryTitleWidthDiff * ratio).toInt()
        }
    }

    private fun updateSubTitle(ratio: Float) {
        mSubtitle?.apply {
            // ready param
            val param = layoutParams as? LinearLayout.LayoutParams
            param?.apply {
                bottomMargin = (mSubTitleMarginBottom - mSubTitleMarginBottomRange * ratio).toInt()
                topMargin = (mSubTitleHeight * (-ratio)).toInt()
            }
            val newAlpha = if (ratio < PERCENT_50) (1F - 2 * ratio) else 0F
            //set param
            layoutParams = param
            alpha = newAlpha
            mSubtitle?.updateSubTitleClickState(alpha)
        }
    }

    private fun updateTitleContent(ratio: Float) {
        mContent?.apply {
            val param = layoutParams as? AppBarLayout.LayoutParams
            param?.topMargin = (-ratio * mTitleContentMarginTop).toInt()
            layoutParams = param
        }
    }

    private fun updateDividerLine(ratio: Float) {
        mDividerLine?.apply {
            //ready param
            val param = layoutParams as? LinearLayout.LayoutParams
            val marginStartAndEnd = (mDividerLineMargin * (1 - ratio)).toInt()
            param?.apply {
                marginStart = marginStartAndEnd
                marginEnd = marginStartAndEnd
            }
            //set param
            layoutParams = param
            alpha = ratio
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun getChild(): View? {
        return try {
            // adapter 无数据，直接return null--findViewHolderForLayoutPosition从有数据到无，仍可以拿到view
            if ((mScrollView?.adapter as? BrowseAdapter)?.getRealItemCount() == 0) {
                return null
            }
            /**
             * findViewHolderForLayoutPosition ：数据从有变无，还是会拿到缓存中的 view
             * findViewHolderForAdapterPosition ：data数据改变，未渲染完，会return null
             */
            mScrollView?.findViewHolderForLayoutPosition(0)?.itemView
        } catch (e: Exception) {
            null
        }
    }

    private fun getChildWithSearch(): View? {
        runCatching {
            // adapter 无数据，直接return null--findViewHolderForLayoutPosition从有数据到无，仍可以拿到view
            if ((mScrollView?.adapter as? BrowseAdapter)?.getRealItemCount() == 0) {
                return null
            }
            /**
             * findViewHolderForLayoutPosition ：数据从有变无，还是会拿到缓存中的 view
             * findViewHolderForAdapterPosition ：data数据改变，未渲染完，会return null
             */
            return mScrollView?.findViewHolderForLayoutPosition(1)?.itemView
        }
        return null
    }

    fun expandSubTitle() {
        if (isCanScroll) {
            updateView(0F)
        }
    }

    fun unexpandSubTitle() {
        updateView(1F)
    }

    fun setCanScroll(scroll: Boolean) {
        this.isCanScroll = scroll
    }

    fun getCanScroll(): Boolean {
        return isCanScroll
    }


    /**
     * Behavior展开时旋转至横屏，做滑动操作，再旋转为竖屏，根据计算第1个item位置来判断Behavior是否展开(item数量不满一屏同样适用)
     */
    fun getFirstBrowseViewMoreThanListView(): Int {
        if ((mScrollView?.adapter as? BrowseAdapter) == null || ((mScrollView?.adapter as? BrowseAdapter)?.getRealItemCount() == 0)) {
            //列表中无数据时，返回一个展开的状态
            DebugUtil.i(TAG, "getFirstBrowseViewMoreThanListView childCount = 0")
            return LOCATION_STATE_BELOW
        }
        //获取列表中第1个数据项的位置
        val firstBrowseView = mScrollView?.layoutManager?.findViewByPosition(1)
        val firstBrowseViewLocation = IntArray(2)
        firstBrowseView?.getLocationInWindow(firstBrowseViewLocation)
        val viewY = firstBrowseViewLocation[1] - systemBarInsetsTop
        DebugUtil.i(TAG, "getFirstBrowseViewMoreThanListView viewY=$viewY," +
                "mToolbar?.height=${mToolbar?.measuredHeight},mAppBarLayoutHeight=$mAppBarLayoutHeight")

        return when {
            viewY > mAppBarLayoutHeight -> {
                //在 mAppBarLayout 下面
                LOCATION_STATE_BELOW
            }
            viewY in ((mToolbar?.measuredHeight ?: 0) + 1)..mAppBarLayoutHeight -> {
                //处于两者之间
                LOCATION_STATE_BOTH
            }
            else -> {
                // viewY<= mToolbar?.height  处于滑出屏幕状态或刚好是behavior折叠
                LOCATION_STATE_ABOVE
            }
        }
    }

    /**
     * 处理录制面板上方divider显示隐藏
     */
    private fun handleRecordPanelDividerVisible() {
        if (isEditMode) {
            // 编辑模式底部录制面板不可见，所以不涉及面板divider
            return
        }
        val bottomLine = bottomDivider ?: return
        val bottomPanel = bottomRecordPanel ?: return
        val recyclerView = mScrollView ?: return

        if (!bottomPanel.isVisible) {
            /*录制面板不显示，不涉及divider显示隐藏处理*/
            //DebugUtil.d(TAG, "bottomPanel is not visible")
            return
        }

        if (hasDataEmpty()) {
            //若无列表数据，则隐藏divider
            bottomLine.isVisible = false
            return
        }

        val totalCount = recyclerView.adapter?.itemCount ?: 0
        val lastVisiblePos =
            (recyclerView.layoutManager as LinearLayoutManager).findLastVisibleItemPosition()
        if (lastVisiblePos != (totalCount - 1)) {
            // 最后一个可见item不是最后的item，则代表没滚动到最后，
            return
        }
        val lastItem = (recyclerView.adapter as? BrowseAdapter?)?.getFooter() ?: return
        // 能拿到最后一个item，根据最后一个itemX和divider的X做比较，lastItem在面板下方，则显示divider，否则隐藏
        val location = IntArray(2)
        lastItem.getLocationInWindow(location)
        // 减去item底部space，item可见底部同divider接触才显示
        val lastItemY = location[1] - bottomLine.resources.getDimensionPixelSize(R.dimen.browse_item_space_one_side)
        bottomPanel.getLocationInWindow(location)
        val dividerY = location[1]
        bottomLine.isVisible = lastItemY > dividerY
//        DebugUtil.i(TAG, "handleRecordPanelDividerVisible lastItemY=$lastItemY, dividerY=$dividerY")
    }
}