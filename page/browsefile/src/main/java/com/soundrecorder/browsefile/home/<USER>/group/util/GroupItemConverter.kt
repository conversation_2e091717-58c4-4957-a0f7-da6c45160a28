/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: GroupItemConverter.kt
 ** Description: Convert GroupInfo to GroupItem
 ** Version: 1.0
 ** Date : 2025/02/11
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/02/11      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import com.coui.appcompat.cardlist.COUICardListHelper
import com.soundrecorder.browsefile.home.view.group.entity.GroupFactory
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem
import com.soundrecorder.common.databean.GroupInfo

internal object GroupItemConverter {
    const val TAG = "GroupItemConverter"
    val data = mutableListOf<GroupItem>()

    /**
     * <GroupInfo> 转换成 <GroupItem>
     */
    fun groupInfo2GroupItem(
        vm: GroupViewModel,
        groupInfos: List<GroupInfo>
    ): List<GroupItem> {
        return arrayListOf<GroupItem>().apply {
            groupInfos.find { it.mGroupType == GroupInfo.INT_DEFAULT_ALL }
                ?.let { convertInternal(it, COUICardListHelper.FULL, vm) }?.let { add(it) }
            add(createTitleGroupItem())
            fillCustomGroups(
                groupInfos,
                this,
                vm = vm,
                filter = { GroupFactory.isEmbedGroup(it) })

            val callGroupInfo = groupInfos.find { it.mGroupType == GroupInfo.INT_DEFAULT_CALLING }
            callGroupInfo?.let { convertInternal(it, COUICardListHelper.HEAD, vm) }?.let { add(it) }
            groupInfos.find { it.mGroupType == GroupInfo.INT_DEFAULT_COMMON }
                ?.let {
                    convertInternal(
                        it,
                        if (callGroupInfo == null) COUICardListHelper.HEAD else COUICardListHelper.MIDDLE,
                        vm
                    )
                }?.let { add(it) }
            groupInfos.find { it.mGroupType == GroupInfo.INT_DEFAULT_RECENTLY_DELETED }
                ?.let { convertInternal(it, COUICardListHelper.TAIL, vm) }?.let { add(it) }
        }
    }

    private fun createTitleGroupItem(): GroupItem {
        return GroupItem(
            groupInfo = null,
            recordingCount = 0,
            encrypted = false,
            cardType = COUICardListHelper.NONE,
            selected = false,
            checked = false,
            enableInChecked = false,
            viewType = GroupItem.VIEW_TYPE_GROUP_TITLE
        )
    }

    private fun fillCustomGroups(
        groupInfos: List<GroupInfo>,
        result: MutableList<GroupItem>,
        vm: GroupViewModel? = null,
        filter: (GroupInfo) -> Boolean
    ) {
        //自定义分组排序规则：先按mGroupSort排序，如果mGroupSort相同，则按照创建顺序，新的在前面。即mId大的在前。
        val sortedCustomGroupInfos: List<GroupInfo> =
            groupInfos.filter { it.isCustomGroup() }
                .sortedWith(compareBy<GroupInfo> { it.mGroupSort }.thenByDescending { it.mId })
        vm?.customGroupInfoList?.value = sortedCustomGroupInfos
        val first = sortedCustomGroupInfos.firstOrNull { !filter.invoke(it) }
        val last = sortedCustomGroupInfos.lastOrNull { !filter.invoke(it) }
        if (first != null && first == last) {
            result.add(convertInternal(first, COUICardListHelper.FULL, vm))
        } else {
            sortedCustomGroupInfos.forEach { groupInfo ->
                val cardType = when (groupInfo) {
                    first -> COUICardListHelper.HEAD
                    last -> COUICardListHelper.TAIL
                    else -> COUICardListHelper.MIDDLE
                }
                result.add(convertInternal(groupInfo, cardType, vm))
            }
        }
    }

    private fun convertInternal(
        groupInfo: GroupInfo,
        cardType: Int,
        vm: GroupViewModel? = null,
        curFolder: GroupInfo? = null
    ): GroupItem {
        val realCurGroup: GroupInfo? = curFolder ?: vm?.currentGroup?.value
        val count = vm?.findRichGroupCountInGroup(groupInfo) ?: 0
        val isSelected = groupInfo.mUuId == realCurGroup?.mUuId

        return GroupItem(
            groupInfo = groupInfo,
            recordingCount = count,
            encrypted = false,
            cardType = cardType,
            selected = isSelected,
            checked = false,
            enableInChecked = !GroupFactory.isDefaultGroup(groupInfo),
            viewType = GroupItem.VIEW_TYPE_NORMAL
        )
    }

    fun createChosenTransformer(
        vm: GroupViewModel,
        groupInfos: List<GroupInfo>
    ): List<GroupItem> {
        return arrayListOf<GroupItem>().apply {
            add(createTitleGroupItem())
            fillCustomGroups(groupInfos, this, vm = vm, filter = {
                GroupFactory.isEmbedGroup(it)
            })
            groupInfos.find { it.mGroupType == GroupInfo.INT_DEFAULT_COMMON }
                ?.let { convertInternal(it, COUICardListHelper.FULL, vm) }?.let { add(it) }
        }
    }
}