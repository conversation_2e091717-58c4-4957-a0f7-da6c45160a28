package com.soundrecorder.browsefile.home.dialog.navigation

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.view.MenuItem
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_PDF
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_TXT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_WORD
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_AUDIO
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_DOCUMENT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_LINK
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_SUMMARY
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.ShareStatisticsUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileoperator.CheckOperate
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialog
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialogUtil
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import com.soundrecorder.common.fileoperator.getDeleteButtonMessage
import com.soundrecorder.common.fileoperator.getDeleteMessage
import com.soundrecorder.common.fileoperator.getDeleteTitle
import com.soundrecorder.common.fileoperator.getRecoverButtonMessage
import com.soundrecorder.common.fileoperator.getRecoverTitle
import com.soundrecorder.common.fileoperator.recover.OnRecoverFileListener
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialog
import com.soundrecorder.common.fileoperator.rename.RenameFileDialog
import com.soundrecorder.common.share.ShareSupportHelper
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeCopy
import com.soundrecorder.common.share.ShareTypeDoc
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.utils.AmpFileUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.ConvertDbUtil.SHOW_SWITH_TRUE
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.convertService.ConvertServiceInterface
import com.soundrecorder.modulerouter.mark.WaveMarkInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.summary.ConversionFileAction
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.exportfile.ExportDoc
import com.soundrecorder.summary.exportfile.ExportPdf
import com.soundrecorder.summary.exportfile.ExportSummaryData
import com.soundrecorder.summary.exportfile.ExportTxt
import com.soundrecorder.summary.exportfile.SummaryContent
import com.soundrecorder.summary.ui.content.SummaryContentViewUtil.generateExportFilePath
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

class NavigationViewManager(
    var mActivity: FragmentActivity?,
    var mBottomNavigationView: COUINavigationView?
) {
    companion object {
        private const val DOC_SUFFIX = "doc"
        private const val PDF_SUFFIX = "pdf"
        private const val TXT_SUFFIX = "txt"
        private const val SHARE_TYPE_SUMMARY = 0
        private const val SHARE_TYPE_TEXT = 1
    }

    var showRecordCount = 0
    var editDisplayName: String? = null
    var onOptionCompletedListener: OnOptionCompletedListener? = null
    var onMoveGroupListener: OnMoveGroupListener? = null
    var selectedRecordList = ArrayList<Record>()
    var supportSmartName: Boolean = false
    private val TAG = "NavigationViwManager"
    private val context: Context = BaseApplication.getAppContext()
    private val mMainHandler = Handler(Looper.getMainLooper())
    private var mConcurrentHashMap: ConcurrentHashMap<Long, Record>? = null
    private val mSelectedMediaId = ArrayList<String>()
    private var mDeleteDialog: DeleteFileDialog? = null
    private var mRenameDialog: RenameFileDialog? = null
    private var mLoadingDialog: LoadingDialog? = null
    private var mRecoverDialog: RecoverFileDialog? = null

    //回收站【非编辑模式】下，点击“全部删除”按钮弹出的删除提示框
    private var mRecycleBinNonEditModeDeleteAllDialog: DeleteFileDialog? = null

    private var mCurrentGroup: GroupInfo? = null
    private var mAllRecordList = ArrayList<Record>()
    private var mCurrentDeleteDialogType = DeleteFileDialogUtil.DIALOG_TYPE_NORMAL

    private var isRestoreShareTextDialog: Boolean = false

    private var isShowPdf = false

    private val shareSupportHelper: ShareSupportHelper by lazy {
        ShareSupportHelper()
    }

    private var mRenamePopupWindow: COUIPopupListWindow? = null
    private var mRenameItemList: MutableList<PopupListItem>? = null
    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    private val convertServiceApi by lazy {
        Injector.injectFactory<ConvertServiceInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val summaryAction by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val conversionFileAction by lazy {
        Injector.injectFactory<ConversionFileAction>()
    }

    /*  private var mNormalPopupWindow: COUIPopupListWindow? = null
        private var mNormalItemList: MutableList<PopupListItem>? = null*/

    init {
        reloadSelected()
        //initNormalPopup()
        initRenamePopup()
    }

    private fun initRenamePopup() {
        mActivity?.let {
            mRenameItemList = ArrayList()
            mRenameItemList?.apply {
                val builder = PopupListItem.Builder()
                builder.setTitle(it.getString(com.soundrecorder.base.R.string.custom_name))
                    .setId(com.soundrecorder.common.R.id.set_custom_name)
                    .setGroupId(com.soundrecorder.common.R.id.group1)
                add(builder.build())

                builder.reset()
                    .setTitle(it.getString(com.soundrecorder.base.R.string.intelligent_name))
                    .setId(com.soundrecorder.common.R.id.set_smart_name)
                    .setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())
            }
            mRenamePopupWindow = COUIPopupListWindow(it).apply {
                itemList = mRenameItemList
                setOnItemClickListener { _, _, pos, _ ->
                    mRenamePopupWindow?.let {
                        val item = mRenamePopupWindow?.itemList?.get(pos)
                        if (item?.isEnable == false) {
                            return@setOnItemClickListener
                        }
                        if (it.isShowing && item?.hasSubMenu() != true) {
                            when (pos) {
                                0 -> {
                                    //自定义命名
                                    clickPopupToRename()
                                }

                                1 -> {
                                    //智能命名
                                    DebugUtil.d(TAG, "set_smart_name:")
                                    showSmartNameDialog()
                                }
                            }
                            it.dismiss()
                        }
                    }
                }
            }
        }
    }

    private fun clickPopupToRename() {
        DebugUtil.d(TAG, "clickPopupToRename")
        val eventInfo = HashMap<String, String>()
        eventInfo[RecorderUserAction.KEY_FROM] = RecorderUserAction.DEFAULT_VALUE
        showRenameDialog(selectedRecordList[0])
        RecorderUserAction.addCommonUserAction(
            context,
            RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
            RecorderUserAction.EVENT_BROWSERFILE_RENAME,
            eventInfo, false
        )
        mRenamePopupWindow?.dismiss()
    }

    private fun showSmartNameDialog() {
        if (mActivity == null) {
            DebugUtil.d(TAG, "showSmartNameDialog, activity is null")
            return
        }

        //智能标题生成 start
        if (mSelectedMediaId.isEmpty()) {
            return
        }
        resetContinueOperator()

        val mediaIdList = mutableListOf<Long>()
        mSelectedMediaId.forEach {
            mediaIdList.add(it.toLong())
        }
        onOptionCompletedListener?.onOptionSmartName(CheckOperate.OPERATE_SMART_NAME, mediaIdList)
    }

    /*
        private fun initNormalPopup() {
            mActivity?.let {
                mNormalItemList = ArrayList()
                mNormalItemList?.apply {
                    val builder = PopupListItem.Builder()
                    builder.setTitle(it.getString(com.soundrecorder.base.R.string.set_as))
                        .setId(com.soundrecorder.common.R.id.set_as_ring)
                        .setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())

                    builder.reset()
                        .setTitle(it.getString(com.soundrecorder.base.R.string.set_record_to_encrypted))
                        .setId(com.soundrecorder.common.R.id.set_as_encrypted)
                        .setGroupId(com.soundrecorder.common.R.id.group2)
                    add(builder.build())
                }

                mNormalPopupWindow = COUIPopupListWindow(it).apply {
                    itemList = mNormalItemList
                    setOnItemClickListener { _, _, pos, _ ->
                        mNormalPopupWindow?.let {
                            if (it.isShowing && mNormalPopupWindow?.itemList?.get(pos)
                                    ?.hasSubMenu() != true
                            ) {
                                when (pos) {
                                    0 -> {
                                        BuryingPoint.addClickSetRingtone(RecorderUserAction.VALUE_SET_RINGTONE_FROM_EDIT)
                                        SendSetUtil.setAs(mSelectedMediaId[0].toLong(), mActivity)
                                    }
                                    1 -> {
                                        //设置私密
                                        DebugUtil.d(TAG, "set_as_encrypted")
                                    }
                                }
                                it.dismiss()
                            }
                        }
                    }
                }
            }
        }
    */

    fun setAllRecordList(allRecordList: ArrayList<Record>?) {
        if (allRecordList != null) {
            mAllRecordList.clear()
            mAllRecordList.addAll(allRecordList)

            loadDeleteAllSelect()
        }
    }

    private fun reloadSelected() {
        val taskId = mActivity?.taskId ?: 0
        mConcurrentHashMap = ItemBrowseRecordViewModel.liveSelectedMap[taskId]?.value
        selectedRecordList.clear()
        mSelectedMediaId.clear()
        mConcurrentHashMap?.forEach {
            mSelectedMediaId.add(it.key.toString())
            selectedRecordList.add(it.value)
        }
        DebugUtil.i(TAG, "selectedRecordList size = ${selectedRecordList.size}")
    }

    fun restoreShareTextDialog(
        menuItem: MenuItem?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        childFragmentManager: FragmentManager? = null
    ) {
        isRestoreShareTextDialog = true
        click(menuItem, coroutineScope, listener, null, childFragmentManager)
    }

    @Suppress("LongMethod")
    fun click(
        menuItem: MenuItem?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        browseViewModel: BrowseViewModel?,
        childFragmentManager: FragmentManager? = null
    ) {
        reloadSelected()
        val eventInfo = HashMap<String, String>()
        eventInfo[RecorderUserAction.KEY_FROM] = RecorderUserAction.DEFAULT_VALUE
        when (menuItem?.itemId) {
            R.id.item_send -> {
                if (checkSelectIsEmpty()) return
                if (mSelectedMediaId.size > 1) {
                    if (browseViewModel?.isCanShareRecordFiles == false) {
                        DebugUtil.i(TAG, "click, Cannot share audios: already sharing")
                        return
                    }
                    browseViewModel?.isCanShareRecordFiles = false
                    FileDealUtil.sendRecordFiles(
                        mActivity,
                        mSelectedMediaId,
                        mBottomNavigationView?.findViewById(R.id.item_send)
                    ).takeIf { !it }?.also {
                        browseViewModel?.isCanShareRecordFiles = true
                        DebugUtil.w(TAG, "click, sendRecordFiles failure")
                    }
                } else {
                    coroutineScope?.launch(Dispatchers.IO) {
                        val selectedId = mSelectedMediaId[0].toLong()
                        val convertRecord = ConvertDbUtil.selectByRecordId(selectedId)
                        //判断录音文本转写是否有内容
                        val isConvertNotEmpty = convertRecord != null &&
                                !convertRecord.convertTextfilePath.isNullOrEmpty() &&
                                File(convertRecord.convertTextfilePath).length() > 0
                        withContext(Dispatchers.Main) {
                            if (isConvertNotEmpty) {
                                DebugUtil.i(TAG, "click, isConvertNotEmpty")
                                showShareDialog(
                                    selectedRecordList[0],
                                    convertRecord!!,
                                    mBottomNavigationView?.findViewById(R.id.item_send),
                                    coroutineScope,
                                    listener,
                                    childFragmentManager
                                )
                            } else {
                                FileDealUtil.sendRecordFile(
                                    mActivity,
                                    mSelectedMediaId[0].toLong(),
                                    mBottomNavigationView?.findViewById(R.id.item_send)
                                )
                            }
                        }
                    }
                }

                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    RecorderUserAction.EVENT_BROWSERFILE_SEND,
                    eventInfo, false
                )
            }

            R.id.item_move -> {
                onMoveGroupListener?.onMoveGroup(ArrayList(selectedRecordList))
                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    RecorderUserAction.EVENT_BROWSERFILE_MOVE_RECORD,
                    eventInfo, false
                )
            }

            R.id.item_rename -> {
                if (checkSelectIsEmpty()) return
                if (supportSmartName) {
                    if (mRenamePopupWindow == null) {
                        initRenamePopup()
                    }
                    mRenamePopupWindow?.resetOffset()
                    mRenamePopupWindow?.itemList?.get(0)?.isEnable = mSelectedMediaId.size <= 1
                    mRenamePopupWindow?.show(mActivity?.findViewById(R.id.item_rename))
                } else {
                    clickPopupToRename()
                }
            }

            R.id.item_delete -> {
                if (checkSelectIsEmpty()) return
                deleteOnClick()
                val eventId = if (isRecycle()) {
                    RecorderUserAction.EVENT_BROWSERFILE_RECYCLE_DELETE
                } else {
                    RecorderUserAction.EVENT_BROWSERFILE_DELETE
                }
                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    eventId,
                    eventInfo, false
                )
            }

            R.id.set_as -> {
                if (checkSelectIsEmpty()) return
                BuryingPoint.addClickSetRingtone(RecorderUserAction.VALUE_SET_RINGTONE_FROM_EDIT)
                SendSetUtil.setAs(mSelectedMediaId[0].toLong(), mActivity)
            }

            R.id.item_recycle_recover -> {
                if (checkSelectIsEmpty()) return
                showRecoverDialog()
                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    RecorderUserAction.EVENT_BROWSERFILE_RECYCLE_RECOVER,
                    eventInfo, false
                )
            }

            R.id.item_recycle_delete -> {
                DebugUtil.d(TAG, "item_recycle_delete")
                if (checkSelectIsEmpty()) return
                deleteOnClick()
                val eventId = RecorderUserAction.EVENT_BROWSERFILE_RECYCLE_DELETE
                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    eventId,
                    eventInfo, false
                )
            }

            R.id.item_delete_all -> {
                loadDeleteAllSelect()
                if (mAllRecordList.isEmpty()) {
                    DebugUtil.d(TAG, "item delete all mAllRecordList is empty")
                    return
                }

                //if (checkSelectIsEmpty()) return
                //回收站 全部删除
                showRecycleBinNonEditModeDeleteAllDialog()
                RecorderUserAction.addCommonUserAction(
                    context,
                    RecorderUserAction.USER_ACTION_BROWSEFILE_TAG,
                    RecorderUserAction.EVENT_BROWSERFILE_RECYCLE_DELETE,
                    eventInfo, false
                )
            }
            /*R.id.item_more -> {
                DebugUtil.d(TAG, "click, item_more")
                if (checkSelectIsEmpty()) return
                if (mNormalPopupWindow == null) {
                    initNormalPopup()
                }

                mNormalPopupWindow?.resetOffset()
                if (mSelectedMediaId.size > 1) {
                    mNormalPopupWindow?.itemList?.get(0)?.isEnable = false
                } else {
                    mNormalPopupWindow?.itemList?.get(0)?.isEnable = true
                }
                mNormalPopupWindow?.show(mActivity?.findViewById(R.id.item_more))
            }*/
            else -> {
                DebugUtil.e(TAG, "menu$menuItem")
            }
        }
    }

    private fun loadDeleteAllSelect() {
        if (mAllRecordList.isEmpty()) {
            DebugUtil.d(TAG, "loadDeleteAllSelect, allRecordList isEmpty.")
            return
        }
        selectedRecordList.clear()
        mSelectedMediaId.clear()
        mAllRecordList.forEach {
            mSelectedMediaId.add(it.id.toString())
            selectedRecordList.add(it)
        }
        DebugUtil.i(TAG, "loadDeleteAllSelect, selectedRecordList size = ${selectedRecordList.size}")
    }

    private fun checkSelectIsEmpty(): Boolean {
        if (mSelectedMediaId.isEmpty() || selectedRecordList.isEmpty()) {
            DebugUtil.e(TAG, "setOnNavigationItemSelectedListener list is empty ")
            return true
        }
        return false
    }

    private fun showRecoverDialog() {
        if (mActivity == null) {
            DebugUtil.e(TAG, "showRecoverDialog mActivity is null")
            return
        }
        if (isRecoverDialogShowing()) {
            DebugUtil.e(TAG, "showRecoverDialog RecoverDialog is Showing")
            return
        }
        //重置所有可能显示过的dialog操作状态，保证只记录当前业务dialog的操作状态，避免冲突
        resetContinueOperator()
        val isRecoverAll = mSelectedMediaId.size >= showRecordCount
        mRecoverDialog = mActivity?.let {
            RecoverFileDialog(
                it,
                it.getRecoverTitle(mSelectedMediaId.size, isRecoverAll),
                it.getRecoverButtonMessage(isRecoverAll)
            )
        }
        mRecoverDialog?.mOnFileRecoverListener = recoverListener
        mRecoverDialog?.showRecoverDialog(selectedRecordList, isRecoverAll)
    }

    private val recoverListener = object : OnRecoverFileListener {
        override fun onRecoverFileBefore() {
            mMainHandler.post { showProgress(com.soundrecorder.common.R.string.recovering) }
        }

        override fun onRecoverFileResult(deleteSuccess: Boolean) {
            mMainHandler.post {
//                hideProgress()
                onOptionCompletedListener?.onOptionCompleted(
                    CheckOperate.OPERATE_RECOVER,
                    mSelectedMediaId,
                    selectedRecordList
                )?.let {
                    mMainHandler.postDelayed({
                        hideProgress()
                    }, it)
                }
                resetContinueOperator()
            }
        }

        override fun provideRecoverRequestCode(): Int? {
            return BrowseFragment.REQUEST_CODE_SYS_RECOVER_AUTH
        }
    }

    fun recover() {
        mRecoverDialog?.recoverWithPermission(mActivity!!, selectedRecordList, false)
    }

    fun recoverHasPermission() {
        mRecoverDialog?.recoverWithPermission(mActivity!!, selectedRecordList, true)
    }

    private fun deleteOnClick() {
        showDeleteDialog(cloudTipManagerAction?.isCloudSwitchOn() == true)
    }

    private fun showProgress(titleResId: Int) {
        if ((mLoadingDialog == null) && (mActivity != null)) {
            mLoadingDialog = LoadingDialog(mActivity)
        }
        //修复loading dialog只显示一次的问题
        if (mLoadingDialog?.isActivityNull() == true) {
            mLoadingDialog?.resetActivity(mActivity)
        }
        mLoadingDialog?.show(titleResId, false)
    }

    private fun hideProgress() {
        mLoadingDialog?.dismiss()
    }

    fun showMovingGroupProgress() {
        mMainHandler.post {
            showProgress(com.soundrecorder.common.R.string.waiting)
        }
    }

    fun dismissMovingGroupProcess() {
        mMainHandler.post {
            hideProgress()
        }
    }


    private fun showDeleteDialog(isCloudOn: Boolean) {
        if (mActivity == null) {
            DebugUtil.e(TAG, "showDeleteDialog mActivity is null")
            return
        }
        if (isDeleteDialogShowing()) {
            DebugUtil.e(TAG, "showDeleteDialog DeleteDialog is Showing")
            return
        }
        //重置所有可能显示过的dialog操作状态，保证只记录当前业务dialog的操作状态，避免冲突
        resetContinueOperator()
        val isDeleteAll = mSelectedMediaId.size >= showRecordCount
        mDeleteDialog = mActivity?.let {
            DeleteFileDialog(
                it,
                it.getDeleteTitle(mSelectedMediaId.size, isDeleteAll),
                it.getDeleteMessage(mSelectedMediaId.size, isCloudOn, isDeleteAll, isRecycle()),
                it.getDeleteButtonMessage(isDeleteAll)
            )
        }
        mDeleteDialog?.mOnFileDeleteListener = getDeleteListener(isDeleteAll)
        mDeleteDialog?.showDeleteDialog(selectedRecordList, isDeleteAll, isRecycle())
        mCurrentDeleteDialogType = DeleteFileDialogUtil.DIALOG_TYPE_NORMAL
    }

    private fun showRecycleBinNonEditModeDeleteAllDialog() {
        if (mActivity == null) {
            DebugUtil.e(TAG, "showDeleteDialog mActivity is null")
            return
        }
        if (isDeleteAllDialogShowing()) {
            DebugUtil.e(TAG, "showDeleteDialog DeleteAllDialog is Showing")
            return
        }
        //重置所有可能显示过的dialog操作状态，保证只记录当前业务dialog的操作状态，避免冲突
        resetContinueOperator()
        mRecycleBinNonEditModeDeleteAllDialog = mActivity?.let {
            DeleteFileDialog(
                it,
                it.getDeleteTitle(mAllRecordList.size, true),
                it.getDeleteMessage(mAllRecordList.size, false, true, true),
                it.getDeleteButtonMessage(true)
            )
        }
        mRecycleBinNonEditModeDeleteAllDialog?.mOnFileDeleteListener = getDeleteListener(true)
        mRecycleBinNonEditModeDeleteAllDialog?.showDeleteDialog(mAllRecordList, true, true)
        mCurrentDeleteDialogType = DeleteFileDialogUtil.DIALOG_TYPE_RECYCLE_DELETE_ALL_NONE_EDIT
    }

    private fun isRecycle(): Boolean {
        return if (mCurrentGroup?.isRecentlyDeleteGroup() == true) {
            true
        } else {
            false
        }
    }

    fun delete(isDeleteAll: Boolean) {
        if (isDeleteAll && mCurrentDeleteDialogType == DeleteFileDialogUtil.DIALOG_TYPE_RECYCLE_DELETE_ALL_NONE_EDIT) {
            //不能仅根据isDeleteAll来确定用mDeleteAllDialog，再录音列表选中所有记录时，会调用mDeleteDialog，此时mDeleteAllDialog为null
            mRecycleBinNonEditModeDeleteAllDialog?.deleteWithPermission(mActivity!!, selectedRecordList, false, true)
        } else {
            mDeleteDialog?.deleteWithPermission(mActivity!!, selectedRecordList, false, isRecycle())
        }
    }

    fun getDeleteAllDialog(): DeleteFileDialog? {
        return mRecycleBinNonEditModeDeleteAllDialog
    }

    fun deleteHasPermission(isDeleteAll: Boolean) {
        if (isDeleteAll && mCurrentDeleteDialogType == DeleteFileDialogUtil.DIALOG_TYPE_RECYCLE_DELETE_ALL_NONE_EDIT) {
            //不能仅根据isDeleteAll来确定用mDeleteAllDialog，再录音列表选中所有记录时，会调用mDeleteDialog，此时mDeleteAllDialog为null
            mRecycleBinNonEditModeDeleteAllDialog?.deleteWithPermission(mActivity!!, selectedRecordList, true, true)
        } else {
            mDeleteDialog?.deleteWithPermission(mActivity!!, selectedRecordList, true, isRecycle())
        }
    }

    fun getDeleteListener(isDeleteAll: Boolean = false): OnFileDeleteListener {
        return object : OnFileDeleteListener {
            override fun onDeleteFileBefore() {
                mMainHandler.post { showProgress(com.soundrecorder.common.R.string.deleting) }
            }

            override fun onDeleteFileResult(deleteSuccess: Boolean) {
                mMainHandler.post {
//                    hideProgress()
                    onOptionCompletedListener?.onOptionCompleted(
                        CheckOperate.OPERATE_DELETE,
                        mSelectedMediaId,
                        selectedRecordList,
                        isDeleteAll
                    )?.let {
                        mMainHandler.postDelayed({
                            FileDealUtil.mDeleteRecordsByGroupDeleted = false
                            hideProgress()
                        }, it)
                    }
                    resetContinueOperator()
                }
            }

            override fun provideDeleteRequestCode(): Int {
                if (isDeleteAll) {
                    return BrowseFragment.REQUEST_CODE_SYS_DELETE_ALL_AUTH
                } else {
                    return BrowseFragment.REQUEST_CODE_SYS_DELETE_AUTH
                }
            }

            override fun provideLockScreenDeleteRequestCode(): Int {
                return if (isDeleteAll) {
                    DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RECYCLE_DELETE_ALL_NONE_EDIT
                } else {
                    DeleteFileDialogUtil.REQUEST_LOCK_SCREEN_RESULT_SUCCESS
                }
            }
        }
    }

    private fun showRenameDialog(mediaRecord: Record) {
        val content = if (editDisplayName.isNullOrEmpty()) {
            mediaRecord.displayName.title()
        } else {
            editDisplayName.title()
        }

        if (content.isNullOrEmpty()) {
            return
        }
        //重置所有可能显示过的dialog操作状态，保证只记录当前业务dialog的操作状态，避免冲突
        resetContinueOperator()
        mActivity?.let {
            mRenameDialog = RenameFileDialog(
                it,
                RenameFileDialog.FROM_NAVIGATION,
                content,
                object : PositiveCallback {
                    override fun callback(displayName: String?, path: String?) {
                        DebugUtil.i(TAG, "rename callback displayName: $displayName, path: $path")
                        onOptionCompletedListener?.onOptionCompleted(CheckOperate.OPERATE_RENAME)
                        seedingApi?.sendRecordFileInnerRename(mediaRecord.id)
                    }
                }).apply {
                this.mediaRecord = mediaRecord
                this.requestCode = BrowseFragment.REQUEST_CODE_SYS_RENAME_AUTH
                this.show()
            }
        }
    }

    interface OnOptionCompletedListener {
        fun onOptionCompleted(
            option: Int,
            selectedMediaIdList: ArrayList<String>? = null,
            deleteRecordList: ArrayList<Record>? = null,
            isDeleteAll: Boolean = false
        ): Long

        fun onOptionSmartName(option: Int, selectedMediaIdList: MutableList<Long>?) {
        }
    }

    fun release() {
        mActivity = null
        releaseRenameDialog()
        selectedRecordList.clear()
        mSelectedMediaId.clear()
        dismissDeleteDialog()
        dismissRecoverDialog()
        dismissDeleteAllDialog()
        mDeleteDialog = null
        mRecoverDialog = null
        mRecycleBinNonEditModeDeleteAllDialog = null
        onOptionCompletedListener = null
        mAllRecordList.clear()
        shareSupportHelper.release()
        releasePopupWindow()
    }

    fun releasePopupWindow() {
        if (mRenamePopupWindow?.isShowing == true) {
            mRenamePopupWindow?.dismiss()
        }
        mRenamePopupWindow = null
    }

    fun releaseRenameDialog() {
        mRenameDialog?.dismiss()
        mRenameDialog = null
    }

    fun showRenameDialog() {
        if (selectedRecordList.size > 0) {
            showRenameDialog(selectedRecordList[0])
        }
    }

    fun dismissDeleteDialog() {
        mDeleteDialog?.release()
        hideProgress()
    }

    fun dismissRecoverDialog() {
        mRecoverDialog?.release()
        hideProgress()
    }

    fun dismissDeleteAllDialog() {
        mRecycleBinNonEditModeDeleteAllDialog?.release()
        hideProgress()
    }

    fun isDeleteAllDialogShowing(): Boolean = mRecycleBinNonEditModeDeleteAllDialog?.isShowing() ?: false

    fun isRecoverDialogShowing(): Boolean = mRecoverDialog?.isShowing() ?: false

    fun isDeleteDialogShowing(): Boolean = mDeleteDialog?.isShowing() ?: false

    fun isRenameDialogShowing(): Boolean = mRenameDialog?.isShowing() ?: false

    fun isShareDialogShowing(): Boolean = shareSupportHelper.getExportDialogIsShowing()

    fun isShareTextDialogShowing(): Boolean = shareSupportHelper.getExportTextDialogIsShowing()

    fun isClickDeleteAll(): Boolean {
        return mRecycleBinNonEditModeDeleteAllDialog?.getOperating() ?: false
    }

    fun isClickRecover(): Boolean {
        return mRecoverDialog?.getOperating() ?: false
    }

    fun isClickDelete(): Boolean {
        return mDeleteDialog?.getOperating() ?: false
    }

    fun isClickRename(): Boolean = mRenameDialog?.getOperating() ?: false

    fun resetContinueOperator() {
        DebugUtil.i(TAG, " resetContinueOperator")
        mRenameDialog?.resetOperating()
        mDeleteDialog?.resetOperating()
        mRecoverDialog?.resetOperating()
        mRecycleBinNonEditModeDeleteAllDialog?.resetOperating()
    }

    fun getRenameContent(): String {
        return mRenameDialog?.getRenameContent() ?: ""
    }

    fun editDisplayName(): String? {
        var editDisplayName: String? = null
        if (selectedRecordList.size > 0) {
            val editContent = mRenameDialog?.getNewContent()
            if (editContent != null) {
                editDisplayName = editContent + selectedRecordList[0].displayName.suffix()
            }
        }
        return editDisplayName
    }

    fun setGroupInfo(groupInfo: GroupInfo) {
        this.mCurrentGroup = groupInfo
    }

    fun getGroupInfo(): GroupInfo? {
        return mCurrentGroup
    }

    interface OnMoveGroupListener {
        fun onMoveGroup(selectedRecordList: ArrayList<Record>)
    }

    private fun showShareDialog(
        record: Record,
        convertRecord: ConvertRecord,
        anchor: View?,
        coroutineScope: CoroutineScope,
        listener: IShareListener?,
        childFragmentManager: FragmentManager? = null
    ) {

        val act = mActivity ?: return
        coroutineScope.launch(Dispatchers.IO) {
            val summaryEntity = summaryAction?.getSummaryContent(act, convertRecord.recordId) ?: return@launch
            val summaryContent = summaryEntity.first
            withContext(Dispatchers.Main) {
                val hasSummary = summaryContent.isNotEmpty()
                shareSupportHelper.showShareDialog(act, anchor, null, childFragmentManager, hasSummary) { type ->
                    when (type) {
                        ShareSupportHelper.SHARE_TYPE_LINK -> {
                            ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_LINK_SHARE)
                            shareLink(act, record, convertRecord, listener, coroutineScope)
                            AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(), VALUE_SHARE_LINK, "")
                        }

                        ShareSupportHelper.SHARE_TYPE_AUDIO -> {
                            ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_AUDIO_SHARE)
                            FileDealUtil.sendRecordFile(mActivity, record.id, anchor)
                            AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(), VALUE_SHARE_AUDIO, "")
                        }

                        ShareSupportHelper.SHARE_TYPE_TEXT -> {
                            ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_TEXT_SHARE)
                            isShowPdf = true
                            showExportTextDialogTow(childFragmentManager, record, convertRecord, anchor, coroutineScope, listener)
                        }

                        ShareSupportHelper.SHARE_TYPE_SUMMARY -> {
                            //添加埋点
                            isShowPdf = false
                            showExportSummaryDialog(childFragmentManager, record, convertRecord, anchor, coroutineScope, listener)
                        }
                    }
                }
            }
        }
    }

    private fun showExportTextDialogTow(
        childFragmentManager: FragmentManager?,
        record: Record,
        convertRecord: ConvertRecord,
        anchor: View?,
        coroutineScope: CoroutineScope,
        listener: IShareListener?
    ) {
        val act = mActivity ?: return
        shareSupportHelper.showShareSummaryDialog(isShowPdf, act, childFragmentManager) { type ->
            when (type) {
                //转文本时不需要传 summaryFilePath 和fileType 的参数 所以为null
                ShareSupportHelper.SHARE_TYPE_ONLY_TEXT -> {
                    startWithSharedTextActivity(act, record, convertRecord, SHARE_TYPE_TEXT, "", "")
                    AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(), VALUE_SHARE_DOCUMENT, VALUE_FORMAT_TXT)
                }
                ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC -> {
                    shareToDoc(act, record, convertRecord, coroutineScope, listener)
                    AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(), VALUE_SHARE_DOCUMENT, VALUE_FORMAT_WORD)
                }
            }
        }
    }

    private fun showExportSummaryDialog(
        childFragmentManager: FragmentManager? = null,
        record: Record,
        convertRecord: ConvertRecord,
        anchor: View?,
        coroutineScope: CoroutineScope,
        listener: IShareListener?
    ) {
        val act = mActivity ?: return
        shareSupportHelper.showShareSummaryDialog(isShowPdf, act, childFragmentManager) { type ->
            when (type) {
                ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC -> {
                    AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(),
                        VALUE_SHARE_SUMMARY, VALUE_FORMAT_WORD
                    )
                    startSaveFile(record, convertRecord, SHARE_TYPE_SUMMARY, DOC_SUFFIX, coroutineScope)
                }
                ShareSupportHelper.SHARE_TYPE_ONLY_TEXT -> {
                    AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(),
                        VALUE_SHARE_SUMMARY, VALUE_FORMAT_TXT
                    )
                    startSaveFile(record, convertRecord, SHARE_TYPE_SUMMARY, TXT_SUFFIX, coroutineScope)
                }
                ShareSupportHelper.SHARE_TYPE_TEXT_TO_PDF -> {
                    AISummaryBuryingUtil.addShareSummaryEvent(convertRecord.recordId.toString(),
                        VALUE_SHARE_SUMMARY, VALUE_FORMAT_PDF
                    )
                    startSaveFile(record, convertRecord, SHARE_TYPE_SUMMARY, PDF_SUFFIX, coroutineScope)
                }
            }
        }
    }

    private fun generateExportTitle(title: String, recordTitle: String): String {
        val name = recordTitle.title()
        if (name?.isNotEmpty() == true) {
            return name
        }
        if (title.isNotEmpty()) {
            return title
        }
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return context.getString(com.soundrecorder.common.R.string.summary) + "$timestamp"
    }

    private fun startSaveFile(
        record: Record,
        convertRecord: ConvertRecord,
        shareType: Int,
        fileType: String,
        coroutineScope: CoroutineScope
    ) {
        val act = mActivity ?: return
        coroutineScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val summaryEntity = summaryAction?.getSummaryContent(act, convertRecord.recordId) ?: return@launch
                val summaryContent = summaryEntity.first
                val summaryTitle = generateExportTitle(summaryEntity.second, record.displayName)
                DebugUtil.i(TAG, "startSaveFile summaryTitle = $summaryTitle")
                val exportFilePath = generateExportFilePath(act, fileType, summaryTitle)
                when (fileType) {
                    TXT_SUFFIX -> ExportTxt.saveReport(exportFilePath, summaryContent)
                    DOC_SUFFIX -> {
                        ExportDoc.saveToWord(
                            BaseApplication.getAppContext(), exportFilePath, ExportSummaryData(
                                summaryTitle, SummaryContent(summaryContent, listOf()), listOf()))
                    }
                    PDF_SUFFIX -> {
                        ExportPdf.saveToPdf(
                            BaseApplication.getAppContext(), exportFilePath, ExportSummaryData(
                                summaryTitle, SummaryContent(summaryContent, listOf()), listOf())
                        )
                    }
                    else -> DebugUtil.d(TAG, "startSaveFile other")
                }
                withContext(Dispatchers.Main) {
                    startWithSharedTextActivity(act, record, convertRecord, shareType, TXT_SUFFIX, exportFilePath)
                }
            }.onFailure {
                DebugUtil.e(TAG, "startSaveFile", it)
            }
        }
    }

    fun dismissShareDialog() {
        shareSupportHelper.dismissShareDialog()
    }

    private fun shareLink(
        activity: Activity,
        record: Record,
        convertRecord: ConvertRecord,
        listener: IShareListener?,
        coroutineScope: CoroutineScope
    ) {
        coroutineScope.launch(Dispatchers.Main) {
            if (NetworkUtils.isNetworkInvalid(activity)) {
                ToastManager.showShortToast(activity, com.soundrecorder.common.R.string.no_network_unable_generate_share_link)
                return@launch
            }
            if (AmpFileUtil.isFileSizeLargerThanOrEqualToLimit(record.fileSize, AmpFileUtil.MAX_UPLOAD_AUDIO_FILE_SIZE_MB)) {
                val fileOverLimitString = context.resources.getString(
                    com.soundrecorder.common.R.string.files_over_200M_unable_generate_share_link,
                    AmpFileUtil.MAX_UPLOAD_AUDIO_FILE_SIZE_MB
                )
                ToastManager.showShortToast(activity, fileOverLimitString)
                return@launch
            }
            if (false == shareAction?.canUploadMoreAudioFiles()) {
                ToastManager.showShortToast(activity, com.soundrecorder.common.R.string.uploading_last_audio_retry)
                return@launch
            }
            val shareTextContent = getShareTextData(convertRecord)
            if (shareTextContent == null) {
                DebugUtil.e(TAG, "shareLink shareTextContent is null")
                return@launch
            }
            if (shareTextContent.textContentItems.isEmpty()) {
                DebugUtil.e(TAG, "shareLink convertContentList is null or empty")
                return@launch
            }
            if (listener == null) {
                DebugUtil.e(TAG, "shareLink listener is null")
                return@launch
            }
            shareAction?.registerShareListener(listener)
            shareAction?.share(activity, shareTextContent, ShareTypeLink("", record), coroutineScope, listener)
        }
    }

    fun dismissShareTextDialog() {
        shareSupportHelper.dismissShareTextDialog()
    }

    private fun saveToClipboard(
        activity: FragmentActivity,
        record: Record,
        convertRecord: ConvertRecord,
        coroutineScope: CoroutineScope,
        listener: IShareListener?
    ) {
        coroutineScope.launch(Dispatchers.IO) {
            val shareTextContent = getShareTextData(convertRecord)
            if (shareTextContent == null) {
                DebugUtil.d(TAG, "saveToClipboard shareTextContent is null")
                return@launch
            }
            shareAction?.share(
                activity,
                shareTextContent,
                ShareTypeCopy,
                coroutineScope,
                listener
            )
        }
    }

    private fun shareToDoc(
        activity: FragmentActivity,
        record: Record,
        convertRecord: ConvertRecord,
        coroutineScope: CoroutineScope,
        listener: IShareListener?
    ) {
        if (!shareSupportHelper.isSupportWPS()) {
            shareSupportHelper.showInstallWpsGuideDialog(activity) { isAgree ->
                DebugUtil.d(TAG, "showInstallWpsGuideDialog  isAgree:$isAgree")
            }
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            val shareTextContent = getShareTextData(convertRecord)
            if (shareTextContent == null) {
                DebugUtil.d(TAG, "showExportDialog shareTextContent is null")
                return@launch
            }
            val hasImageMark = if (!FunctionOption.IS_SUPPORT_MIX_EXPORT_DOC) {
                false
            } else {
                //解析标记中是否包含图片
                loadSubSentenceMarkBean(record, shareTextContent.textContentItems)
                hasImgMark(shareTextContent.textContentItems)
            }
            if (hasImageMark) {
                withContext(Dispatchers.Main) {
                    shareSupportHelper.showDocOnlySupportTextDialog(activity) { isAgree ->
                        if (isAgree) {
                            executeShareToDoc(
                                activity, convertRecord, coroutineScope, shareTextContent, listener
                            )
                        }
                    }
                }
            } else {
                executeShareToDoc(
                    activity,
                    convertRecord,
                    coroutineScope,
                    shareTextContent,
                    listener
                )
            }
        }
    }

    private fun executeShareToDoc(
        activity: Activity,
        convertRecord: ConvertRecord,
        coroutineScope: CoroutineScope,
        shareTextContent: ShareTextContent,
        listener: IShareListener?
    ) {
        val canShowSpeakerRole = convertRecord.canShowSpeakerRole == SHOW_SWITH_TRUE
        shareAction?.share(
            activity, shareTextContent, ShareTypeDoc(canShowSpeakerRole), coroutineScope, listener
        )
    }

    private fun shareToNote(
        activity: Activity,
        record: Record,
        convertRecord: ConvertRecord,
        coroutineScope: CoroutineScope,
        listener: IShareListener?
    ) {
        coroutineScope.launch(Dispatchers.IO) {
            val shareTextContent = getShareTextData(convertRecord)
            if (shareTextContent == null) {
                DebugUtil.d(TAG, "showExportDialog shareTextContent is null")
                return@launch
            }
            val hasImageMark = if (!FunctionOption.IS_SUPPORT_SAVE_TO_NOTE_MIX) {
                false
            } else {
                //解析标记中是否包含图片
                loadSubSentenceMarkBean(record, shareTextContent.textContentItems)
                if (shareSupportHelper.isNoteSupportImg(activity)) {
                    true
                } else {
                    hasImgMark(shareTextContent.textContentItems)
                }
            }
            shareAction?.share(
                activity,
                shareTextContent,
                ShareTypeNote(hasImageMark),
                coroutineScope,
                listener
            )
        }
    }

    private fun startWithSharedTextActivity(
        activity: Activity,
        record: Record,
        convertRecord: ConvertRecord,
        shareType: Int,
        fileType: String,
        summaryFilePath: String
    ) {
        val playFileName = record.displayName
        val playFilePath = if (record.isRecycle) record.recycleFilePath else record.data
        val isShowSpeaker =
            convertRecord.speakerRoleIsShowing == ConvertDbUtil.SPEAKER_ROLE_ISSHOWING_TRUE
        val createTimeByPath = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
            .getCreateTimeByPath(record.id, playFileName.endsWith(".amr"))
        val canShowSpeaker = convertRecord.canShowSpeakerRole == SHOW_SWITH_TRUE
        val intent = Intent("com.oplus.soundrecorder.SHARE_WITH_TXT_PREVIEW")
        intent.setPackage(activity.packageName)
        intent.putExtra("mediaRecordId", record.id)
        intent.putExtra("canShowSpeaker", canShowSpeaker)
        intent.putExtra("isShowSpeaker", isShowSpeaker)
        intent.putExtra("createTime", createTimeByPath)
        intent.putExtra("playFileName", playFileName)
        intent.putExtra("playFilePath", playFilePath)
        intent.putExtra("shareType", shareType)
        intent.putExtra("fileType", fileType)
        intent.putExtra("summaryFilePath", summaryFilePath)
        activity.startActivityForResult(intent, Constants.REQUEST_CODE_SHARE_TXT)
    }

    private suspend fun getShareTextData(convertRecord: ConvertRecord): ShareTextContent? = withContext(Dispatchers.IO) {
        val context = BaseApplication.getAppContext()
        val convertFileName = getConvertFileName(context, convertRecord)
        if (convertFileName.isNullOrEmpty()) {
            DebugUtil.i(TAG, "getShareTextData covertFileName is null or empty")
            return@withContext null
        }
        val convertFile: File = File(convertRecord.convertTextfilePath)
        if (!convertFile.exists() || convertFile.length() <= 0) {
            DebugUtil.i(TAG, "getShareTextData convertFile is not exists or length <= 0")
            return@withContext null
        }
        //转文本内容大小,超过50M的时候，保存和分享需要弹窗,
        val convertFileSize = convertFile.length()
        //解析详细转写内容的方法实现在playback里，未避免重复实现，增加接口调用
        val convertContentItems: ArrayList<ConvertContentItem>? =
            if (convertRecord.isOShareFile) {
                playbackApi?.readOShareConvertContent(
                    convertRecord.convertTextfilePath,
                    convertRecord.serverPlanCode
                ) as? ArrayList<ConvertContentItem>
            } else {
                playbackApi?.readConvertContent(
                    context,
                    convertFileName,
                    convertRecord.serverPlanCode
                ) as? ArrayList<ConvertContentItem>
            }
        DebugUtil.i(TAG, "getShareTextData convertContentList:$convertContentItems")
        if (convertContentItems == null) {
            DebugUtil.i(TAG, "getShareTextData convertContentList is null")
            return@withContext null
        }
        val isShowSpeaker =
            convertRecord.speakerRoleIsShowing == ConvertDbUtil.SPEAKER_ROLE_ISSHOWING_TRUE
        ShareTextContent(
            convertRecord.recordId,
            isShowSpeaker,
            convertFileName,
            convertFileSize,
            convertContentItems
        )
    }

    private fun getConvertFileName(context: Context?, convertRecord: ConvertRecord): String? {
        var convertFilePath: String? = null
        var convertFileName: String? = null
        convertFilePath = convertRecord.convertTextfilePath
        if (!convertFilePath.isNullOrEmpty() && (context != null)) {
            //转写文件保存路径保存在ConvertServices里，因为继承了不同的build.gradle，此模块无法直接依赖转写文件保存路径保存在ConvertServices里，增加接口访问
            val convertFileDir = convertServiceApi?.getConvertSavePath(context) + File.separator
            convertFileName = convertFilePath.replace(convertFileDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName")
        return convertFileName
    }

    private fun getConvertFileSize(appContext: Context, filename: String): Long {
        var size: Long = 0
        val file: File = File(convertServiceApi?.getConvertSavePath(appContext), filename)
        if (file.exists()) {
            size = file.length()
        } else {
            DebugUtil.e(TAG, "$filename not found.")
        }
        DebugUtil.d(TAG, "getConvertFileSize: $filename size = $size")
        return size
    }

    /**
     * 是否存在图片标记
     */
    private fun hasImgMark(contents: List<ConvertContentItem>): Boolean {
        if (!FunctionOption.IS_SUPPORT_SAVE_TO_NOTE_MIX) {
            DebugUtil.d(TAG, "hasImg >> is support save to note mix")
            return false
        }
        for (convertIndex in contents.indices) {
            contents[convertIndex].mTextOrImageItems?.forEach {
                if (it is ConvertContentItem.ImageMetaData) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 查询当前录音的标记数据
     */
    private fun getMarkList(record: Record?): List<MarkDataBean>? {
        record?.let {
            val path = if (it.isRecycle) {
                it.recycleFilePath
            } else {
                it.data
            }
            DebugUtil.i(TAG, "getMarkList, mRecord?.isRecycle:${it.isRecycle}, path:$path")
            val playUri = if (it.isRecycle) {
                path?.let {
                    Uri.parse(it)
                }
            } else {
                MediaDBUtils.genUri(record.id)
            }
            if (playUri == null) {
                DebugUtil.i(TAG, "getMarkList, playUri is null")
                return null
            }
            val list = waveMarkApi?.getMergeMarkList(path, playUri, it.isRecycle) as? List<MarkDataBean>
            DebugUtil.d(TAG, "getMarkList, list:$list")
            return list
        }
        return null
    }

    /**
     * 解析标记数据
     */
    private fun loadSubSentenceMarkBean(record: Record, data: List<ConvertContentItem>?) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) {
            return
        }
        if (data.isNullOrEmpty()) {
            DebugUtil.i(TAG, "loadSubSentenceMarkBean convert data is null or empty return")
            return
        }
        val markList = getMarkList(record) ?: mutableListOf()
        val copyOnWriteArrayList = CopyOnWriteArrayList<MarkDataBean>()
        copyOnWriteArrayList.addAll(markList)
        for (convertIndex in data.indices) {
            val contentItem = data[convertIndex]
            //子句列表
            val listSubSentences = contentItem.listSubSentence
            //time 是子句开始时间
            if (!listSubSentences.isNullOrEmpty()) {
                //循环子句列表
                for (sentencesIndex in listSubSentences.indices) {
                    val subSentence = listSubSentences[sentencesIndex]
                    subSentence.onlyHasSimpleMark = false
                    val startTime = if (sentencesIndex == 0) {
                        //如果是当前item的第一个分句，时间从item的开始时间来计算
                        contentItem.startTime.toFloat()
                    } else {
                        //非第一个分句，开始时间从当前item来计算
                        subSentence.time
                    }

                    val endTime = if (sentencesIndex < listSubSentences.size - 1) {
                        listSubSentences[sentencesIndex + 1].time
                    } else {
                        if (convertIndex < data.size - 1) {
                            val nextConvert = data[convertIndex + 1]
                            nextConvert.startTime.toFloat()
                        } else {
                            /*
                                如果是最后一段  以外的标记信息都记入最后一段
                                录音尾部静音片段的标记会记录到最后一句文本上，即使最后一句文本原本没有标记
                            */
                            contentItem.endTime.toFloat()
                        }
                    }
                    // 判断第一个item的第一个分句时，需要将之前的图片标记分配在当前句的列表中
                    val needAddPictureMarkBeforeFirstSentce =
                        (convertIndex == 0) && (sentencesIndex == 0)
                    val isLastSentenceInAudioFile =
                        (convertIndex == (data.size - 1)) && (sentencesIndex == (listSubSentences.size - 1))
                    getMarkListOfTime(
                        subSentence,
                        needAddPictureMarkBeforeFirstSentce,
                        startTime,
                        endTime,
                        isLastSentenceInAudioFile,
                        copyOnWriteArrayList
                    )
                }
            }
            //第一个convertItem不需要再段落内部将第一句分句之前的ImageMark人为调整到第一句之后，非第一个ConvertItem默认执行分句调整逻辑
            val needJustFirstImage = (convertIndex != 0)
            contentItem.parceNewTextOrImageItems(needJustFirstImage)
        }
    }

    /**
     * 解析标记数据
     */
    private fun getMarkListOfTime(
        subSentence: ConvertContentItem.SubSentence,
        needAddPictureMarkBeforeFirstSentce: Boolean = false,
        startTime: Float,
        endTime: Float,
        lastSentenceInTheFile: Boolean = false,
        copyMarkList: CopyOnWriteArrayList<MarkDataBean>
    ) {
        val newMarkList: MutableList<MarkDataBean> = mutableListOf()
        val iterator: Iterator<MarkDataBean> = copyMarkList.iterator()
        while (iterator.hasNext()) {
            val markDataBean = iterator.next()
            val endTimeSmallerThanEnd = if (lastSentenceInTheFile) {
                //最后一段最后一句需要endTime需要包含，
                markDataBean.correctTime <= endTime
            } else {
                //非最后一段最后一句，endTime不包含
                markDataBean.correctTime < endTime
            }
            //起点时间>=,包含当前起点时间
            val startTimeBiggerThanStart = markDataBean.correctTime >= startTime
            if (endTimeSmallerThanEnd && startTimeBiggerThanStart) {
                //文本标记
                if (!markDataBean.fileExists() && !subSentence.onlyHasSimpleMark) {
                    subSentence.onlyHasSimpleMark = true
                }
                newMarkList.add(markDataBean)
                //减少循环
                copyMarkList.remove(markDataBean)
            } else {
                //是否需要将当前Sentence之前的图片标记放在markList列表中
                if (needAddPictureMarkBeforeFirstSentce &&
                    (markDataBean.correctTime < startTime && markDataBean.correctTime >= 0) &&
                    markDataBean.fileExists()
                ) {
                    newMarkList.add(markDataBean)
                    //减少循环
                    copyMarkList.remove(markDataBean)
                }
            }
        }
        DebugUtil.i(
            TAG,
            "getMarkListOfTime needAddPictureMarkBeforeFirstSentce: $needAddPictureMarkBeforeFirstSentce" +
                    ", lastSentenceInTheFile $lastSentenceInTheFile, startTime: $startTime  endTime: $endTime" +
                    " size: ${newMarkList.size}, inputMarkList: $copyMarkList"
        )
        subSentence.markDataBeanList = newMarkList
    }
}