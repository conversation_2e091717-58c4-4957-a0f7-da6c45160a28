/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : AbsItemView.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.item

import android.content.Context
import android.view.View
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView

abstract class AbsItemViewHolder<V : ViewDataBinding, D : BaseItemRecordViewModel>(protected val mBinding: V) :
    RecyclerView.ViewHolder(mBinding.root) {

    protected val context: Context = mBinding.root.context

    protected lateinit var mItemViewModel: D

    protected abstract fun onRootViewClick(v: View)

    protected abstract fun onRootViewLongClick(v: View): Boolean

    /**
     * isLastPosition: 是否为item最后一个
     */
    protected abstract fun bindView(isLastPosition: Boolean?)

    protected abstract fun <D> setViewModel(data: D)

    protected abstract fun observeData(owner: LifecycleOwner)

    init {
        itemView.setOnClickListener {
            onRootViewClick(it)
        }

        itemView.setOnLongClickListener {
            onRootViewLongClick(it)
        }
    }

    fun <D> onBindViewHolder(data: D, owner: LifecycleOwner, isLastPosition: Boolean? = null) {
        setViewModel(data)
        bindView(isLastPosition)
        observeData(owner)
        bindOtherInfo()
        mBinding.executePendingBindings()
    }

    protected open fun bindOtherInfo() {}

    open fun onViewRecycled() {
        itemView.handler?.removeCallbacksAndMessages(null)
    }
}