/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - SearchHandlerDataFactory.java
 * Description:
 * Version: 1.0
 * Date :  2020/9/28
 * Author: <EMAIL>
 **
 * ---------------- Revision History: ---------------------------
 *      <author>        <data>        <version >        <desc>
 *    Yongjiang,Lu      2020/9/28          1.0         build this module
 ********************************************************************/

package com.soundrecorder.browsefile.search.load.mediadb;

import java.util.ArrayList;

import com.soundrecorder.browsefile.search.load.ItemSearchViewModel;

import com.soundrecorder.base.utils.DebugUtil;

public class SearchHandlerDataFactory extends AbsSubHandlerDataFactory<AbsSubMessageBean, ArrayList<ItemSearchViewModel>> {
    private static final String TAG = "SearchHandlerDataFactory";

    @Override
    public ArrayList<ItemSearchViewModel> convertData(AbsSubMessageBean data) {
        DebugUtil.v(TAG, "convertData: start");
        if (data != null) {
            if ((data.getCursor() != null) && (data.getSearchValue() != null)) {
                ArrayList<ItemSearchViewModel> arrayList = SearchSortUtils.cursorToArrayList(data.getCursor(), data.getSearchValue());
                SearchSortUtils.doSortSearchBeans(arrayList);
                DebugUtil.v(TAG, "convertData  return");
                return arrayList;
            }
        }
        DebugUtil.v(TAG, "convertData: end");
        return null;
    }
}
