/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.entity

import com.coui.appcompat.cardlist.COUICardListHelper
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem.Companion.VIEW_TYPE_GROUP_TITLE
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem.Companion.VIEW_TYPE_NORMAL
import com.soundrecorder.common.databean.GroupInfo

/**
 * @param cardType defined in [COUICardListHelper]
 * @param viewType one of [VIEW_TYPE_NORMAL], [VIEW_TYPE_GROUP_TITLE]
 */
data class GroupItem(
    val groupInfo: GroupInfo?,
    val recordingCount: Int, // 分组中的录音数量
    var encrypted: Boolean, // 分组是否加密
    var cardType: Int, // 分组卡片背景类型
    var selected: Boolean, // 是否选中，item背景
    var checked: Boolean, // 多选状态下是否选中，checkbox状态
    var enableInChecked: Boolean, // 多选状态是否可操作
    var viewType: Int = VIEW_TYPE_NORMAL, // 列表项类型
) {
    companion object {
        const val VIEW_TYPE_NORMAL = 0
        const val VIEW_TYPE_GROUP_TITLE = 1
    }
}