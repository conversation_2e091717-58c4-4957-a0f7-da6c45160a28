/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/8/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load

data class SearchResultWrapper(
    val pageData: MutableList<ItemSearchViewModel> = mutableListOf(),
    var nextPageNo: Int? = null,
    var totalCount: Int = -1
)
