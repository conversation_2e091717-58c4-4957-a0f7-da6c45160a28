/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFileApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/9/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.api

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.home.load.ConvertingInfo
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.browsefile.parentchild.BrowsePanelController
import com.soundrecorder.browsefile.search.load.NoteSearchRepository
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.modulerouter.BrowseFileInterface

object BrowseFileApi : BrowseFileInterface {

    override fun createBrowseFileIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, BrowseFile::class.java)
        return jumpIntent
    }

    override fun isBrowseFile(context: Context): Boolean = context is BrowseFile

    override fun getBrowseFileClass(): Class<*> {
        return BrowseFile::class.java
    }

    override fun onFileUpdateSuccess(rowId: Long, newPath: String?) {
        CenterFileChangeObserver.fileUpdateChangeSuccess(rowId, newPath)
    }

    override fun hasPlayBackRecord(activity: Activity?): Boolean {
        return (activity as? BrowseFile)?.hasPlayBackRecord() ?: false
    }

    override fun getBrowseActivityViewModel(activity: AppCompatActivity?): ViewModel? {
        return activity?.let { ViewModelProvider(it)[BrowseFileActivityViewModel::class.java] }
    }

    override fun <T> getViewModelSpeakerModeController(viewModel: ViewModel?): T? {
        return (viewModel as? BrowseFileActivityViewModel)?.mSpeakerModeController as? T
    }

    override fun <T> getViewModelWindowType(viewModel: ViewModel?): LiveData<T>? {
        return (viewModel as? BrowseFileActivityViewModel)?.windowType as? LiveData<T>
    }

    override fun setViewModelWindowType(viewModel: ViewModel?, config: Configuration?) {
        (viewModel as? BrowseFileActivityViewModel)?.windowType?.value = ScreenUtil.getWindowType(configuration = config)
    }

    override fun getViewModelAnimRunning(viewModel: ViewModel?): MutableLiveData<Boolean>? {
        return (viewModel as? BrowseFileActivityViewModel)?.childAnimRunning
    }

    override fun getViewModelIsFromOther(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isFromOtherApp ?: false
    }

    override fun <T> getViewModelPlayData(viewModel: ViewModel?): LiveData<T>? {
        return (viewModel as? BrowseFileActivityViewModel)?.mCurrentPlayRecordData as? LiveData<T>
    }

    override fun getDeleteSummaryNoteIdLiveData(viewModel: ViewModel?): MutableLiveData<String>? {
        return (viewModel as? BrowseFileActivityViewModel)?.beDeleteSummaryNoteId
    }

    override fun <T> setViewModelPlayData(viewModel: ViewModel?, playData: T?) {
        (viewModel as? BrowseFileActivityViewModel)?.mCurrentPlayRecordData?.value = playData as? StartPlayModel
    }

    override fun getViewModelClickedToRecord(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.clickedToRecord ?: false
    }

    override fun clearViewModelPlayData(viewModel: ViewModel?) {
        (viewModel as? BrowseFileActivityViewModel)?.clearPlayRecordData()
    }

    override fun isSmallWindow(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isSmallWindow() ?: false
    }

    override fun onConvertSearchStateChanged(
        activity: Activity?,
        inSearch: Boolean,
        searchTextNotEmpty: Boolean,
        outSearchFun: (() -> Unit)
    ) {
        (activity as? BrowseFile)?.onConvertSearchStateChanged(
            inSearch,
            searchTextNotEmpty,
            outSearchFun
        )
    }

    override fun getParentPercentDefault(): Float {
        return BrowsePanelController.PARENT_DEFAULT_WIDTH_PERCENT
    }

    override fun getBrowseFileActivityName(): String {
        return BrowseFile::class.java.name
    }

    override fun hasFastPlayingFile(activity: Activity?): Boolean {
        return (activity as? BrowseFile)?.isFastPlaying() ?: false
    }

    override fun getBrowseFileActivityViewModel(activity: AppCompatActivity?): MutableLiveData<Boolean> {
        return activity?.let { ViewModelProvider(it) }
            ?.get(BrowseFileActivityViewModel::class.java)?.isNeedRefresh ?: MutableLiveData(true)
    }

    override fun isNoteExist(noteId: String): Boolean {
        return NoteSearchRepository.isNoteExist(noteId)
    }

    override fun <T> showGroupChooseFragment(fragment: Fragment?, record: T) {
        fragment?.apply {
            val mGroupViewModel: GroupViewModel by activityViewModels<GroupViewModel>()
            mGroupViewModel.mutableSelectRecordings.value = listOf(record as Record)
            fragment.let {
                it.activity?.supportFragmentManager?.let { fragmentManager ->
                    mGroupViewModel.showChooseGroupFragment(
                        fragmentManager
                    )
                }
            }
        }
    }

    override fun isSupportSmartName(viewModel: ViewModel?): Boolean {
        return (viewModel as? BrowseFileActivityViewModel)?.isSupportSmartName() ?: false
    }

    override fun isSmartNaming(mediaId: Long): Boolean {
        return ConvertingInfo.isSmartNaming(mediaId)
    }
}