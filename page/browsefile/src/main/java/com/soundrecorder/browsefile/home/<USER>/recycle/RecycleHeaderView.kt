/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecycleHeaderView
 * Description:
 * Version: 1.0
 * Date: 2024/10/20
 * Author: W9035969(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9035969 2024/10/20 1.0 create
 */
package com.soundrecorder.browsefile.home.view.recycle

import android.content.Context
import android.widget.FrameLayout
import android.widget.TextView
import com.soundrecorder.browsefile.R

class RecycleHeaderView(context: Context) : FrameLayout(context) {

    private var mTvRecycleTips: TextView? = null

    init {
        inflate(context, R.layout.item_recycle_header_layout, this)
        /*
                    updateLayoutParams {
                        setPadding( 0,
                            context.resources.getDimension(com.soundrecorder.common.R.dimen.dp16).toInt(),
                            0,
                            context.resources.getDimension(com.soundrecorder.common.R.dimen.dp4).toInt()
                        )
                    }
        */
        mTvRecycleTips = findViewById(R.id.tv_recycle_tips)
        mTvRecycleTips?.setText(context.getString(com.soundrecorder.common.R.string.recycle_records_keep_days))
    }

    fun setRecycleTips(text: String?) {
        mTvRecycleTips?.setText(text)
    }
}