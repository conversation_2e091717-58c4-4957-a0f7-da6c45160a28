<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/label_container"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp42"
    android:clickable="false"
    android:focusable="false"
    android:paddingBottom="@dimen/dp2"
    android:paddingStart="@dimen/dp16">

    <com.coui.appcompat.chip.COUIChip
        android:id="@+id/chip_entrance"
        style="@style/Widget.COUI.Chip.Choice"
        android:layout_width="@dimen/dp44"
        android:layout_height="@dimen/dp40"
        android:layout_marginEnd="@dimen/dp8"
        android:contentDescription="@string/record_list_desc"
        app:chipCornerRadius="@dimen/dp12"
        app:chipIcon="@drawable/group_label_default_icon"
        app:chipIconVisible="true"
        app:chipIconSize="@dimen/dp20"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:checkedDisabledChipIconTint="@color/coui_color_label_tertiary"
        app:uncheckedDisabledBackgroundColor = "@color/group_entrance_background"
        app:uncheckedBackgroundColor="@color/group_entrance_background" />

    <com.soundrecorder.browsefile.home.view.NestedScrollableHost
        android:id="@+id/list_label_container"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintStart_toEndOf="@+id/chip_entrance"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="@dimen/dp4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.soundrecorder.browsefile.home.view.HorizontalNestedRecyclerView
            android:id="@+id/rv_label_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:forceDarkAllowed="false"
            android:fadingEdgeLength="@dimen/record_group_label_rv_fading_edge_length"
            android:requiresFadingEdge="horizontal"/>
    </com.soundrecorder.browsefile.home.view.NestedScrollableHost>
</androidx.constraintlayout.widget.ConstraintLayout>